import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RouteLogger extends NavigatorObserver {
  String? previousRoute;
  String? currentRoute;

  @override
  void didPush(Route route, Route? previousRoute) {
    this.previousRoute = previousRoute?.settings.name;
    currentRoute = route.settings.name;
    debugPrint("➡️ Pushed: $currentRoute  ← from: ${this.previousRoute}");
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    currentRoute = previousRoute?.settings.name;
    debugPrint("⬅️ Popped to: $currentRoute");
  }
}
