import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'api_consts.dart';

class APIConfig {
  const APIConfig._();

  static final baseUrl = dotenv.env['API_BASE_URL'] ?? '';

  static const login =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/token';
  static const logout =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/logout';
  static const socialSignIn =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/social/token';
  static const userProfile =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/user/profile';
  static const infocard =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/statistics';
  static const refreshtoken =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/token/refresh';
  static const brokers =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/search';
  static const brokerageTopPerformers =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/brokerage/top-performers';
  static const agentTopPerformers =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/agent/top-performers';

  //DASHBOARD
  static const totalPropertyValue =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/property-value';
  static const totalSales =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/sales';
  static const totalRevenue =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/revenue';
  static const totalSubscriptionRevenue =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/subscription-revenue';
  static const totalCommissions =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/commissions';
  static const totalBrokerages =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/brokerages';
  static const totalAgents =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/agents';
  static const totalCommissionsPersonal =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/commissions/personal';

  //AGENT NETWORK
  static const agentNetwork =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/hierarchy-details';
  static const salesDetails =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales';
  static const agentsSearch =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent/search';
  static const upstreamUsers =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/hierarchy-details/';

  // SALES
  static const closingSalesDocument =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales';
  static const transactionTypes =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales/transaction-types';
  static const representingTypes =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales/representing';
  static const leadSourceTypes =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales/lead-sources';
  static const closingSalesDocumentCommission =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/commission-processing/process';

  static const inviteAgent =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent/invitation';
  static const brokerRegistration =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage';
  static const uploadFile =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/file';

  //FILTER
  static const brokerageFilterOptions =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage';
  static const agentFilterOptions =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent';
  static const propertyTypeFilter =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/property-types';
  static const userStatusFilter =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/user/status';
  static const brokerageRole =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/role';
  static const brokerageSubUser =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/subuser';

  static const registeredAgentInfo =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent';
  static const signupAgent =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent/registration';
  static const createPassword =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/password';
  static const countries =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/countries';
  static const states =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/states';
  static const cities =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/dashboard/cities';

  // Agent/brokerage/offcie staff registartion status upload
  static const agentInviteStatus =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent/';
  static const brokerageRegStatus =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/';
  static const officeStaffStatus =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/brokerage/file-upload-status';
  static const registrationStatus = '/registration-status';

  // password - forgot/reset
  static const forgotPasswordVerifyMail =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/password-reset-requests';
  static const forgotPasswordValidateToken =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/password-reset-tokens/'; //auth/password-reset-tokens/{token}/validation

  static const forgotPasswordReset =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/password-resets';
  static const resetPassword =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/password';
  static const inviteLinkValidation =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/agent/invites/'; //api/v1/agent/invites/{inviteId}/validation
  static const passwordSetupLinkValidation =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/auth/users/'; //api/v1/auth/users/{userId}/password-setup/validation
  // Commission revenue info
  static const String commissionRevenueByUser =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/commission-processing/user-commission';

  static const salesUploadStatus =
      '${APIConsts.API_PREFIX}/${APIConsts.API_VERSION_V1}/sales/'; //api/v1/sales/{salesId}/upload-status
}
