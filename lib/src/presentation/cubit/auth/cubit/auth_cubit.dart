import 'dart:async' as hydrated_bloc_overrides;
import 'dart:async' as HydratedBlocOverrides;

import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import '/src/core/config/app_strings.dart';
import '/src/data/repository/auth_data_repository.dart';
import '../../../../core/services/exceptions.dart';
import '../../../../core/services/firebase_auth_service.dart';
import '../../../../data/repository/user_repository_impl.dart';
import '../../user/user_cubit.dart';
import '/src/domain/repository/auth_repository.dart';
import '../../../../core/services/cookie_manager.dart';
import '../../../../core/utils/web_navigation_helper.dart';
part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this._authRepository) : super(AuthInitial());

  final AuthRepository _authRepository;
  late final AuthDataRepository _authDataRepository = AuthDataRepository();
  late final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  String? idToken;

  /// Helper method to store authentication tokens
  void _storeTokens(String jwt, String refreshToken) {
    _authDataRepository.setTokens(jwt, refreshToken);
  }

  /// Helper method to handle API exceptions and emit appropriate error states
  void _handleApiException<T extends AuthState>(
    Exception exception,
    String? idToken,
    T Function({
      required String error,
      required bool invalidCredentials,
      String? idToken,
      required bool userNotFound,
    })
    errorStateBuilder,
  ) {
    if (exception is ApiException) {
      debugPrint('ApiException: ${exception.message}');

      switch (exception.statusCode) {
        case 401:
          emit(
            errorStateBuilder(
              error: 'Unauthorized',
              invalidCredentials: true,
              idToken: idToken,
              userNotFound: false,
            ),
          );
          break;
        case 404:
          emit(
            errorStateBuilder(
              error: exception.toString(),
              invalidCredentials: false,
              idToken: idToken,
              userNotFound: true,
            ),
          );
          break;
        default:
          emit(
            errorStateBuilder(
              error: exception.toString(),
              invalidCredentials: false,
              idToken: idToken,
              userNotFound: false,
            ),
          );
      }
    } else {
      emit(
        errorStateBuilder(
          error: exception.toString(),
          invalidCredentials: false,
          idToken: idToken,
          userNotFound: false,
        ),
      );
    }
  }

  Future<void> login(Map<String, dynamic> payload) async {
    emit(AuthLoading(true));
    try {
      final result = await _authRepository.login(payload);
      _storeTokens(result.jwt, result.refreshToken);
      emit(AuthSuccess());
    } on InvalidCredentialsException catch (e) {
      emit(AuthError(error: e.message, invalidCredentials: true));
    } catch (e) {
      emit(AuthError(error: e.toString(), invalidCredentials: false));
    }
  }

  Future<void> signInWithGoogle(String? idToken) async {
    emit(AuthGoogleLoading(true));

    try {
      final payload = {'idToken': idToken};
      final result = await _authRepository.signInWithGoogle(payload);
      _storeTokens(result.jwt, result.refreshToken!);
      emit(AuthSuccess());
    } on Exception catch (e) {
      _handleApiException<AuthGoogleError>(
        e,
        idToken,
        ({
          required String error,
          required bool invalidCredentials,
          String? idToken,
          required bool userNotFound,
        }) => AuthGoogleError(
          error: error,
          invalidCredentials: invalidCredentials,
          idToken: idToken,
          userNotFound: userNotFound,
        ),
      );
    }
  }

  Future<void> signInWithApple(String? idToken) async {
    emit(AuthAppleLoading(true));
    try {
      final payload = {'idToken': idToken};
      final result = await _authRepository.signInWithApple(payload);
      _storeTokens(result.jwt, result.refreshToken!);
      emit(AuthSuccess());
    } on Exception catch (e) {
      _handleApiException<AuthAppleError>(
        e,
        idToken,
        ({
          required String error,
          required bool invalidCredentials,
          String? idToken,
          required bool userNotFound,
        }) => AuthAppleError(
          error: error,
          invalidCredentials: invalidCredentials,
          idToken: idToken,
          userNotFound: userNotFound,
        ),
      );
    }
  }

  Future<void> logout() async {
    emit(AuthLoading(true));
    try {
      final logoutSuccess = await _authRepository.logout();
      if (logoutSuccess) {
        final authRepository = AuthDataRepository();
        authRepository.setTokens('', '');
        await CookieManager.clearAllCookies();
        await WebNavigationHelper.clearBrowserStorage();
        await HydratedBlocOverrides.runZoned(() async {
          final userCubit = UserCubit(UserRepositoryImpl());
          userCubit.clearUser();
        });
        emit(AuthLogout());
      }
    } catch (e) {
      // Emit logout error state
      emit(AuthLogoutError(error: e.toString()));
    }
  }

  // Handle logout locally since the API is unavailable in the dev environment.
  Future<void> logoutLocally() async {
    try {
      final authRepository = AuthDataRepository();
      authRepository.setTokens('', '');
      await HydratedBlocOverrides.runZoned(() async {
        final userCubit = UserCubit(UserRepositoryImpl());
        userCubit.clearUser();
      });
      // Emit logout state to trigger navigation
      await navToLogin();
    } on Exception catch (e) {
      debugPrint("Logout locally failed: $e");
    }
  }

  Future<void> navToLogin() async {
    if (state is! AuthNavigateToLogin) {
      emit(AuthNavigateToLogin());
    }
  }

  // create password
  Future<void> createPassword(
    String userId,
    String newPassword,
    String confirmPassword,
  ) async {
    emit(AuthLoading(true));
    try {
      final payload = {
        "userId": userId,
        "newPassword": newPassword,
        "confirmPassword": confirmPassword,
      };
      final result = await _authRepository.createPassword(payload);
      emit(
        result != null
            ? CreatePasswordSuccess()
            : CreatePasswordFailure(error: 'Failed to create password'),
      );
    } catch (e) {
      emit(CreatePasswordFailure(error: e.toString()));
    }
  }

  Future<void> verifyEmail(String email) async {
    emit(EmailVerificationLoading());
    try {
      final result = await _authRepository.verifyEmail({"email": email});
      emit(
        result != null
            ? EmailVerificationSuccess(message: result)
            : EmailVerificationFailure(error: 'Failed to verify email'),
      );
    } catch (e) {
      emit(EmailVerificationFailure(error: e.toString()));
    }
  }

  Future<void> validateToken(String token) async {
    emit(TokenValidationLoading());
    try {
      final result = await _authRepository.validateToken(token);
      emit(
        result != null
            ? TokenValidationSuccess(message: result)
            : TokenValidationFailure(error: 'Failed to validate token'),
      );
    } catch (e) {
      emit(TokenValidationFailure(error: e.toString()));
    }
  }

  /// Forgot password - creates new password with token
  Future<void> forgotPassword(
    String? token,
    String newPassword,
    String confirmPassword,
  ) async {
    if (token == null) {
      emit(CreateNewPasswordFailure(error: 'Token is null'));
      return;
    }

    emit(CreateNewPasswordLoading());
    try {
      final payload = {
        "token": token,
        "newPassword": newPassword,
        "confirmPassword": confirmPassword,
      };
      final result = await _authRepository.forgotPassword(payload);
      emit(
        result != null
            ? CreateNewPasswordSuccess(message: result)
            : CreateNewPasswordFailure(error: 'Failed to create new password'),
      );
    } catch (e) {
      emit(CreateNewPasswordFailure(error: e.toString()));
    }
  }

  /// Reset password from profile
  Future<void> resetPassword(
    String oldPassword,
    String newPassword,
    String confirmPassword,
  ) async {
    emit(ResetPasswordLoading());
    try {
      final payload = {
        "currentPassword": oldPassword,
        "newPassword": newPassword,
        "confirmPassword": confirmPassword,
      };
      final result = await _authRepository.resetPassword(payload);
      emit(
        result != null
            ? ResetPasswordSuccess(message: result)
            : ResetPasswordFailure(error: 'Failed to reset password'),
      );
    } catch (e) {
      emit(ResetPasswordFailure(error: e.toString()));
    }
  }

  Future<void> validatePasswordSetupLink(String? userId) async {
    try {
      if (userId == null || userId.isEmpty) {
        emit(
          PasswordSetupLinkValidationFailure(error: invalidPasswordSetupLink),
        );
        return;
      }
      final result = await _authRepository.validatePasswordSetupLink(userId);
      if (result["shouldRedirect"] == true) {
        emit(PasswordSetupLinkValidationSuccess(message: 'validation success'));
      } else {
        emit(
          PasswordSetupLinkValidationFailure(
            error: result["message"] ?? invalidPasswordSetupLink,
          ),
        );
      }
    } catch (e) {
      emit(PasswordSetupLinkValidationFailure(error: e.toString()));
    }
  }
}
