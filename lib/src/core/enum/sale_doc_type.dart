enum SalesDocumentsType {
  CLOSING_DOCUMENT,
  PURCHASE_AGREEMENT,
  AGENCY_DISCLOSURE,
  LEAD_BASED_PAINT_DISCLOSURE,
  SELLER_DISCLOSURE,
  EXCLUSIVE_BUYER_AGENCY_AGREEMENT,
  ADDENDUMS,
  EXCLUSIVE_RIGHT_TO_SELL,
  APPROVED_CLOSING_DOCUMENT,
}

extension SalesDocumentsTypeExt on SalesDocumentsType {
  String get displayName {
    switch (this) {
      case SalesDocumentsType.CLOSING_DOCUMENT:
        return 'Closing Document';
      case SalesDocumentsType.PURCHASE_AGREEMENT:
        return 'Purchase Agreement';
      case SalesDocumentsType.AGENCY_DISCLOSURE:
        return 'Agency Disclosure';
      case SalesDocumentsType.LEAD_BASED_PAINT_DISCLOSURE:
        return 'Lead‑Based Paint Disclosure';
      case SalesDocumentsType.SELLER_DISCLOSURE:
        return 'Seller Disclosure';
      case SalesDocumentsType.EXCLUSIVE_BUYER_AGENCY_AGREEMENT:
        return 'Exclusive Buyer Agency Agreement';
      case SalesDocumentsType.ADDENDUMS:
        return 'Addendum';
      case SalesDocumentsType.EXCLUSIVE_RIGHT_TO_SELL:
        return 'Exclusive Right to Sell';
      case SalesDocumentsType.APPROVED_CLOSING_DOCUMENT:
        return 'Approved Closing Document';
      default:
        return toString().split('.').last.replaceAll('_', ' ');
    }
  }

  /// Optional: a safe label (enum name) if you need the canonical string.
  String get label => toString().split('.').last;
}
