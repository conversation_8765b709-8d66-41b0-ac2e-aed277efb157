import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/commission_revenue_info_model.dart';
import '../../../domain/repository/commission_repository.dart';

part 'commission_revenue_state.dart';

class CommissionRevenueCubit extends Cubit<CommissionRevenueState> {
  final CommissionRepository repository;

  CommissionRevenueCubit(this.repository)
    : super(const CommissionRevenueInitial());

  /// Fetch commission revenue details with pagination support
  Future<void> fetchCommissionRevenueDetailsByUser({
    required Map<String, dynamic> requestBody,
    bool loadMore = false,
  }) async {
    // Only show loading state if not loading more
    // Only show loading state if not loading more
    if (!loadMore) {
      emit(const CommissionRevenueLoading());
    }

    try {
      // repository returns a paginated response (similar to AgentRepository.getAgents)
      final response = await repository.fetchCommissionByUser(requestBody);

      emit(
        CommissionRevenueLoaded(
          commissionRevenue: response.content,
          totalCount: response.totalElements,
          currentPage: response.number,
          hasMore: !(response.last),
          totalPages: response.totalPages,
        ),
      );
    } on ApiException catch (e) {
      emit(
        CommissionRevenueError(message: e.message, statusCode: e.statusCode),
      );
    } catch (e) {
      emit(
        CommissionRevenueError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  /// Clear current state
  void clearCommissionRevenue() {
    emit(const CommissionRevenueInitial());
  }
}
