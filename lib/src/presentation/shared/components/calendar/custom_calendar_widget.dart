import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:neorevv/src/core/utils/date_formatter.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/json_consts.dart';
import '../elevated_button.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';
import 'month_year_picker.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;

class CustomCalendarWidget extends HookWidget {
  final bool fromFilter;
  final bool isFutureDateHide;
  final bool isQuickSelectionShow;
  final DateTime currentMonth;
  final String selectedQuickOption;
  final DateTime? selectedDate;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final bool isCustomRangeMode;
  final DateTime? customRangeStart;
  final DateTime? customRangeEnd;
  final Function(String) onQuickSelection;
  final Function(DateTime) onDateSelection;
  final Function(int) onNavigateMonth;
  final VoidCallback onCancel;
  final VoidCallback onApply;
  final bool Function(DateTime) isDateInSelectedRange;
  final bool Function(DateTime) isRangeStartDate;
  final bool Function(DateTime)? isDateDisabled;
  final DateTime? maxSelectableDate;
  final DateTime? initialDateForPicker;
  final String dateFilterMode;
  final List<String> quickSelectionOptions;

  const CustomCalendarWidget({
    super.key,
    required this.currentMonth,
    required this.selectedQuickOption,
    required this.selectedDate,
    required this.rangeStart,
    required this.rangeEnd,
    required this.isCustomRangeMode,
    required this.customRangeStart,
    required this.customRangeEnd,
    required this.onQuickSelection,
    required this.onDateSelection,
    required this.onNavigateMonth,
    required this.onCancel,
    required this.onApply,
    required this.isDateInSelectedRange,
    required this.isRangeStartDate,
    this.fromFilter = false,
    this.isFutureDateHide = true,
    this.isQuickSelectionShow = false,
    this.isDateDisabled,
    this.maxSelectableDate,
    this.initialDateForPicker,
    this.dateFilterMode = 'both',
    this.quickSelectionOptions = const [],
  });

  @override
  Widget build(BuildContext context) {
    final showMonthYearPicker = useState(false);
    final shouldShowQuickSelection = _shouldShowQuickSelection();
    // Use custom breakpoint for calendar - only small mobile devices use mobile layout
    // Tablets (≥800px) and desktop use the desktop layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobileCalendar =
        screenWidth < 800; // Mobile layout only for screens < 800px

    return Stack(
      children: [
        if (isMobileCalendar)
          _buildMobileCalendar(
            context,
            showMonthYearPicker,
            shouldShowQuickSelection,
          )
        else
          _buildDesktopCalendar(
            context,
            showMonthYearPicker,
            shouldShowQuickSelection,
          ),
        if (showMonthYearPicker.value)
          Positioned.fill(
            child: Container(
              color: AppTheme.black.withValues(alpha: 0.5),
              child: Center(
                child: MonthYearPicker(
                  anchorKey:
                      GlobalKey(), // Provide a suitable GlobalKey or pass one from parent
                  initialDate: initialDateForPicker ?? currentMonth,
                  minDate: _getMinSelectableDate(),
                  maxDate: _getMaxSelectableDate(),
                  onDateSelected: (DateTime newDate) {
                    onNavigateMonth(
                      newDate.month -
                          currentMonth.month +
                          (newDate.year - currentMonth.year) * 12,
                    );
                    showMonthYearPicker.value = false;
                  },
                  onCancel: () {
                    showMonthYearPicker.value = false;
                  },
                ),
              ),
            ),
          ),
      ],
    );
  }

  DateTime _getMinSelectableDate() {
    if (isDateDisabled == null) {
      return DateTime(2015, 1, 1); // Default min date
    }

    // Start from a reasonable min date and find first enabled month
    DateTime testDate = DateTime(2015, 1, 1);
    final maxDate = DateTime.now();

    while (testDate.isBefore(maxDate)) {
      // Check if any day in this month is enabled
      final daysInMonth = DateTime(testDate.year, testDate.month + 1, 0).day;
      bool hasEnabledDay = false;

      for (int day = 1; day <= daysInMonth; day++) {
        final date = DateTime(testDate.year, testDate.month, day);
        if (!isDateDisabled!(date)) {
          hasEnabledDay = true;
          break;
        }
      }

      if (hasEnabledDay) {
        return testDate;
      }

      // Move to next month
      testDate = DateTime(testDate.year, testDate.month + 1, 1);
    }

    return DateTime.now(); // Fallback to current date
  }

  DateTime _getMaxSelectableDate() {
    if (maxSelectableDate != null) {
      return maxSelectableDate!;
    }
    if (!isFutureDateHide) {
      return DateTime(DateTime.now().year + 10, 12, 31);
    }
    return DateTime.now();

    // if (isDateDisabled == null) {
    //   return DateTime.now();
    // }

    // For expiration date field, we might allow future dates
    // You can customize this based on your requirements
    // return DateTime(DateTime.now().year + 10, 12, 31);
  }

  Widget _buildMobileCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
    bool shouldShowQuickSelection,
  ) {
    return Container(
      width: 380,
      //height: 500,
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.12), // Slight opacity
            blurRadius: 30, // Large blur for soft edges
            spreadRadius: 10, // Optional: increases size of the shadow
            offset: Offset(0, 8), // Centered shadow on all sides
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(defaultPadding * 1.2), // Increased padding
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMobileCalendarHeader(showPicker),
            if (isQuickSelectionShow)
              _buildMobileQuickSelectionBubbles(context),
            _buildMobileWeekdayHeaders(),
            _buildMobileDatesGrid(),
            _buildMobileActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
    bool shouldShowQuickSelection,
  ) {
    return Container(
      width: isQuickSelectionShow
          ? calenderWebWidthWithQuickselection.toDouble()
          : calenderWebWidthWithOutQuickselection.toDouble(),
      height: isQuickSelectionShow
          ? calenderWebHeightWithQuickselection.toDouble()
          : calenderWebHeightWithOutQuickselection.toDouble(),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment:
            CrossAxisAlignment.stretch, // Make sidebar fill full height
        children: [
          // Show sidebar only if mode supports quick selection
          if (shouldShowQuickSelection) _buildQuickSelectionSidebar(context),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(
                defaultPadding,
                defaultPadding,
                defaultPadding,
                0,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildCalendarHeader(showPicker),
                  _buildWeekdayHeaders(context),
                  _buildDatesGrid(context),
                  const Spacer(),
                  _buildSelectedDateRangeDisplay(context),
                  const Spacer(),
                  _buildActionButtons(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSelectionSidebar(BuildContext context) {
 final sidebarWidth = Responsive.isTablet(context) ? 140.0 : 160.0;
  final List<String> options = quickSelectionOptions;
  final bool hasUserInteracted = _hasUserInteracted();
  
  return Container(
    width: sidebarWidth,
    decoration: BoxDecoration(
      color: AppTheme.searchbarBg,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(10),
        bottomLeft: Radius.circular(10),
      ),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.all(defaultPadding),
          child: Text(quickSelection, style: AppFonts.semiBoldTextStyle(14)),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: defaultPadding / 2),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: options.map((option) {
                final isClearOption = option.toLowerCase().contains('clear');

                // Keep quick-option visually selected either when:
                // - parent-provided selectedQuickOption matches the option, OR
                // - the option represents a single-date selection (heuristic: contains 'single')
                //   and a date is currently selected (covers parent clearing selectedQuickOption on date selection).
                final isSingleIndicator = option.toLowerCase().contains('single');
                 final bool hasExplicitQuick = selectedQuickOption.isNotEmpty;
                              final bool nothingSelected = selectedDate == null &&
                    rangeStart == null &&
                    customRangeStart == null &&
                    !hasExplicitQuick;
                final bool isSelected = hasExplicitQuick
                    ? (selectedQuickOption == option)
                    : (!isCustomRangeMode && isSingleIndicator && nothingSelected);

                final isEnabled = isClearOption ? hasUserInteracted : true;
                return _buildQuickOptionButton(
                  option, 
                  isSelected, 
                  isClearOption,
                  isEnabled,
                );
              }).toList(),
            ),
          ),
        ),
      ],
    ),
  );
}
Widget _buildQuickOptionButton(
  String option,
  bool isSelected,
  bool isClearOption,
  bool isEnabled,
) {
  // Shared visuals
  const buttonPadding = EdgeInsets.symmetric(
    horizontal: defaultPadding * 0.8,
    vertical: defaultPadding * 0.6,
  );
  const buttonRadius = 25.0;

  // Disabled look (used for Clear when inactive)
  final disabledBg = AppTheme.searchbarBg.withValues(alpha: 0.3);
  final disabledText = AppTheme.tableDataFont.withValues(alpha: 0.4);
  final disabledBorder = BorderSide(color: AppTheme.comboBoxBorder.withValues(alpha: 0.3));

  // Clear button neutral look (when enabled) - NOTE: no border
  final clearBg = AppTheme.scaffoldBgColor;
  final clearText = AppTheme.primaryTextColor;

  // Regular button look (unselected -> white, no border)
  final regularBg = AppTheme.white;
  final regularText = AppTheme.black;

  // Selected look for regular quick options (primary blue)
  final selectedBg = AppTheme.selectedComboBoxBorder;
  final selectedText = AppTheme.white;

  Color bg;
  Color fg;
  BorderSide? side;

  if (isClearOption) {
    // Clear never shows a visible border; use background/text differences only
    if (!isEnabled) {
      // Clear inactive (dimmed)
      bg = disabledBg;
      fg = disabledText;
      side = null;
    } else if (isSelected) {
      // Clear selected -> neutral appearance, no border
      bg = clearBg;
      fg = clearText;
      side = null;
    } else {
      // Clear enabled but not selected -> neutral appearance, no border
      bg = clearBg;
      fg = clearText;
      side = null;
    }
  } else {
    // Non-clear options
    if (isSelected) {
      // Selected -> primary blue, no border
      bg = selectedBg;
      fg = selectedText;
      side = null;
    } else {
      // Unselected -> white background, no border
      bg = regularBg;
      fg = regularText;
      side = null;
    }
  }

  return Padding(
    padding: const EdgeInsets.only(bottom: defaultPadding / 2),
    child: AppButton(
      label: option,
      onPressed: isEnabled ? () => onQuickSelection(option) : null,
      backgroundColor: bg,
      foregroundColor: fg,
      borderRadius: buttonRadius,
      padding: buttonPadding,
      elevation: 0,
      borderSide: side, // null => BorderSide.none inside AppButton
      textStyle: AppFonts.regularTextStyle(12, color: fg),
      useMinSize: false,
    ),
  );
}
  bool _isCurrentMonth(DateTime month) {
    final now = DateTime.now();
    return month.year == now.year && month.month == now.month;
  }

  bool _isLastAllowedMonth(DateTime month) {
    if (maxSelectableDate != null) {
      // Check if current month is the same as or after the max selectable month
      return month.year == maxSelectableDate!.year &&
          month.month >= maxSelectableDate!.month;
    }
    if (!isFutureDateHide) {
      return false; // No restriction when future dates are allowed
    }
    final now = DateTime.now();
    return month.year == now.year && month.month == now.month;
  }

  bool _isPreviousMonthDisabled() {
    if (isDateDisabled != null) {
      // Check if there's ANY enabled date before the current month
      // Start from a reasonable minimum date (e.g., 10 years ago)
      final minDate = DateTime(DateTime.now().year - 10, 1, 1);
      final currentMonthStart = DateTime(
        currentMonth.year,
        currentMonth.month,
        1,
      );

      // Sample check: iterate through months before current month
      DateTime checkDate = DateTime(
        currentMonth.year,
        currentMonth.month - 1,
        1,
      );

      // Check up to 120 months back (10 years)
      for (int i = 0; i < 120; i++) {
        if (checkDate.isBefore(minDate)) break;

        // Check if this month has any enabled day
        final daysInMonth = DateTime(
          checkDate.year,
          checkDate.month + 1,
          0,
        ).day;
        for (int day = 1; day <= daysInMonth; day++) {
          final date = DateTime(checkDate.year, checkDate.month, day);
          if (!isDateDisabled!(date)) {
            // Found at least one enabled date in a previous month
            return false;
          }
        }

        // Move to previous month
        checkDate = DateTime(checkDate.year, checkDate.month - 1, 1);
      }

      // No enabled dates found in any previous month
      return true;
    }
    return false;
  }

  bool _isNextMonthDisabled() {
    if (isDateDisabled != null) {
      // Get last day of current month
      final lastDayOfCurrentMonth = DateTime(
        currentMonth.year,
        currentMonth.month + 1,
        0,
      );

      // If last day of current month is disabled, immediately block next month
      if (isDateDisabled!(lastDayOfCurrentMonth)) {
        return true;
      }

      // Get first day of next month
      final firstDayOfNextMonth = DateTime(
        currentMonth.year,
        currentMonth.month + 1,
        1,
      );

      // Get last day of next month
      final lastDayOfNextMonth = DateTime(
        currentMonth.year,
        currentMonth.month + 2,
        0,
      );

      // Check if both first and last day of next month are disabled
      // This ensures entire next month is invalid
      return isDateDisabled!(firstDayOfNextMonth) &&
          isDateDisabled!(lastDayOfNextMonth);
    }
    if (maxSelectableDate != null) {
      final nextMonth = DateTime(currentMonth.year, currentMonth.month + 1, 1);
      return nextMonth.isAfter(
        DateTime(maxSelectableDate!.year, maxSelectableDate!.month, 1),
      );
    }
    return false;
  }

  Widget _buildCalendarHeader(ValueNotifier<bool> showPicker) {
    final isPrevMonthDisabled = _isPreviousMonthDisabled();
    final isNextMonthDisabled = _isNextMonthDisabled();

    return Material(
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        mouseCursor: SystemMouseCursors.click,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Tooltip(
              message: AppStrings.previousMonth,
              child: IconButton(
                onPressed: isPrevMonthDisabled
                    ? null
                    : () => onNavigateMonth(-1),
                icon: Icon(
                  Icons.chevron_left,
                  color: isPrevMonthDisabled
                      ? AppTheme.black.withValues(alpha: 0.3)
                      : AppTheme.black,
                ),
              ),
            ),
            Tooltip(
              message: AppStrings.changeYear,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  mouseCursor: SystemMouseCursors.click,
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () => showPicker.value = true,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        DateFormat('MMMM yyyy').format(currentMonth),
                        style: AppFonts.semiBoldTextStyle(16),
                      ),
                      SizedBox(width: defaultPadding / 4),
                      Icon(
                        Icons.calendar_month,
                        size: 16,
                        color: AppTheme.tableDataFont,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Tooltip(
              message: AppStrings.nextMonth,
              child: IconButton(
                onPressed:
                    isNextMonthDisabled || _isLastAllowedMonth(currentMonth)
                    // (isFutureDateHide && _isCurrentMonth(currentMonth))
                    ? null
                    : () => onNavigateMonth(1),
                icon: Icon(
                  Icons.chevron_right,
                  color:
                      isNextMonthDisabled || _isLastAllowedMonth(currentMonth)
                      // (isFutureDateHide && _isCurrentMonth(currentMonth))
                      ? AppTheme.black.withValues(alpha: 0.3)
                      : AppTheme.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeekdayHeaders(BuildContext context) {
    return Row(
      children: weekdayHeaders
          .map(
            (day) => Expanded(
              child: Center(
                child: Text(
                  day,
                  style: AppFonts.semiBoldTextStyle(
                    Responsive.isMobile(context) ? 14 : 12,
                    color: AppTheme.tableDataFont,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildDatesGrid(BuildContext context) {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstDate = DateTime(currentMonth.year, currentMonth.month, 1);
    final firstWeekday = firstDate.weekday; // 1=Monday, 7=Sunday
    // Adjust to Sunday-based week (0=Sunday, 6=Saturday)
    final emptySlots = firstWeekday == 7 ? 0 : firstWeekday;
    final now = DateTime.now();

    // Calculate responsive grid dimensions
    final isMobile = Responsive.isMobile(context);
    final spacing = isMobile ? 4.0 : 3.0; // Reduced spacing
    final totalSlots = emptySlots + daysInMonth;
    final weeksNeeded = (totalSlots / 7).ceil();
    final itemCount = weeksNeeded * 7;
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: spacing,
        crossAxisSpacing: spacing,
      ),
      itemCount: itemCount, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final dayNumber = index - emptySlots + 1;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;
        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(
          currentMonth.year,
          currentMonth.month,
          dayNumber,
          0,
          0,
          0,
          0,
          0,
        );
        final isFuture = date.isAfter(DateTime(now.year, now.month, now.day));
        return _buildDateCell(
          date,
          now,
          context,
          isFuture: isFuture && isFutureDateHide,
        );
      },
    );
  }

  Widget _buildDateCell(
    DateTime date,
    DateTime now,
    BuildContext context, {
    bool isFuture = false,
  }) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;
    bool isCustomDisabled = isDateDisabled?.call(date) ?? false;
    bool isDisabled =
        isFuture || isCustomDisabled; //|| (isFutureDateHide && isToday);
   if (isDisabled) {
    bgColor = AppTheme.searchbarBg.withAlpha(80);
    textColor = AppTheme.tableDataFont.withAlpha(120);
  } else if (isSelected) {
    // Single date mode - only highlight the selected date
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isCustomRangeStart || isCustomRangeEnd) {
    // Custom range mode - highlight start/end dates
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isStartDate) {
    // Quick selection range mode - highlight start date
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isInRange) {
    // Only show range highlight in custom range or quick selection modes
    bgColor = Colors.grey[350];
    textColor = AppTheme.black;
  } else if (isToday) {
    bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
    textColor = AppTheme.selectedComboBoxBorder;
  }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        mouseCursor: isDisabled
            ? SystemMouseCursors.basic
            : SystemMouseCursors.click,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: isDisabled ? null : () => onDateSelection(date),
        child: Container(
          margin: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(8),
            border: isToday && !isSelected && !isStartDate
                ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
                : null,
          ),
          child: Center(
            child: Text(
              date.day.toString(),
              style:
                  AppFonts.regularTextStyle(
                    Responsive.isMobile(context) ? 14 : 12,
                    color: textColor,
                  ).copyWith(
                    fontWeight: isSelected || isToday || isStartDate
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedDateRangeDisplay(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.only(
        top: isMobile ? defaultPadding * 0.3 : defaultPadding / 2,
        left: isMobile
            ? defaultPadding / 2
            : 0, // Remove extra padding for desktop
        right: isMobile
            ? defaultPadding / 2
            : 0, // Remove extra padding for desktop
        bottom: isMobile ? defaultPadding * 0.2 : defaultPadding * 0.1,
      ),
      child: _buildSelectedDisplayButton(),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.only(
        top: isMobile
            ? defaultPadding * 0.2
            : defaultPadding * 0.4, // Increased top padding for better spacing
        left: isMobile
            ? defaultPadding / 2
            : 0, // Match selected date range display padding
        right: isMobile
            ? defaultPadding / 2
            : 0, // Match selected date range display padding
        bottom: defaultPadding / 2,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildCancelButton(),
          SizedBox(width: defaultPadding),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildSelectedDisplayButton() {
   String displayText = 'Select Date';

    // Single date mode - highest priority
    if (selectedDate != null) {
      displayText = AppDateFormatter.formatDateMMddyyyy(selectedDate!);
    }
    // Custom range mode
    else if (isCustomRangeMode) {
      if (customRangeStart != null && customRangeEnd != null) {
        final startFormatted = AppDateFormatter.formatDateMMddyyyy(
          customRangeStart!,
        );
        final endFormatted = AppDateFormatter.formatDateMMddyyyy(
          customRangeEnd!,
        );
        displayText = '$startFormatted - $endFormatted';
      } else if (customRangeStart != null) {
        // Show start date even without end date
        displayText = AppDateFormatter.formatDateMMddyyyy(customRangeStart!);
      }
    }
    // Quick selection range mode
    else if (selectedQuickOption.isNotEmpty) {
      if (rangeStart != null && rangeEnd != null) {
        final startFormatted = AppDateFormatter.formatDateMMddyyyy(rangeStart!);
        final endFormatted = AppDateFormatter.formatDateMMddyyyy(rangeEnd!);
        displayText = '$startFormatted - $endFormatted';
      }
    }
    return Container(
      width: double.infinity, // Ensure full width like mobile
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.8,
        vertical: defaultPadding * 0.6,
      ),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(2),
        border: Border.all(
          color: AppTheme.comboBoxBorder.withValues(alpha: 0.5),
        ),
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          13, // Slightly larger font like mobile
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCancelButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        mouseCursor: SystemMouseCursors.click,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: onCancel,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding,
            vertical: defaultPadding / 2,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.comboBoxBorder),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(cancel, style: AppFonts.regularTextStyle(12)),
        ),
      ),
    );
  }

  Widget _buildApplyButton() {
    final bool isActive = _hasUserInteracted();
    return Material(
      color: Colors.transparent,
      child: InkWell(
        mouseCursor: isActive
            ? SystemMouseCursors.click
            : SystemMouseCursors.basic,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: isActive ? onApply : null, // Only allow tap when active
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding,
            vertical: defaultPadding / 2,
          ),
          decoration: BoxDecoration(
            color: isActive
                ? AppTheme.selectedComboBoxBorder
                : AppTheme.selectedComboBoxBorder.withValues(
                    alpha: 0.5,
                  ), // Inactive state with opacity
            border: Border.all(color: AppTheme.comboBoxBorder),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            confirmLabel,
            style: AppFonts.regularTextStyle(
              12,
              color: isActive
                  ? AppTheme.white
                  : AppTheme.white.withValues(
                      alpha: 0.7,
                    ), // Dimmed text when inactive
            ),
          ),
        ),
      ),
    );
  }

  String _getDisplayText() {
     if (selectedQuickOption == 'Clear Selection' || 
      (selectedDate == null && 
       customRangeStart == null && 
       rangeStart == null && 
       selectedQuickOption.isEmpty)) {
    return selectDateRange; // or "Select Date" - your default text
  }
    // Single date mode - highest priority
    if (selectedDate != null) {
      return AppDateFormatter.formatDateMMddyyyy(selectedDate!);
    }

    // Custom range mode
    if (isCustomRangeMode) {
      if (customRangeStart != null && customRangeEnd != null) {
        final startFormatted = AppDateFormatter.formatDateMMddyyyy(
          customRangeStart!,
        );
        final endFormatted = AppDateFormatter.formatDateMMddyyyy(
          customRangeEnd!,
        );
        return '$startFormatted - $endFormatted';
      } else if (customRangeStart != null) {
        // Show start date even without end date
        return AppDateFormatter.formatDateMMddyyyy(customRangeStart!);
      } else {
        return selectStartDate;
      }
    }

    // Quick selection range mode
    if (selectedQuickOption.isNotEmpty &&
        rangeStart != null &&
        rangeEnd != null) {
      final startFormatted = AppDateFormatter.formatDateMMddyyyy(rangeStart!);
      final endFormatted = AppDateFormatter.formatDateMMddyyyy(rangeEnd!);
      return '$startFormatted - $endFormatted';
    }

    // Default fallback
    return selectDateRange;
  }
  // Helper method to check if user has interacted with calendar
  bool _hasUserInteracted() {
    return selectedDate != null ||
        (rangeStart != null && rangeEnd != null) ||
        customRangeStart != null ||
        selectedQuickOption.isNotEmpty;
  }

  // Mobile methods - compact design
  Widget _buildMobileQuickSelectionBubbles(BuildContext context) {
  final List<String> options = quickSelectionOptions; 
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Container(
        width: double.infinity, // TEMP: highlight parent container
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title Padding
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: defaultPadding * 0.9,
              ),
              child: Text(
                quickSelection,
                style: AppFonts.semiBoldTextStyle(14),
              ),
            ),
            // Horizontal Bubbles (left-aligned)
            SizedBox(
              height: 44,
              child: ScrollConfiguration(
                behavior: ScrollConfiguration.of(
                  context,
                ).copyWith(scrollbars: false),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  physics: const BouncingScrollPhysics(),
                  child: Wrap(
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: defaultPadding * 0.4,
                    children: List.generate(options.length, (index) {
                      final option = options[index];

                      // Same selection fallback as desktop: consider "single" option selected
                      final isSingleIndicator = option.toLowerCase().contains('single');
                      
                                           final bool hasExplicitQuick = selectedQuickOption.isNotEmpty;
                      final bool nothingSelected = selectedDate == null &&
                          rangeStart == null &&
                          customRangeStart == null &&
                          !hasExplicitQuick;
                      final isSelected = hasExplicitQuick
                          ? (selectedQuickOption == option)
                          : (!isCustomRangeMode && isSingleIndicator && nothingSelected);

                    final isClearOption = option.toLowerCase().contains('clear');
                    final isEnabled = isClearOption ? _hasUserInteracted() : true;

                      return _buildMobileQuickOptionBubble(option, isSelected, isClearOption, isEnabled);
                    }),
                  ),
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 0.6),
          ],
        ),
      ),
    );
  }
Widget _buildMobileQuickOptionBubble(String option, bool isSelected, bool isClearOption, bool isEnabled) {
  // Shared visuals to match AppButton style
  const buttonPadding = EdgeInsets.symmetric(
    horizontal: defaultPadding * 0.8,
    vertical: defaultPadding * 0.35,
  );
  const buttonRadius = 20.0;

  // Disabled look (clear inactive)
  final disabledBg = AppTheme.searchbarBg.withValues(alpha: 0.3);
  final disabledText = AppTheme.tableDataFont.withValues(alpha: 0.4);

  // Clear neutral look (no border ever)
  final clearBg = AppTheme.scaffoldBgColor;
  final clearText = AppTheme.primaryTextColor;

  // Regular unselected
  final regularBg = AppTheme.white;
  final regularText = AppTheme.black;

  // Selected look for regular quick options (primary blue)
  final selectedBg = AppTheme.selectedComboBoxBorder;
  final selectedText = AppTheme.white;

  Color bg;
  Color fg;
  BorderSide? side;

   if (isClearOption) {
    // Clear: no border ever. disabled look until user interacts.
    if (!isEnabled) {
      bg = disabledBg;
      fg = disabledText;
      side = null;
    } else {
      bg = clearBg;
      fg = clearText;
      side = null;
    }
  } else {
    // Non-clear options: selected -> primary blue; unselected -> white, no border
    if (isSelected) {
      bg = selectedBg;
      fg = selectedText;
      side = null;
    } else {
      bg = regularBg;
      fg = regularText;
      side = null;
    }
  }

  return Padding(
    padding: const EdgeInsets.only(bottom: defaultPadding / 2),
    child: AppButton(
      label: option,
      onPressed: isEnabled ? () => onQuickSelection(option) : null,
      backgroundColor: bg,
      foregroundColor: fg,
      borderRadius: buttonRadius,
      padding: buttonPadding,
      elevation: 0,
      borderSide: side, // null => BorderSide.none inside AppButton
      textStyle: AppFonts.regularTextStyle(12, color: fg),
      useMinSize: false,
    ),
  );
}

  Widget _buildMobileCalendarHeader(ValueNotifier<bool> showPicker) {
    final isPrevMonthDisabled = _isPreviousMonthDisabled();
    final isNextMonthDisabled = _isNextMonthDisabled();

    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Tooltip(
            message: AppStrings.previousMonth,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                mouseCursor: SystemMouseCursors.click,
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: isPrevMonthDisabled ? null : () => onNavigateMonth(-1),
                child: Container(
                  padding: EdgeInsets.all(defaultPadding * 0.6),
                  decoration: BoxDecoration(
                    color: isPrevMonthDisabled
                        ? AppTheme.searchbarBg.withValues(alpha: 0.5)
                        : AppTheme.searchbarBg,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.chevron_left,
                    size: 14,
                    color: isPrevMonthDisabled
                        ? AppTheme.black.withValues(alpha: 0.3)
                        : AppTheme.black.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ),
          ),
          Tooltip(
            message: AppStrings.changeYear,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                mouseCursor: SystemMouseCursors.click,
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () => showPicker.value = true,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppDateFormatter.formatCalendarHeader(currentMonth),
                      style: AppFonts.semiBoldTextStyle(
                        16,
                      ), // Increased font size
                    ),
                    SizedBox(width: defaultPadding * 0.2),
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: AppTheme.black.withValues(alpha: 0.7),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Tooltip(
            message: AppStrings.nextMonth,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                mouseCursor:
                    isNextMonthDisabled || _isLastAllowedMonth(currentMonth)
                    // (isFutureDateHide && _isCurrentMonth(currentMonth))
                    ? SystemMouseCursors.basic
                    : SystemMouseCursors.click,
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: isNextMonthDisabled || _isLastAllowedMonth(currentMonth)
                    // (isFutureDateHide && _isCurrentMonth(currentMonth))
                    ? null
                    : () => onNavigateMonth(1),
                child: Container(
                  padding: EdgeInsets.all(defaultPadding * 0.6),
                  decoration: BoxDecoration(
                    color:
                        isNextMonthDisabled ||
                            (isFutureDateHide && _isCurrentMonth(currentMonth))
                        ? AppTheme.searchbarBg.withValues(alpha: 0.5)
                        : AppTheme.searchbarBg,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.chevron_right,
                    size: 14,
                    color:
                        isNextMonthDisabled ||
                            (isFutureDateHide && _isCurrentMonth(currentMonth))
                        ? AppTheme.black.withValues(alpha: 0.3)
                        : AppTheme.black.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileWeekdayHeaders() {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        children: weekdayHeaders
            .map(
              (day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: AppFonts.semiBoldTextStyle(
                      12, // Increased font size
                      color: AppTheme.tableDataFont,
                    ),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildMobileDatesGrid() {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstWeekday = DateTime(
      currentMonth.year,
      currentMonth.month,
      1,
    ).weekday;
    final now = DateTime.now();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: 2.0, // Reduced spacing for better fit
        crossAxisSpacing: 2.0, // Reduced spacing for better fit
      ),
      itemCount: 42,
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 2;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(currentMonth.year, currentMonth.month, dayNumber);
        final isFuture = date.isAfter(DateTime(now.year, now.month, now.day));
        return _buildMobileDateCell(
          date,
          now,
          isFuture: isFuture && isFutureDateHide,
        );
      },
    );
  }

  Widget _buildMobileDateCell(
    DateTime date,
    DateTime now, {
    bool isFuture = false,
  }) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;
    bool isCustomDisabled = isDateDisabled?.call(date) ?? false;
    bool isDisabled =
        isFuture || isCustomDisabled; //|| (isFutureDateHide && isToday);
    if (isDisabled) {
    bgColor = AppTheme.searchbarBg.withAlpha(80);
    textColor = AppTheme.tableDataFont.withAlpha(120);
  } else if (isSelected) {
    // Single date mode - only highlight the selected date
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isCustomRangeStart || isCustomRangeEnd) {
    // Custom range mode - highlight start/end dates
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isStartDate) {
    // Quick selection range mode - highlight start date
    bgColor = AppTheme.selectedComboBoxBorder;
    textColor = AppTheme.white;
  } else if (isInRange) {
    // Only show range highlight in custom range or quick selection modes
    bgColor = Colors.grey[350];
    textColor = AppTheme.black;
  } else if (isToday) {
    bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
    textColor = AppTheme.selectedComboBoxBorder;
  }
    return Material(
      color: Colors.transparent,
      child: InkWell(
        mouseCursor: isDisabled
            ? SystemMouseCursors.basic
            : SystemMouseCursors.click,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: isDisabled ? null : () => onDateSelection(date),
        child: Container(
          margin: const EdgeInsets.all(0.5), // Reduced margin for better fit
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(6), // Slightly smaller radius
            border: isToday && !isSelected && !isStartDate
                ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
                : null,
          ),
          child: Center(
            child: Text(
              date.day.toString(),
              style:
                  AppFonts.regularTextStyle(
                    12, // Reduced font size by 2px (was 14)
                    color: textColor,
                  ).copyWith(
                    fontWeight: isSelected || isToday || isStartDate
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    return Padding(
      padding: EdgeInsets.only(
        top: defaultPadding * 0.8, // More spacing from calendar
        left: defaultPadding * 0.3,
        right: defaultPadding * 0.3,
        bottom: defaultPadding * 0.6,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Full width selected date display button
          _buildMobileSelectedDisplayButton(),
          SizedBox(
            height: defaultPadding * 0.8,
          ), // More spacing between sections
          // Cancel and Apply buttons in a row
          Row(
            children: [
              Expanded(child: _buildMobileCancelButton()),
              SizedBox(width: defaultPadding * 0.6),
              Expanded(child: _buildMobileApplyButton()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      width: double.infinity, // Ensure full width
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.8,
        vertical: defaultPadding * 0.6,
      ),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.comboBoxBorder.withValues(alpha: 0.5),
        ),
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          13, // Slightly larger font
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildMobileCalanderButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
    Border? border,
    bool isActive = true, // Add isActive parameter with default true
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        mouseCursor: SystemMouseCursors.click,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: isActive ? onTap : null, // Only allow tap when active
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 0.6,
            vertical: defaultPadding * 0.5,
          ),
          decoration: BoxDecoration(
            color: isActive
                ? backgroundColor
                : backgroundColor.withValues(
                    alpha: 0.5,
                  ), // Apply opacity when inactive
            borderRadius: BorderRadius.circular(8),
            border: border,
          ),
          child: Center(
            child: Text(
              text,
              style: AppFonts.regularTextStyle(
                13,
                color: isActive
                    ? textColor
                    : textColor.withValues(
                        alpha: 0.7,
                      ), // Dimmed text when inactive
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCancelButton() {
    return _buildMobileCalanderButton(
      text: cancel,
      onTap: onCancel,
      backgroundColor: AppTheme.searchbarBg,
      textColor: AppTheme.black,
      border: Border.all(color: AppTheme.comboBoxBorder),
    );
  }

  Widget _buildMobileApplyButton() {
    final bool isActive = _hasUserInteracted();
    return _buildMobileCalanderButton(
      text: applyLabel,
      onTap: onApply,
      backgroundColor: AppTheme.selectedComboBoxBorder,
      textColor: AppTheme.white,
      isActive: isActive, // Pass the active state
    );
  }

  /// Determine if quick selection should be shown based on dateFilterMode
  bool _shouldShowQuickSelection() {
    // Show quick selection only for 'dateRange' and 'both' modes
    if (dateFilterMode == 'singleDate') {
      return false;
    }
    // For 'dateRange' and 'both' modes, show if options are available
    return quickSelectionOptions.isNotEmpty;
  }
}
