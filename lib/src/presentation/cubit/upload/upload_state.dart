import '../../../domain/models/upload_task_model.dart';

/// Base class for upload states
abstract class UploadState {
  const UploadState();
}

/// Initial state
class UploadInitial extends UploadState {}

/// State when a new task is added
class UploadTaskAdded extends UploadState {
  final UploadTask task;
  final List<UploadTask> allTasks;

  const UploadTaskAdded({required this.task, required this.allTasks});
}

/// State when a task is updated
class UploadTaskUpdated extends UploadState {
  final UploadTask task;
  final List<UploadTask> allTasks;

  const UploadTaskUpdated({required this.task, required this.allTasks});
}

/// State when a task is removed
class UploadTaskRemoved extends UploadState {
  final String taskId;
  final List<UploadTask> allTasks;

  const UploadTaskRemoved({required this.taskId, required this.allTasks});
}

/// State when tasks are cleared
class UploadTasksCleared extends UploadState {
  final List<UploadTask> allTasks;

  const UploadTasksCleared({required this.allTasks});
}

