import 'package:flutter/material.dart';
// ...existing code...
import 'package:flutter/services.dart';

/// Ensures the phone text always starts with [prefix] (can contain trailing space)
/// and only digits after it. Keeps cursor after the editable part.
class PhonePrefixFormatter extends TextInputFormatter {
  final String prefix;
  final int maxDigits;

  PhonePrefixFormatter(this.prefix, {this.maxDigits = 12});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String raw = newValue.text;

    // If user cleared everything, restore prefix
    if (raw.isEmpty) {
      final text = prefix;
      return TextEditingValue(
        text: text,
        selection: TextSelection.collapsed(offset: text.length),
      );
    }

    // Remove all non-digits (keep digits only) from the new value
    final digitsOnly = raw.replaceAll(RegExp(r'[^0-9]'), '');

    // If prefix contains digits (like "+1 "), extract its digit part to avoid duplication
    final prefixDigits = prefix.replaceAll(RegExp(r'[^0-9]'), '');

    String digits = digitsOnly;

    // If user typed the country code again, remove duplicate leading prefix digits
    if (prefixDigits.isNotEmpty && digits.startsWith(prefixDigits)) {
      digits = digits.substring(prefixDigits.length);
    }

    if (digits.length > maxDigits) {
      digits = digits.substring(0, maxDigits);
    }

    // Format as XXX-XXX-XXXX
    String formattedDigits = '';
    if (digits.isNotEmpty) {
      if (digits.length <= 3) {
        formattedDigits = digits;
      } else if (digits.length <= 6) {
        formattedDigits = '${digits.substring(0, 3)}-${digits.substring(3)}';
      } else {
        formattedDigits =
            '${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}';
      }
    }

    final resultText = '$prefix$formattedDigits';
    // final cursorPos = resultText.length;

    // final resultText = '$prefix$digits';
    // final cursorPos = (resultText.length).clamp(0, resultText.length);

    // Smart cursor positioning
    int cursorPos = _calculateCursorPosition(
      oldValue: oldValue,
      newValue: newValue,
      resultText: resultText,
      oldDigitsCount:
          oldValue.text.replaceAll(RegExp(r'[^0-9]'), '').length -
          prefixDigits.length,
      newDigitsCount: digits.length,
      prefix: prefix,
    );

    return TextEditingValue(
      text: resultText,
      selection: TextSelection.collapsed(offset: cursorPos),
    );
  }

  /// Calculates intelligent cursor position based on user action
  int _calculateCursorPosition({
    required TextEditingValue oldValue,
    required TextEditingValue newValue,
    required String resultText,
    required int oldDigitsCount,
    required int newDigitsCount,
    required String prefix,
  }) {
    // Get the old cursor position
    final oldCursor = oldValue.selection.baseOffset;
    final newCursor = newValue.selection.baseOffset;

    // Check if user had text selected (selection replacement scenario)
    final hadSelection = oldValue.selection.start != oldValue.selection.end;

    // If user selected all/most text and typed a single digit, place cursor after that digit
    if (hadSelection && newDigitsCount == 1) {
      return _findPositionAfterNDigits(resultText, 1, prefix);
    }

    // If user is at the start (in prefix area), keep them at end of prefix
    if (newCursor <= prefix.length) {
      return prefix.length;
    }

    // Calculate how many digits were before the cursor in old value
    final textBeforeCursor = oldValue.text.substring(0, oldCursor);
    final digitsBeforeCursor = textBeforeCursor
        .replaceAll(RegExp(r'[^0-9]'), '')
        .length;

    // Remove prefix digits from count
    final prefixDigits = prefix.replaceAll(RegExp(r'[^0-9]'), '');
    final adjustedDigitsBeforeCursor =
        (digitsBeforeCursor - prefixDigits.length).clamp(0, maxDigits);

    // If deleting (fewer digits now)
    if (newDigitsCount < oldDigitsCount) {
      // Position cursor after the same number of digits (accounting for new formatting)
      return _findPositionAfterNDigits(
        resultText,
        adjustedDigitsBeforeCursor - 1,
        prefix,
      );
    }
    // If adding (more digits now)
    else if (newDigitsCount > oldDigitsCount) {
      // Position cursor after one more digit
      return _findPositionAfterNDigits(
        resultText,
        adjustedDigitsBeforeCursor + 1,
        prefix,
      );
    }
    // Same number of digits (replacement)
    else {
      // Keep cursor at same logical position
      return _findPositionAfterNDigits(
        resultText,
        adjustedDigitsBeforeCursor,
        prefix,
      );
    }
  }

  /// Finds the position in formatted text after N digits
  int _findPositionAfterNDigits(
    String formattedText,
    int digitCount,
    String prefix,
  ) {
    if (digitCount < 0) {
      return prefix.length;
    }

    int digitsFound = 0;
    int position = prefix.length;

    while (position < formattedText.length && digitsFound < digitCount) {
      if (RegExp(r'[0-9]').hasMatch(formattedText[position])) {
        digitsFound++;
      }
      position++;
    }

    // Move past any non-digit character (like hyphen) that immediately follows
    while (position < formattedText.length &&
        !RegExp(r'[0-9]').hasMatch(formattedText[position])) {
      position++;
    }

    return position.clamp(prefix.length, formattedText.length);
  }
}
