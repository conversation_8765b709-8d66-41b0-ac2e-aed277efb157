import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/config/responsive.dart';
import '../../../../domain/models/top_performers.dart';

class SalesByBrokersCard extends HookWidget {
  final List<TopPerformers> brokersList;
  final List<TopPerformers> agentsList;
  final bool isBrokerView;
  static const List<Color> colorPalette = [
    Color(0xFF2196F3),
    Color(0xFFFF9800),
    Color(0xFF4CAF50),
    Color(0xFF9C27B0),
    Color(0xFFF44336),
    Color(0xFF009688),
    Color(0xFF3F51B5),
    Color(0xFF795548),
    Color(0xFF00BCD4),
    Color(0xFFFF5722),
    Color(0xFFE91E63),
    Color(0xFF607D8B),
    Color(0xFF8BC34A),
    Color(0xFFFFEB3B),
    Color(0xFF673AB7),
    Color(0xFF00E676),
    Color(0xFFFF6F00),
    Color(0xFF1DE9B6),
    Color(0xFF536DFE),
    Color(0xFFFF4081),
  ];

  const SalesByBrokersCard({
    super.key,
    required this.isBrokerView,
    required this.brokersList,
    required this.agentsList,
  });

  @override
  Widget build(BuildContext context) {
    final touchedIndex = useState<int>(-1);
    final chartSectionsCount = useState<int>(0);
    useEffect(() {
      chartSectionsCount.value = isBrokerView
          ? brokersList.length
          : agentsList.length;

      // Reset touched index if data becomes empty or if current index is out of bounds
      if (chartSectionsCount.value == 0 ||
          touchedIndex.value >= chartSectionsCount.value) {
        touchedIndex.value = -1;
      }
      return null;
    }, [isBrokerView, brokersList, agentsList]);

    final Size size = MediaQuery.of(context).size;
    final bool isTablet = Responsive.isTablet(context);
    final bool isEmpty = chartSectionsCount.value == 0;

    return Align(
      alignment: (isTablet && isEmpty) ? Alignment.center : Alignment.topCenter,
      child: Container(
        // constraints: BoxConstraints(
        //   minHeight:
        //       (MediaQuery.of(context).size.height * 0.75) +
        //       (8 * defaultPadding),
        // ),
        margin: isTablet
            ? EdgeInsets.fromLTRB(
                0,
                defaultPadding / 1.5,
                defaultPadding / 1.5,
                defaultPadding / 1.5,
              )
            : const EdgeInsets.fromLTRB(
                defaultPadding / 1.5,
                2,
                defaultPadding / 1.5,
                defaultPadding / 1.5,
              ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: isTablet
              ? LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            salesTab,
                            textAlign: TextAlign.center,
                            style: AppFonts.semiBoldTextStyle(
                              16,
                              color: AppTheme.primaryTextColor,
                            ),
                          ),
                        ),
                        if (isEmpty)
                          Expanded(child: _buildEmptyState())
                        else
                          Expanded(
                            child: Column(
                              crossAxisAlignment: (!_areAllSalesZero())
                                  ? CrossAxisAlignment.center
                                  : CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                if (!_areAllSalesZero())
                                  Flexible(
                                    flex: 2,
                                    child: _pieChart(
                                      context,
                                      size,
                                      touchedIndex,
                                      chartSectionsCount,
                                      constraints,
                                    ),
                                  ),
                                Flexible(
                                  flex: 3,
                                  child: _pieChartLegend(
                                    context,
                                    touchedIndex,
                                    chartSectionsCount,
                                    constraints,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    );
                  },
                )
              : Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        salesTab,
                        textAlign: TextAlign.center,
                        style: AppFonts.semiBoldTextStyle(
                          16,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: isEmpty
                          ? _buildEmptyState()
                          : LayoutBuilder(
                              builder: (context, constraints) {
                                return Responsive(
                                  smallMobile: _pieChartColumnView(
                                    context,
                                    size,
                                    touchedIndex,
                                    chartSectionsCount,
                                    constraints,
                                  ),
                                  mobile: Column(
                                    crossAxisAlignment: (!_areAllSalesZero())
                                        ? CrossAxisAlignment.center
                                        : CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      (!_areAllSalesZero())
                                          ? _pieChart(
                                              context,
                                              size,
                                              touchedIndex,
                                              chartSectionsCount,
                                              constraints,
                                            )
                                          : const SizedBox(),
                                      _pieChartLegend(
                                        context,
                                        touchedIndex,
                                        chartSectionsCount,
                                        constraints,
                                      ),
                                    ],
                                  ),
                                  tablet: Column(
                                    crossAxisAlignment: (!_areAllSalesZero())
                                        ? CrossAxisAlignment.center
                                        : CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      (!_areAllSalesZero())
                                          ? _pieChart(
                                              context,
                                              size,
                                              touchedIndex,
                                              chartSectionsCount,
                                              constraints,
                                            )
                                          : const SizedBox(),
                                      _pieChartLegend(
                                        context,
                                        touchedIndex,
                                        chartSectionsCount,
                                        constraints,
                                      ),
                                    ],
                                  ),
                                  desktop: _pieChartColumnView(
                                    context,
                                    size,
                                    touchedIndex,
                                    chartSectionsCount,
                                    constraints,
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart_outline,
            size: 48,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: defaultPadding),
          Padding(
            padding: const EdgeInsets.only(
              left: defaultPadding / 4,
              right: defaultPadding / 4,
            ),
            child: Text(
              'No sales data available for selected month',
              textAlign: TextAlign.center,
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.primaryTextColor.withValues(alpha: 0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // for desktop, tablet and small mobile views
  Widget _pieChartColumnView(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
    BoxConstraints parentConstraints,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!_areAllSalesZero())
          _pieChart(
            context,
            size,
            touchedIndex,
            chartSectionsCount,
            parentConstraints,
          ),
        _pieChartLegend(
          context,
          touchedIndex,
          chartSectionsCount,
          parentConstraints,
        ),
      ],
    );
  }

  Widget _pieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
    BoxConstraints parentConstraints,
  ) {
    final isTablet = Responsive.isTablet(context);
    final isDesktop = Responsive.isDesktop(context);

    return Flexible(
      flex: isTablet ? 1 : 2,
      fit: isTablet ? FlexFit.loose : FlexFit.tight,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final devicePixelRatio = View.of(context).devicePixelRatio;
          // Calculate legend height to reserve space
          final legendHeight = _calculateLegendHeight(
            context,
            chartSectionsCount.value,
            parentConstraints,
          );

          // Resolve a finite available height (guard for unbounded constraints)
          final parentMaxHeight = parentConstraints.maxHeight.isFinite
              ? parentConstraints.maxHeight
              : MediaQuery.of(context).size.height;

          // Available vertical space for pie chart (subtract legend + margins + padding)
          final availableHeight = (parentMaxHeight - legendHeight - 32).clamp(
            0.0,
            parentMaxHeight,
          );

          // Use constraints.maxWidth and availableHeight for sizing
          final maxDimension =
              constraints.maxWidth.isFinite && constraints.maxWidth > 0
              ? math.min(constraints.maxWidth, availableHeight)
              : availableHeight;

          // Calculate responsive size based on screen type
          double sizeFactor;
          if (isDesktop) {
            sizeFactor = 0.6; // Increased from 0.5
          } else if (isTablet) {
            sizeFactor = 0.7; // Increased from 0.6
          } else {
            sizeFactor = 0.75; // Increased from 0.65
          }

          // Adjust for zoom level - reduce size at high zoom levels (150%+)
          final zoomAdjustment = devicePixelRatio >= 1.5
              ? 1.0 -
                    (devicePixelRatio - 1.0) *
                        0.15 // Reduce size at high zoom
              : 1.0;

          final baseSize = maxDimension * sizeFactor * zoomAdjustment;

          // Clamp with respect to available height and ensure we never exceed the actual available area
          final minSize = isTablet
              ? 150.0
              : 120.0; // lower min on small screens
          final maxAllowedByLayout =
              availableHeight; // ensure chart + legend fit
          final maxSize = maxAllowedByLayout.clamp(minSize, 400.0);

          var chartSize = baseSize.clamp(minSize, maxSize);

          // final safety: if chart + legend still exceed parent, shrink chart to fit
          final totalNeeded = chartSize + legendHeight + 32; // paddings
          if (totalNeeded > parentMaxHeight) {
            chartSize = math.max(minSize, parentMaxHeight - legendHeight - 32);
          }

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: SizedBox(
              height: chartSize,
              child: Stack(
                children: [
                  _buildPieChart(
                    context,
                    size,
                    touchedIndex,
                    chartSectionsCount,
                    availableHeight,
                  ),
                  if (touchedIndex.value != -1) _chartBadge(touchedIndex),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  double _calculateLegendHeight(
    BuildContext context,
    int itemCount,
    BoxConstraints parentConstraints,
  ) {
    final isTablet = Responsive.isTablet(context);
    final isDesktop = Responsive.isDesktop(context);
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (isTablet) {
      return parentConstraints.maxHeight * 0.35;
    } else if (isDesktop) {
      return parentConstraints.maxHeight * 0.3;
    } else {
      // Mobile: estimate based on item count
      final crossAxisCount = isSmallMobile ? 1 : 2;
      final rows = (itemCount / crossAxisCount).ceil();
      final itemHeight = 45.0; // Approximate height per row
      return (rows * itemHeight + 40).clamp(180.0, 250.0);
    }
  }

  Widget _buildPieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
    double availableHeight,
  ) {
    return ValueListenableBuilder<int>(
      valueListenable: touchedIndex,
      builder: (context, touchedValue, child) {
        return PieChart(
          PieChartData(
            pieTouchData: PieTouchData(
              enabled: true,
              touchCallback: (FlTouchEvent event, pieTouchResponse) {
                //TODO: handle navigation to selected sales info.
                final hasSection =
                    pieTouchResponse != null &&
                    pieTouchResponse.touchedSection != null;
                if (hasSection) {
                  final sectionIndex =
                      pieTouchResponse.touchedSection!.touchedSectionIndex;
                  final currentList = isBrokerView ? brokersList : agentsList;
                  if (sectionIndex >= 0 && sectionIndex < currentList.length) {
                    touchedIndex.value = sectionIndex;
                  } else {
                    touchedIndex.value = -1;
                  }
                } else {
                  touchedIndex.value = -1;
                }
              },
            ),
            borderData: FlBorderData(show: false),
            sectionsSpace: 1.0,
            centerSpaceRadius: 0,
            sections: _showingSections(
              context,
              size,
              touchedIndex,
              chartSectionsCount,
              availableHeight,
            ),
          ),
          // animate radius changes (makes hover/touch enlargement smooth)
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
      },
    );
  }

  List<PieChartSectionData> _showingSections(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
    double availableHeight,
  ) {
    final currentList = isBrokerView ? brokersList : agentsList;

    // Return empty list if no data
    if (currentList.isEmpty || chartSectionsCount.value == 0) {
      return [];
    }
    final actualCount = currentList.length;
    return List.generate(actualCount, (i) {
      // Additional safety check for index bounds
      if (i >= currentList.length) return null;

      final isTouched = i == touchedIndex.value;
      final height = MediaQuery.of(context).size.height;
      final isDesktop = Responsive.isDesktop(context);

      // Calculate base radius with desktop boost
      double baseRadius;
      if (isDesktop) {
        // Desktop: adjusted to achieve target radius of 50
        baseRadius = (height <= 650)
            ? 62.5 // Calculated: 50 / 0.8 = 62.5 for target radius 50
            : 81.25; // For larger screens
      } else {
        // Other devices use original values
        baseRadius = (height <= 650) ? 50.0 : 60.0;
      }

      return PieChartSectionData(
        showTitle: false,
        borderSide: BorderSide.none,
        color: colorPalette[i % colorPalette.length],
        value: isBrokerView
            ? brokersList[i].monthlySalesCount.toDouble()
            : agentsList[i].monthlySalesCount.toDouble(),
        title: '', // Remove title text from pie chart
        radius: _scaledRadius(
          context,
          baseRadius,
          isTouched: isTouched,
          availableHeight: availableHeight,
        ),
        titleStyle: AppFonts.semiBoldTextStyle(0, color: Colors.white),
      );
    }).whereType<PieChartSectionData>().toList();
  }

  Widget _chartBadge(ValueNotifier<int> touchedIndex) {
    if (touchedIndex.value == -1) return const Positioned(child: SizedBox());

    // Safety check for empty lists
    final currentList = isBrokerView ? brokersList : agentsList;
    if (currentList.isEmpty || touchedIndex.value >= currentList.length) {
      return const Positioned(child: SizedBox());
    }

    final color = colorPalette[touchedIndex.value % colorPalette.length];

    final name = isBrokerView
        ? brokersList[touchedIndex.value].name
        : agentsList[touchedIndex.value].name;
    final sales = isBrokerView
        ? brokersList[touchedIndex.value].monthlySalesCount
        : agentsList[touchedIndex.value].monthlySalesCount;
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              name,
              style: AppFonts.semiBoldTextStyle(11, color: Colors.white),
            ),
            Text(
              '$sales $salesTab',
              style: AppFonts.semiBoldTextStyle(9, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pieChartLegend(
    BuildContext context,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
    BoxConstraints parentConstraints,
  ) {
    final size = MediaQuery.sizeOf(context);
    final isTablet = Responsive.isTablet(context);
    final isDesktop = Responsive.isDesktop(context);
    final isSmallMobile = Responsive.isSmallMobile(context);

    final devicePixelRatio = View.of(context).devicePixelRatio;

    final crossAxisCount = (size.width < 300 || chartSectionsCount.value == 1)
        ? 1
        : isSmallMobile
        ? 1
        : isTablet
        ? 2
        : 2;

    return Flexible(
      flex: isTablet ? 1 : 2,
      child: Container(
        margin: const EdgeInsets.fromLTRB(
          defaultMargin,
          defaultMargin / 2,
          defaultMargin,
          defaultMargin,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.salesLegendBorderColor, width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ValueListenableBuilder<int>(
            valueListenable: touchedIndex,
            builder: (context, touchedValue, child) {
              return GridView.builder(
                shrinkWrap: true,
                physics: const ClampingScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: getChartSectionItemAspectratio(
                    size,
                    devicePixelRatio,
                  ),
                ),
                itemCount: chartSectionsCount.value,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      touchedIndex.value = touchedIndex.value == index
                          ? -1
                          : index;
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      decoration: BoxDecoration(
                        color: touchedValue == index
                            ? colorPalette[index % colorPalette.length]
                                  .withAlpha(40)
                            : Colors.transparent,
                        border: Border(
                          bottom: _shouldShowBorder(index)
                              ? BorderSide(
                                  color: AppTheme.salesLegendBorderColor,
                                  width: 1,
                                )
                              : BorderSide.none,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          // color square
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            width: touchedValue == index ? 12 : 10,
                            height: touchedValue == index ? 12 : 10,
                            decoration: BoxDecoration(
                              color: colorPalette[index % colorPalette.length],
                              borderRadius: BorderRadius.circular(3),
                              boxShadow: touchedValue == index
                                  ? [
                                      BoxShadow(
                                        color:
                                            colorPalette[index %
                                                    colorPalette.length]
                                                .withAlpha(100),
                                        blurRadius: 4,
                                        spreadRadius: 1,
                                      ),
                                    ]
                                  : null,
                            ),
                          ),
                          const SizedBox(width: 8),
                          _legendUserInfo(
                            index,
                            isDesktop || isTablet,
                            touchedIndex,
                            isSmallMobile,
                            isTablet,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _legendUserInfo(
    int index,
    bool isDesktop,
    ValueNotifier<int> touchedIndex,
    bool isSmallMobile,
    bool isTablet,
  ) {
    // Safety check for empty lists
    final currentList = isBrokerView ? brokersList : agentsList;
    if (currentList.isEmpty || index >= currentList.length) {
      return const Expanded(child: SizedBox());
    }

    final name = isBrokerView
        ? brokersList[index].name
        : agentsList[index].name;
    final sales = isBrokerView
        ? brokersList[index].monthlySalesCount
        : agentsList[index].monthlySalesCount;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              name,
              style: AppFonts.mediumTextStyle(
                isDesktop || isSmallMobile
                    ? 12
                    : isTablet
                    ? 12
                    : 10,
                color: AppTheme.primaryTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              '$sales $salesTab',
              style: AppFonts.mediumTextStyle(
                isDesktop || isSmallMobile
                    ? 10
                    : isTablet
                    ? 10
                    : 7,
                color: AppTheme.breadcrumbArrowColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _shouldShowBorder(int index) {
    final chartSectionsCount = isBrokerView
        ? brokersList.length
        : agentsList.length;

    // If only one item, always show border
    if (chartSectionsCount <= 2) {
      return true;
    }

    // For 2-column layout: Calculate the row index (0-based)
    final rowIndex = index ~/ 2;

    // Draw border if not the last row
    if (rowIndex < (chartSectionsCount / 2).ceil() - 1) {
      return true;
    }

    // Otherwise, the last item in the last row (no border)
    return false;
  }

  bool _areAllSalesZero() {
    final currentList = isBrokerView ? brokersList : agentsList;
    if (currentList.isEmpty) return true;
    return currentList.every((item) => item.monthlySalesCount == 0);
  }
}

double _scaledRadius(
  BuildContext context,
  double baseRadius, {
  required bool isTouched,
  double? availableHeight,
}) {
  // Safety check for invalid baseRadius
  if (baseRadius.isNaN || baseRadius.isInfinite || baseRadius <= 0) {
    baseRadius = 50.0; // fallback value
  }
  final devicePixelRatio = View.of(context).devicePixelRatio;
  final isTablet = Responsive.isTablet(context);
  final isDesktop = Responsive.isDesktop(context);
  final isSmallMobile = Responsive.isSmallMobile(context);
  final screenHeight = MediaQuery.of(context).size.height;
  final zoomPercent = (devicePixelRatio * 100).round();
  if (zoomPercent < 90) {
    // Size-relative mapping for low zoom levels (avoid hardcoded absolute pixels)
    // Use a reference radius derived from availableHeight (if provided) or screen height.
    final double reference = (availableHeight != null && availableHeight > 0)
        ? (availableHeight / 2)
        : (screenHeight * 0.35);

    // Use a single, consistent multiplier for all low-zoom values (45% of reference)
    // per request: "consider for all z with 45%"
    final double multiplier = 0.55;

    // Compute forced radius from reference and cap to available layout
    final double cap = (availableHeight != null && availableHeight > 0)
        ? (availableHeight / 2) * 0.9
        : double.infinity;
    double forcedRadius = reference * multiplier;
    if (cap.isFinite && cap > 0) forcedRadius = math.min(forcedRadius, cap);

    // preserve small touch boost
    final double touchBoost = isTouched ? (isDesktop ? 8.0 : 6.0) : 0.0;
    final result = (forcedRadius + touchBoost).clamp(
      30.0,
      cap.isFinite ? cap : double.infinity,
    );
    return result;
  }
  // Compute a conservative max allowed radius based on availableHeight or heuristics
  double maxAllowedRadius;
  if (availableHeight != null) {
    maxAllowedRadius =
        (availableHeight / 2) *
        0.75; // Reduced to allow more room for expansion
  } else {
    final chartAreaHeight = isTablet ? screenHeight * 0.35 : screenHeight * 0.3;
    maxAllowedRadius = (chartAreaHeight / 2) * 0.75;
  }

  // Density factor adjustment - reduce at high zoom levels
  final densityFactor = isTablet
      ? (devicePixelRatio >= 1.5 ? 0.9 : 1.0) // Reduce tablet size at high zoom
      : devicePixelRatio >= 1.5
      ? (devicePixelRatio.clamp(1.0, 2.5) / 3.0).clamp(
          0.6,
          1.0,
        ) // More reduction for mobile at high zoom
      : (devicePixelRatio.clamp(1.0, 2.5) / 2.0).clamp(0.8, 1.2);
  // Moderate touch/hover boost with zoom-aware scaling
  double touchedBoost;
  if (isDesktop) {
    // Desktop: Boost that adapts to density factor for consistent visibility
    if (devicePixelRatio >= 1.5) {
      touchedBoost = 8.0; // Moderate boost at high zoom
    } else if (devicePixelRatio >= 1.25) {
      touchedBoost = 7.0; // Medium boost at medium zoom
    } else {
      // For normal zoom, provide a subtle but visible boost
      touchedBoost =
          4.0; // Moderate 4px boost for clear but not excessive effect
    }
  } else if (isTablet) {
    touchedBoost = devicePixelRatio >= 1.5 ? 6.0 : 4.0;
  } else {
    touchedBoost = devicePixelRatio >= 1.5 ? 5.0 : 4.0;
  }
  // Base computed radius (without boost)
  double computedBase = baseRadius * densityFactor;

  // Safety check for computed values
  if (computedBase.isNaN || computedBase.isInfinite || computedBase <= 0) {
    computedBase = 50.0; // fallback value
  }

  // Calculate base final radius first (without touch boost)
  double baseFinalRadius = computedBase;

  // Apply hard limit to base radius
  if (baseFinalRadius > 90.0) {
    baseFinalRadius = (availableHeight! > 130) ? 70.0 : 48.0;
  }

  // Now add touch boost after applying the limit
  double finalRadius = baseFinalRadius + (isTouched ? touchedBoost : 0.0);

  print(
    "availableHeight: $availableHeight densityFactor : $densityFactor devicePixelRatio: $devicePixelRatio touchedBoost : $touchedBoost finalRadius: $finalRadius",
  );
  // Safety check for final radius
  if (finalRadius.isNaN || finalRadius.isInfinite || finalRadius <= 0) {
    finalRadius = isTouched ? 55.0 : 50.0; // fallback values
  }

  // Don't clamp here - let device-specific logic handle it to preserve hover effect

  // Device-specific minimum and maximum values with proper hover enlargement
  if (isDesktop) {
    // Desktop: Use the calculated finalRadius directly and ensure proper clamping
    double minRadius, maxRadius;

    if (zoomPercent >= 150) {
      // High zoom: allow smaller range but preserve difference
      minRadius = 30.0;
      maxRadius = math.max(80.0, maxAllowedRadius);
    } else if (zoomPercent >= 125) {
      // Medium zoom: moderate range
      minRadius = 35.0;
      maxRadius = math.max(90.0, maxAllowedRadius);
    } else {
      // Normal zoom: lower minimum to allow smaller base radii
      minRadius = 40.0; // Reduced from 50.0 to 40.0
      maxRadius = math.max(100.0, maxAllowedRadius);
    }

    // Add height-based constraint to prevent overlapping
    if (availableHeight != null && availableHeight > 0) {
      // Ensure radius doesn't exceed 35% of available height for better spacing
      final heightConstrainedMax = availableHeight * 0.35;
      maxRadius = math.min(maxRadius, heightConstrainedMax);
    }

    // Ensure minRadius is never larger than maxRadius to prevent clamp errors
    if (minRadius > maxRadius) {
      minRadius = maxRadius * 0.8; // Set minRadius to 80% of maxRadius
    }

    // Ensure the touched state can be larger but not excessively so
    if (isTouched) {
      // Allow only a small expansion beyond the height constraint
      maxRadius = math.max(
        maxRadius,
        math.min(finalRadius, maxRadius + touchedBoost),
      );
    }

    final clampedResult = finalRadius.clamp(minRadius, maxRadius);

    // Debug output to verify the values
    print(
      "Desktop: isTouched=$isTouched, finalRadius=$finalRadius, clampedResult=$clampedResult, min=$minRadius, max=$maxRadius, availableHeight=$availableHeight",
    );

    return clampedResult;
  } else if (isTablet) {
    final minRadius = isTouched ? 50.0 : 45.0; // Increased from 35.0/30.0
    final maxRadius = isTouched ? 75.0 : 65.0; // Increased from 60.0/50.0
    return finalRadius.clamp(minRadius, maxRadius);
  } else if (isSmallMobile) {
    final minRadius = isTouched ? 45.0 : 40.0; // Increased from 30.0/25.0
    final maxRadius = isTouched ? 65.0 : 55.0; // Increased from 50.0/40.0
    return finalRadius.clamp(minRadius, maxRadius);
  } else {
    final minRadius = isTouched ? 50.0 : 45.0; // Increased from 35.0/30.0
    final maxRadius = isTouched ? 70.0 : 60.0; // Increased from 55.0/45.0
    return finalRadius.clamp(minRadius, maxRadius);
  }
}

double getChartSectionItemAspectratio(Size size, double devicePixelRatio) {
  final width = size.width;

  // Adjust aspect ratio based on zoom level
  final zoomMultiplier = devicePixelRatio > 0.7 ? 1.25 : 1.4;
  if (width <= 500) {
    return 5.5 * zoomMultiplier;
  } else if (width > 500 && width <= 700) {
    return 5 * zoomMultiplier;
  } else if (width > 700 && width <= 780) {
    return 5.0 * zoomMultiplier;
  } else if (width > 780 && width < 800) {
    return 7 * zoomMultiplier;
  } else if (width > 800 && width <= 1000) {
    return 3.5 * zoomMultiplier;
  } else if (width > 1000 && width <= 1200) {
    return 4 * zoomMultiplier;
  } else if (width > 1200 && width <= 1600) {
    return 2 * zoomMultiplier;
  } else if (width > 1600) {
    return 3.5 * zoomMultiplier;
  }
  return 5.0 * zoomMultiplier; // Default callback
}

bool isCustomTablet(Size size) {
  return size.width >= 500 &&
      size.width < 1200 &&
      size.height >= 400 &&
      size.height < 800;
}

double getResponsiveRadius({
  required BuildContext context,
  required bool isTouched,
  required bool isTablet,
  double? availableHeight, // Add this parameter to pass from _pieChart
}) {
  final devicePixelRatio = View.of(context).devicePixelRatio;
  final isDesktop = Responsive.isDesktop(context);
  final isSmallMobile = Responsive.isSmallMobile(context);
  final screenWidth = MediaQuery.of(context).size.width;
  final screenHeight = MediaQuery.of(context).size.height;
  // Calculate maximum allowed radius based on available space
  // The diameter should fit within the chart container
  double maxAllowedRadius;

  if (availableHeight != null) {
    // Use the actual available height from the chart widget
    // Divide by 2 to get radius, and add some padding (0.9 factor)
    maxAllowedRadius = (availableHeight / 2) * 0.85;
  } else {
    // Fallback: estimate based on screen dimensions
    final chartAreaHeight = isTablet ? screenHeight * 0.35 : screenHeight * 0.3;
    maxAllowedRadius = (chartAreaHeight / 2) * 0.85;
  }

  // Calculate base radius based on available screen space
  double baseRadius;

  if (isDesktop) {
    baseRadius = (screenWidth * 0.08).clamp(50.0, 80.0);
  } else if (isTablet) {
    // For problematic dimensions like 1257x442
    if (screenHeight < 600) {
      // Width-constrained landscape tablets
      baseRadius = (screenHeight * 0.15).clamp(30.0, 50.0);
    } else {
      final smallerDimension = screenWidth < screenHeight
          ? screenWidth
          : screenHeight;
      baseRadius = (smallerDimension * 0.12).clamp(40.0, 65.0);
    }
  } else if (isSmallMobile) {
    baseRadius = (screenWidth * 0.20).clamp(35.0, 55.0);
  } else {
    // For problematic dimensions like 721x572
    if (screenWidth < 800 && screenHeight < 650) {
      // Compact mobile screens
      baseRadius = (screenHeight * 0.12).clamp(30.0, 45.0);
    } else {
      baseRadius = (screenWidth * 0.18).clamp(30.0, 60.0);
    }
  }

  // Apply density factor (reduced impact at high zoom)
  double densityFactor = isTablet
      ? (devicePixelRatio >= 1.5
            ? 0.85
            : 1.0) // Reduce tablet size at high zoom
      : devicePixelRatio >= 1.5
      ? (devicePixelRatio.clamp(1.0, 3.0) / 3.5).clamp(
          0.6,
          1.0,
        ) // More reduction at high zoom
      : (devicePixelRatio.clamp(1.0, 3.0) / 2.0).clamp(0.8, 1.3);

  // Subtle touch boost
  final touchedBoost = isDesktop
      ? 7.0
      : isTablet
      ? 8.0
      : 6.0;

  // Calculate radius with density and touch boost
  double radius = (baseRadius * densityFactor) + (isTouched ? touchedBoost : 0);

  // CRITICAL: Ensure radius doesn't exceed available space
  radius = radius.clamp(0, maxAllowedRadius);
  // Final clamps based on device type (but never exceed maxAllowedRadius)
  if (isDesktop) {
    return radius.clamp(55.0, maxAllowedRadius.clamp(55.0, 120.0));
  } else if (isTablet) {
    return radius.clamp(30.0, maxAllowedRadius.clamp(30.0, 75.0));
  } else if (isSmallMobile) {
    return radius.clamp(25.0, maxAllowedRadius.clamp(25.0, 65.0));
  } else {
    return radius.clamp(30.0, maxAllowedRadius.clamp(30.0, 80.0));
  }
}
