import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;

/// Helper class for web-specific navigation operations
class WebNavigationHelper {
  /// Clears the browser history and navigates to a specific path
  /// This is useful for logout scenarios where we don't want users
  /// to navigate back to authenticated pages
  static void clearHistoryAndNavigate(String path) {
    if (kIsWeb) {
      try {
        // Replace the current history state with the login page
        // This prevents the back button from going to previous authenticated pages
        html.window.history.replaceState(null, '', path);
        
        // Clear forward history by pushing a new state
        html.window.history.pushState(null, '', path);
        
        debugPrint('WebNavigationHelper: History cleared and navigated to $path');
      } catch (e) {
        debugPrint('WebNavigationHelper: Error clearing history: $e');
      }
    }
  }

  /// Prevents back navigation by replacing the current state
  static void preventBackNavigation(String currentPath) {
    if (kIsWeb) {
      try {
        // Listen to popstate events (back/forward button)
        html.window.onPopState.listen((event) {
          // Replace the state to prevent going back
          html.window.history.pushState(null, '', currentPath);
        });
      } catch (e) {
        debugPrint('WebNavigationHelper: Error preventing back navigation: $e');
      }
    }
  }

  /// Clears all browser storage (localStorage, sessionStorage, cookies)
  static Future<void> clearBrowserStorage() async {
    if (kIsWeb) {
      try {
        html.window.localStorage.clear();
        html.window.sessionStorage.clear();
        debugPrint('WebNavigationHelper: Browser storage cleared');
      } catch (e) {
        debugPrint('WebNavigationHelper: Error clearing browser storage: $e');
      }
    }
  }
}

