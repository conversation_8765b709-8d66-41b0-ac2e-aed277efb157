import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:universal_html/html.dart' as html;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/enum/user_role.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../domain/models/user.dart';
import '../../cubit/user/user_cubit.dart';
import '../../../core/utils/phone_number_formatter.dart';

import 'dart:ui' as ui;

class UserProfileScreen extends HookWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        if (state is UserLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is UserError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 60, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  state.message,
                  style: AppFonts.regularTextStyle(16, color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<UserCubit>().getUserProfile();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is UserLoaded && state.user != null) {
          final user = state.user!;
          return SingleChildScrollView(
            child: Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: isMobile ? double.infinity : 700,
                ),
                padding: EdgeInsets.all(
                  isMobile ? defaultPadding : defaultPadding * 2,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.roundIconColor,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _formHeader(context, isMobile, user),
                      _formContent(context, isMobile, user),
                    ],
                  ),
                ),
              ),
            ),
          );
        }

        return const Center(child: Text('No user data available'));
      },
    );
  }

  // Form Header with Curved Bottom
  Widget _formHeader(BuildContext context, bool isMobile, User user) {
    // Format joining date
    String formattedDate = 'N/A';
    if (user.joiningDate != null) {
      formattedDate = AppDateFormatter.formatDateMMddyyyy(user.joiningDate);
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Blue Header Container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(defaultPadding * 1.4),
          decoration: BoxDecoration(
            color: AppTheme.roundIconColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Profile Picture
              Container(
                width: isMobile ? 80 : 120,
                height: isMobile ? 80 : 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: user.avatarUrl != null && user.avatarUrl.isNotEmpty
                      ? Image.network(
                          user.avatarUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultAvatar(isMobile);
                          },
                        )
                      : _buildDefaultAvatar(isMobile),
                ),
              ),
              SizedBox(width: isMobile ? defaultPadding : defaultPadding * 1.5),

              // User Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // User Name
                    Text(
                      '${user.firstName} ${user.lastName}',
                      style: AppFonts.semiBoldTextStyle(
                        isMobile ? 20 : 24,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Role Badge
                    Row(
                      children: [
                        Image.asset(
                          '$iconAssetpath/form_user.png',
                          width: isMobile ? 10 : 14,
                          height: isMobile ? 10 : 14,
                          color: AppTheme.white,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Role: ',
                          style: AppFonts.regularTextStyle(
                            isMobile ? 13 : 14,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          user.roleName ?? 'N/A',
                          style: AppFonts.semiBoldTextStyle(
                            isMobile ? 13 : 15,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Join Date
                    Row(
                      children: [
                        Image.asset(
                          '$iconAssetpath/form_calendar.png',
                          width: isMobile ? 10 : 14,
                          height: isMobile ? 10 : 14,
                          color: AppTheme.white,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Join Date: ',
                          style: AppFonts.regularTextStyle(
                            isMobile ? 13 : 14,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          formattedDate,
                          style: AppFonts.semiBoldTextStyle(
                            isMobile ? 13 : 15,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultAvatar(bool isMobile) {
    return Container(
      color: Colors.white,
      child: Icon(
        Icons.person,
        size: isMobile ? 50 : 60,
        color: AppTheme.roundIconColor,
      ),
    );
  }

  // Form Content with White Background
  Widget _formContent(BuildContext context, bool isMobile, User user) {
    // Create a list of all available fields
    List<Widget> fieldWidgets = [];

    // Add Referral Code if available
    if (user.referralCode != null &&
        user.referralCode!.isNotEmpty &&
        user.role != UserRole.admin &&
        user.role != UserRole.platformOwner) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/ico-referral.png',
          label: 'Referral Code',
          value: user.referralCode!,
          showCopyButton: true,
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Add Referred By if available
    if (user.referredBy != null && user.referredBy!.isNotEmpty) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/fi-rr-user.png',
          label: 'Referred By',
          value: user.referredBy!,
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Add Phone if available
    if (user.phone != null && user.phone!.isNotEmpty) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/form_phone.png',
          label: 'Phone',
          value: PhoneNumberFormatter.formatPhoneNumber(user.phone!),
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Add Email if available
    if (user.email != null && user.email!.isNotEmpty) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/email.png',
          label: 'Email',
          value: user.email!,
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Add State if available
    if (user.state != null && user.state!.isNotEmpty) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/fi-rr-map-marker-home.png',
          label: 'State',
          value: user.state!,
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Add City if available
    if (user.city != null && user.city!.isNotEmpty) {
      fieldWidgets.add(
        _buildInfoCard(
          imagePath: '$iconAssetpath/fi-rr-map-marker.png',
          label: 'City',
          value: user.city!,
          isMobile: isMobile,
          context: context,
        ),
      );
    }

    // Check if logo exist
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(25)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(
          left: defaultPadding * 2,
          right: defaultPadding * 2,
          top: defaultPadding * 1.2,
          bottom: defaultPadding * 2,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Brokerage Info (Logo)
            if (user.logo != null && user.logo.isNotEmpty) ...[
              _buildSectionCard(logoUrl: user.logo, isMobile: isMobile),
              const SizedBox(height: defaultPadding),
            ],
            // Grid layout for all fields
            if (fieldWidgets.isNotEmpty)
              isMobile
                  ? Column(
                      children: fieldWidgets
                          .map(
                            (widget) => Padding(
                              padding: const EdgeInsets.only(
                                bottom: defaultPadding,
                              ),
                              child: widget,
                            ),
                          )
                          .toList(),
                    )
                  : _buildTwoColumnGrid(fieldWidgets),

            const SizedBox(height: defaultPadding),
          ],
        ),
      ),
    );
  }

  // Helper method to build 2-column grid
  Widget _buildTwoColumnGrid(List<Widget> widgets) {
    List<Widget> rows = [];

    for (int i = 0; i < widgets.length; i += 2) {
      if (i + 1 < widgets.length) {
        // Two items in this row
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: defaultPadding),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: widgets[i]),
                const SizedBox(width: defaultPadding),
                Expanded(child: widgets[i + 1]),
              ],
            ),
          ),
        );
      } else {
        // Only one item in this row (odd number of widgets)
        rows.add(
          Padding(
            padding: const EdgeInsets.only(bottom: defaultPadding),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: widgets[i]),
                const SizedBox(width: defaultPadding),
                Expanded(child: SizedBox.shrink()), // Empty space
              ],
            ),
          ),
        );
      }
    }

    return Column(children: rows);
  }

  Widget _buildSectionCard({required String logoUrl, required bool isMobile}) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: _buildImageFromBase64OrUrl(
          logoUrl,
          width: isMobile ? 120 : 272,
          height: isMobile ? 30 : 60,
        ),
      ),
    );
  }

  Widget _buildImageFromBase64OrUrl(
    String imageData, {
    required double width,
    required double height,
  }) {
    try {
      // Check if it's a Base64 string
      if (imageData.startsWith('data:image')) {
        // Remove the data:image/xxx;base64, prefix if present
        final base64String = imageData.split(',').last;
        final Uint8List bytes = base64Decode(base64String);

        return Image.memory(
          bytes,
          width: width,
          height: height,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return SizedBox.shrink();
          },
        );
      } else if (imageData.contains('base64') || _isBase64(imageData)) {
        // Handle pure Base64 without prefix
        final Uint8List bytes = base64Decode(imageData);

        return Image.memory(
          bytes,
          width: width,
          height: height,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return SizedBox.shrink();
          },
        );
      } else {
        // Assume it's a regular URL
        return Image.network(
          imageData,
          width: width,
          height: height,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return SizedBox.shrink();
          },
        );
      }
    } catch (e) {
      // If any error occurs, return an empty widget
      return SizedBox.shrink();
    }
  }

  // Helper method to check if string is valid Base64
  bool _isBase64(String str) {
    try {
      base64Decode(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  Widget _buildInfoCard({
    required String imagePath,
    required String label,
    required String value,
    bool showCopyButton = false,
    required bool isMobile,
    required BuildContext context,
  }) {
    return Container(
      // padding: EdgeInsets.all(isMobile ? defaultPadding : defaultPadding * 1.2),
      padding: EdgeInsets.symmetric(
        horizontal: showCopyButton
            ? (isMobile ? 8 : 10) // Reduced padding when copy button is present
            : (isMobile ? 12 : 16), // Normal padding
        vertical: showCopyButton
            ? (isMobile ? 8 : 8) // Reduced padding when copy button is present
            : (isMobile ? 10 : 12), // Normal padding
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: AppTheme.borderColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label Row (without copy button)
          Row(
            children: [
              Image.asset(
                imagePath,
                width: isMobile ? 12 : 16,
                height: isMobile ? 14 : 26,
                color: AppTheme.ternaryTextColor,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  label,
                  style: AppFonts.regularTextStyle(
                    isMobile ? 12 : 14,
                    color: AppTheme.secondaryTextColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Value Row (with copy button immediately after text)
          Row(
            children: [
              // Text(
              //   value,
              //   style: AppFonts.semiBoldTextStyle(
              //     (isMobile ? 13 : 16) + (showCopyButton ? 2 : 0),
              //     color: AppTheme.primaryTextColor,
              //   ),
              // ),
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final style = AppFonts.semiBoldTextStyle(
                      (isMobile ? 13 : 16) + (showCopyButton ? 2 : 0),
                      color: AppTheme.primaryTextColor,
                    );

                    // Account for copy button width
                    final availableWidth =
                        constraints.maxWidth - (showCopyButton ? 30 : 0);

                    // Create TextPainter to check if text will be truncated
                    final textPainter = TextPainter(
                      text: TextSpan(text: value, style: style),
                      maxLines: 1,
                      textDirection: ui.TextDirection.ltr,
                    )..layout(maxWidth: availableWidth);

                    // Check if text exceeds the allowed space
                    final bool needsTooltip = textPainter.didExceedMaxLines;
                    String displayText = value;

                    if (needsTooltip) {
                      // Calculate how many characters fit
                      final safeValue = value.replaceAll('-', '\u2011');
                      int truncateAt = safeValue.length;
                      for (int i = value.length; i > 0; i--) {
                        final testPainter = TextPainter(
                          text: TextSpan(
                            text: '${value.substring(0, i)}...',
                            style: style,
                          ),
                          maxLines: 1,
                          textDirection: ui.TextDirection.ltr,
                        )..layout();

                        if (testPainter.width <= availableWidth - 10) {
                          truncateAt = i;
                          break;
                        }
                      }
                      displayText = '${safeValue.substring(0, truncateAt)}...';
                    }

                    final textWidget = Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        displayText.replaceAll('-', '\u2011'),
                        style: style,
                        maxLines: 1,
                      ),
                    );

                    // Only wrap with Tooltip if text is overflowing
                    return needsTooltip
                        ? Tooltip(
                            richMessage: WidgetSpan(
                              child: ConstrainedBox(
                                constraints: const BoxConstraints(
                                  maxWidth: 250,
                                ),
                                child: Text(
                                  value,
                                  style: AppFonts.regularTextStyle(
                                    13,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            padding: const EdgeInsets.all(10),
                            child: textWidget,
                          )
                        : textWidget;
                  },
                ),
              ),
              if (showCopyButton) ...[
                const SizedBox(width: 8),
                IconButton(
                  iconSize: isMobile ? 12 : 14,
                  padding: const EdgeInsets.all(6),
                  constraints:
                      const BoxConstraints(), // removes default min size
                  icon: Image.asset(
                    '$iconAssetpath/fi-rr-copy-alt.png',
                    width: isMobile ? 12 : 14,
                    height: isMobile ? 12 : 14,
                    color: AppTheme.secondaryTextColor,
                  ),
                  onPressed: () async {
                    // Copy to clipboard
                    try {
                      // Try Flutter clipboard first
                      await Clipboard.setData(ClipboardData(text: value));
                    } catch (e) {
                      // Fallback: Works on Windows Web
                      html.window.navigator.clipboard?.writeText(value);
                    }

                    // Show snackbar confirmation
                    AppSnackBar.showSnackBar(
                      context,
                      'Referral code copied to clipboard!',
                      SnackBarType.info,
                    );
                  },
                  tooltip: 'Copy',
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
