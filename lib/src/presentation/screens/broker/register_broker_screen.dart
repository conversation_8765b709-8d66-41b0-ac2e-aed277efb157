
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/utils/regex.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/network/api_consts.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/combined_dropdown_state.dart';
import '../../../core/utils/helper.dart';
import '../../../core/utils/phone_number_formatter.dart';
import '../../../core/utils/preview_files.dart';
import '../../../core/utils/success_snack_bar.dart';
import '../../../core/utils/whitespace_formatter.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../../domain/models/user.dart';
import '../../../domain/repository/broker_register_repository.dart';
import '../../cubit/broker_register/broker_register_cubit.dart';
import '../../cubit/filter/filter_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/custom_dropdown_button2.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/customDialogues/alert_dialogue.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import 'package:file_picker/file_picker.dart';

import '../../shared/components/phone_prefix_formatter.dart';

class RegisterBrokerScreen extends HookWidget {
  RegisterBrokerScreen({super.key});

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController companyController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  // File variables for document uploads
  final ValueNotifier<PlatformFile?> eoInsuranceFile = ValueNotifier(null);
  final ValueNotifier<PlatformFile?> brokerageLicenseFile = ValueNotifier(null);
  final ValueNotifier<PlatformFile?> principalBrokerIdFile = ValueNotifier(
    null,
  );
  final ValueNotifier<PlatformFile?> logoFile = ValueNotifier(null);

  // GlobalKeys for dropdown fields
  final GlobalKey _cityFieldKey = GlobalKey();
  final GlobalKey _stateFieldKey = GlobalKey();
  final GlobalKey _countryFieldKey = GlobalKey();

  // ValueNotifiers for dropdown options and selected IDs
  final ValueNotifier<List<TableFilter>> countryOptions = ValueNotifier([]);
  final ValueNotifier<List<TableFilter>> stateOptions = ValueNotifier([]);
  final ValueNotifier<List<TableFilter>> cityOptions = ValueNotifier([]);

  // Store selected IDs (needed for API calls)
  final ValueNotifier<TableFilter?> selectedCountry = ValueNotifier(null);
  final ValueNotifier<TableFilter?> selectedState = ValueNotifier(null);
  final ValueNotifier<TableFilter?> selectedCity = ValueNotifier(null);

  final TextEditingController _locationSearchController =
      TextEditingController();

  ValueListenable<CombinedDropdownState> _countryDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));
  ValueListenable<CombinedDropdownState> _stateDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));
  ValueListenable<CombinedDropdownState> _cityDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));

  final nameInputFormatter = FilteringTextInputFormatter.allow(
    RegExp(r"[\p{L}\s.'\-]", unicode: true),
  );

  final ValueNotifier<bool> showFirstNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showLastNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showEmailHint = ValueNotifier(false);
  final ValueNotifier<bool> companyNameHint = ValueNotifier(false);

  void _initializeCombinedListenables() {
    _countryDropdownState = _createCombinedListenable(
      countryOptions,
      selectedCountry,
    );
    _stateDropdownState = _createCombinedListenable(
      stateOptions,
      selectedState,
    );
    _cityDropdownState = _createCombinedListenable(cityOptions, selectedCity);
  }

  ValueListenable<CombinedDropdownState> _createCombinedListenable(
    ValueListenable<List<TableFilter>> optionsNotifier,
    ValueListenable<TableFilter?> selectedNotifier,
  ) {
    final combined = ValueNotifier<CombinedDropdownState>(
      CombinedDropdownState(optionsNotifier.value, selectedNotifier.value),
    );

    void updateCombined() {
      combined.value = CombinedDropdownState(
        optionsNotifier.value,
        selectedNotifier.value,
      );
    }

    optionsNotifier.addListener(updateCombined);
    selectedNotifier.addListener(updateCombined);

    return combined;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    final user = context.watch<UserCubit>().state.user;
    // form should be reset/cleared when reopened,
    // regardless of whether the form was previously filled and not submitted.
    // if want to persist the data remove the useEffect
    useEffect(() {
      _initializeCombinedListenables();
      _setupAdditionalInfoListener();
      _clearForm();
      _loadCountries(context);
      return null;
    }, []);

    /// TODO: Remove later
    /*final header = Header(selectedTab: '');

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: defaultPadding),
            // Static header (remains visible when scrolling)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: webLayoutmargin),
              child: header,
            ),
            SizedBox(height: defaultPadding / 2),

            // The rest of the content is scrollable
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: _formWidget(context, size, isMobile),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );*/
    // return _formWidget(context, size, isMobile);
    return BlocConsumer<BrokerRegisterCubit, BrokerRegisterState>(
      listener: (context, state) {
        // if (state is BrokerRegisterError) {
        // } else if (state is BrokerRegisterSuccess) {
        //   _clearForm();
        // }
      },
      builder: (context, state) {
        return _formWidget(context, size, isMobile, state, user);
      },
    );
  }

  void _setupAdditionalInfoListener() {
    firstNameController.addListener(() {
      showFirstNameHint.value =
          firstNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    lastNameController.addListener(() {
      showLastNameHint.value =
          lastNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    emailController.addListener(() {
      showEmailHint.value =
          emailController.text.length >= APIConsts.emailCharacterLimit;
    });

    companyController.addListener(() {
      companyNameHint.value =
          companyController.text.length >= APIConsts.companyNameCharacterLimit;
    });
  }

  Widget _formWidget(
    BuildContext context,
    Size size,
    bool isMobile,
    BrokerRegisterState state,
    User? user,
  ) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return Container(
          // width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage('$imageAssetpath/register_bg.png'),
            ),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  /// TODO: Remove later
                  /*  BreadCrumbNavigation(
                    hierarchyPath: [
                      AppStrings.dashboardAdmin,
                      AppStrings.addNewBroker,
                    ],
                    onNavigate: (int navigationIndex) {},
                  ),*/
                  SizedBox(height: defaultPadding),
                  Container(
                    decoration: BoxDecoration(
                      color: AppTheme.roundIconColor,
                      borderRadius: BorderRadius.circular(25),
                    ),
                    margin: EdgeInsets.symmetric(
                      horizontal: isMobile
                          ? 0 // defaultPadding * 2.5
                          : selectedIndex.value == 0
                          ? size.width / 7
                          : size.width < desktopBreakpoint
                          ? size.width / 5
                          : size.width / 3,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header with title and buttons
                        _formHeader(context),
                        // SizedBox(height: defaultPadding / 2),
                        // Main form content
                        _formContent(isMobile, context, state, user),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: selectedIndex.value == 0 ? defaultPadding * 2 : 0,
                  ),

                  // Spacer(),
                ],
              ),
              state is BrokerRegisterLoading
                  ? Positioned.fill(
                      child: Container(
                        color: Colors.transparent,
                        child: Center(child: CircularProgressIndicator()),
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      // width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding,
      ),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(25)),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: defaultPadding * 1.5),
            Text(
              AppStrings.registerBroker,
              style: AppFonts.semiBoldTextStyle(22, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding * 1.5),
          ],
        ),
      ),
    );
  }

  Future<void> _loadCountries(BuildContext context) async {
    final filterCubit = context.read<FilterCubit>();
    await filterCubit.getCountiresFilterOptions();

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      countryOptions.value = state.filterOptions;
    }
  }

  Future<void> _loadStates(BuildContext context, String? countryId) async {
    final filterCubit = context.read<FilterCubit>();

    // Clear dependent fields only if country is different
    final isDifferentCountry = selectedCountry.value?.id != countryId;
    if (isDifferentCountry) {
      stateController.clear();
      cityController.clear();
      stateOptions.value = [];
      cityOptions.value = [];
      selectedState.value = null;
      selectedCity.value = null;
    }

    if (countryId != null) {
      await filterCubit.getStatesFilterOptions(selectedCountry: countryId);
    } else {
      // Get all states without country filter
      await filterCubit.getStatesFilterOptions();
    }

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      stateOptions.value = state.filterOptions;
    }
  }

  Future<void> _loadCities(
    BuildContext context,
    String stateId, {
    String? searchText,
  }) async {
    final filterCubit = context.read<FilterCubit>();

    // Clear dependent field only if state is different
    final isDifferentState = selectedState.value?.id != stateId;
    if (isDifferentState && searchText == null) {
      cityController.clear();
      cityOptions.value = [];
      selectedCity.value = null;
    }

    await filterCubit.getCitiesFilterOptions(
      selectedState: stateId,
      searchString: searchText,
    );

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      cityOptions.value = state.filterOptions;
    }
  }

  Future<void> _onLocationSelected(
    BuildContext context,
    String fieldLabel,
    TextEditingController controller,
    TableFilter selected,
  ) async {
    controller.text = selected.value;

    if (fieldLabel == AppStrings.country) {
      final oldCountryId = selectedCountry.value?.id;
      selectedCountry.value = selected;

      // Load states for the selected country
      await _loadStates(context, selected.id);

      // Check if current state belongs to this country
      if (selectedState.value != null) {
        final stateExists = stateOptions.value.any(
          (state) => state.id == selectedState.value?.id,
        );

        if (!stateExists) {
          // Current state doesn't belong to this country, clear state and city
          stateController.clear();
          selectedState.value = null;
          cityController.clear();
          cityOptions.value = [];
          selectedCity.value = null;
        } else if (selectedCity.value != null) {
          // State exists in this country, now check if city exists in the state
          // Reload cities to ensure we have the correct list
          await _loadCities(context, selectedState.value!.id!);
          final cityExists = cityOptions.value.any(
            (city) => city.id == selectedCity.value?.id,
          );

          if (!cityExists) {
            // City doesn't belong to this state, clear only city
            cityController.clear();
            selectedCity.value = null;
          }
        }
      }
    } else if (fieldLabel == AppStrings.stateProvince) {
      final oldStateId = selectedState.value?.id;
      selectedState.value = selected;

      // Load cities for the selected state
      await _loadCities(context, selected.id!);

      // If state changed, always clear city
      if (oldStateId != selected.id) {
        cityController.clear();
        selectedCity.value = null;
      } else {
        // State is same, check if current city belongs to this state
        if (selectedCity.value != null) {
          final cityExists = cityOptions.value.any(
            (city) => city.id == selectedCity.value?.id,
          );
          if (!cityExists) {
            cityController.clear();
            selectedCity.value = null;
          }
        }
      }
    } else if (fieldLabel == AppStrings.city) {
      selectedCity.value = selected;
    }
  }

  Widget _buildCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required int characterLimit,
  }) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        width: double.infinity,
        child: ValueListenableBuilder<bool>(
          valueListenable: showHint,
          builder: (context, show, _) {
            if (!show) return const SizedBox.shrink();
            return Padding(
              padding: const EdgeInsets.only(top: 4, left: 14),
              child: Text(
                'Max $characterLimit characters allowed',
                style: AppFonts.regularTextStyle(
                  12,
                  color: AppTheme.warningColor,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmailCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required TextEditingController controller,
    required int characterLimit,
  }) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        width: double.infinity,
        child: ValueListenableBuilder<bool>(
          valueListenable: showEmailHint,
          builder: (context, show, _) {
            if (!show) return const SizedBox.shrink();
            // Check if there's a validation error
            final emailText = emailController.text;
            final hasValidationError =
                InputValidators.validateEmail(emailText) != null;

            // Don't show warning if validation error exists
            if (hasValidationError) return const SizedBox.shrink();
            return Padding(
              padding: const EdgeInsets.only(top: 4, left: 14),
              child: Text(
                'Max ${APIConsts.emailCharacterLimit} characters allowed',
                textAlign: TextAlign.start,
                style: AppFonts.regularTextStyle(
                  12,
                  color: AppTheme.warningColor,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLabelWithInfo(
    String label, {
    bool isMandatory = false,
    String? tooltipMessage,
  }) {
    return Row(
      children: [
        _buildFormLabel(label, isMandatory: isMandatory),
        if (tooltipMessage != null) ...[
          const SizedBox(width: 4),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: Tooltip(
              richMessage: WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 250),
                  child: Text(
                    tooltipMessage,
                    style: AppFonts.regularTextStyle(12, color: Colors.white),
                    softWrap: true,
                    maxLines: 4,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              preferBelow: false,
              verticalOffset: 0,
              showDuration: const Duration(seconds: 4),
              waitDuration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Container _formContent(
    bool isMobile,
    BuildContext context,
    BrokerRegisterState state,
    User? user,
  ) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isMobile ? defaultPadding : defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          children: [
            Responsive(
              mobile: _buildMobileLayout(context),
              desktop: _buildDesktopLayout(context),
            ),
            SizedBox(height: defaultPadding * 2),
            _actionButtons(context, state, user),
          ],
        ),
      ),
    );
  }

  _countryListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _countryDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'country_empty'),
          hint: AppStrings.enterCountry,
          items: state.options,
          selectedValue: state.selected?.id,
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.country,
                countryController,
                selected,
              );
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            return null;
          },
          enableSearch: true,
          isRequired: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
        );
      },
    );
  }

  _stateListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _stateDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'state_empty'),
          hint: AppStrings.enterState,
          items: state.options,
          selectedValue: state.selected?.id,
          onBeforeOpen: () {
            if (selectedCountry.value == null) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.pleaseSelectCountryFirst,
                SnackBarType.warning,
              );
              return;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.stateProvince,
                stateController,
                selected,
              );
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.stateProvince} is required";
            }
            return null;
          },
          enableSearch: true,
          isRequired: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
        );
      },
    );
  }

  _cityListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _cityDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'city_empty'),
          hint: AppStrings.enterCity,
          items: state.options,
          selectedItem: state.selected,
          selectedValue: state.selected?.id,
          onBeforeOpen: () {
            if (selectedState.value == null) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.pleaseSelectStateFirst,
                SnackBarType.warning,
              );
              return;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.city,
                cityController,
                selected,
              );
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.city} is required";
            }
            return null;
          },
          enableSearch: true,
          isRequired: true,
          useApiSearch: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
          onSearchChanged: (searchValue) async {
            if (selectedState.value?.id != null) {
              // Only call API if search text has 2 or more characters
              await _loadCities(
                context,
                selectedState.value!.id!,
                searchText: searchValue,
              );
            }
          },
        );
      },
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    bool isRegisterTab = selectedIndex.value == 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isRegisterTab) ...[
          // Broker Information Section
          _fieldHead(AppStrings.brokerInformation, 18),
          const SizedBox(height: defaultPadding * 2),
        ],
        // First Name
        _buildFormLabel(AppStrings.firstName, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          firstNameController,
          AppStrings.enterFirstName,
          AppStrings.firstName,
          isMandatory: true,
          validator: (value) =>
              InputValidators.validateName(value, AppStrings.firstName),
          inputFormatters: [
            nameInputFormatter,
            LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
          ],
        ),
        _buildCharacterLimitHint(
          showHint: showFirstNameHint,
          characterLimit: APIConsts.nameCharacterLimit,
        ),
        const SizedBox(height: defaultPadding),

        // Last Name
        _buildFormLabel(AppStrings.lastName, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          lastNameController,
          AppStrings.enterLastName,
          AppStrings.lastName,
          isMandatory: true,
          validator: (value) =>
              InputValidators.validateName(value, AppStrings.lastName),
          inputFormatters: [
            nameInputFormatter,
            LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
          ],
        ),
        _buildCharacterLimitHint(
          showHint: showLastNameHint,
          characterLimit: APIConsts.nameCharacterLimit,
        ),
        const SizedBox(height: defaultPadding),

        // Phone
        _buildFormLabel(AppStrings.phone, isMandatory: true),
        const SizedBox(height: 8),
        _phoneNumTextFormField(),
        const SizedBox(height: defaultPadding),

        // Email
        _buildFormLabel(AppStrings.email, isMandatory: true),
        const SizedBox(height: 8),
        _emailTextFormField(),
        _buildEmailCharacterLimitHint(
          showHint: showEmailHint,
          controller: emailController,
          characterLimit: APIConsts.emailCharacterLimit,
        ),
        const SizedBox(height: defaultPadding),
        if (isRegisterTab) ...[
          // Company
          _buildFormLabel(AppStrings.company, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            companyController,
            AppStrings.enterCompany,
            AppStrings.company,
            isMandatory: true,
            validator: (value) => InputValidators.validateCompanyName(
              value,
              fieldLabel: AppStrings.company,
              limit: 100,
            ),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExUtils.companyNameRegex),
              LengthLimitingTextInputFormatter(
                APIConsts.companyNameCharacterLimit,
              ),
            ],
          ),
          _buildCharacterLimitHint(
            showHint: companyNameHint,
            characterLimit: APIConsts.companyNameCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.country, isMandatory: true),
          const SizedBox(height: 8),
          _countryListenableBuilder(),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
          const SizedBox(height: 8),
          _stateListenableBuilder(),
          const SizedBox(height: defaultPadding),

          _buildLabelWithInfo(
            AppStrings.city,
            isMandatory: true,
            tooltipMessage: AppStrings.cityTooltipRegForm,
          ),
          const SizedBox(height: 8),
          _cityListenableBuilder(),

          // Postal/Zip Code
          _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            postalCodeController,
            AppStrings.postalCodeEg,
            AppStrings.postalZipCode,
            isMandatory: true,
            validator: (value) => InputValidators.validateZipCode(value),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9-]')),
            ],
          ),
          const SizedBox(height: defaultPadding),

          // Upload Documents Section
          _fieldHead(AppStrings.uploadDocuments, 18),

          const SizedBox(height: defaultPadding * 2),

          // E&O Insurance Certificate
          _buildFormLabel(AppStrings.eoInsuranceCertificate),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
            eoInsuranceFile,
            APIConsts.allowedFileExtensions,
            context,
          ),
          const SizedBox(height: defaultPadding),

          // Brokerage License
          _buildFormLabel(AppStrings.brokerageLicense),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
            brokerageLicenseFile,
            APIConsts.allowedFileExtensions,
            context,
          ),
          const SizedBox(height: defaultPadding),

          // Principal Broker ID
          _buildFormLabel(AppStrings.principalBrokerId),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseFileOrDragDrop,
            AppStrings.pdfOrImageOnly,
            principalBrokerIdFile,
            APIConsts.allowedFileExtensions,
            context,
          ),
          const SizedBox(height: defaultPadding),

          // Logo
          _buildFormLabel(AppStrings.logo),
          const SizedBox(height: 8),
          _buildUploadField(
            AppStrings.chooseImageOrDragDrop,
            AppStrings.imageFormatsOnly,
            logoFile,
            APIConsts.imageExtensionsAllowed,
            context,
          ),
        ],
        const SizedBox(height: defaultPadding * 2),
      ],
    );
  }

  Widget _emailTextFormField() {
    return _buildTextFormField(
      emailController,
      AppStrings.enterEmail,
      AppStrings.email,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
      inputFormatters: [
        LengthLimitingTextInputFormatter(APIConsts.emailCharacterLimit),
      ],
    );
  }

  Widget _phoneNumTextFormField() {
    final String phonePrefix = '+1 ';
    // Prefill the controller with prefix if main phone and empty
    if (phoneController.text.isEmpty) {
      phoneController.text = phonePrefix;
      phoneController.selection = TextSelection.fromPosition(
        TextPosition(offset: phoneController.text.length),
      );
    }
    return _buildTextFormField(
      phoneController,
      AppStrings.enterPhone,
      AppStrings.phone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        PhonePrefixFormatter(
          phonePrefix,
          maxDigits: APIConsts.phoneCharacterLimit,
        ),
        LengthLimitingTextInputFormatter(
          phonePrefix.length + APIConsts.phoneCharacterLimit,
        ),
      ],
      validator: (value) {
        // strip prefix before validating
        final stripped = value?.replaceFirst(phonePrefix, '') ?? '';
        return InputValidators.validatePhone(stripped);
      },
      prefixText: null,
    );
  }

  Widget _fieldHead(String title, double fontSize) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: AppFonts.semiBoldTextStyle(
        fontSize,
        color: AppTheme.primaryTextColor,
      ),
    );
  }

  Widget _buildDropDownField(
    TextEditingController controller,
    String hintText,
    String fieldLabel, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    required BuildContext context,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFormLabel(fieldLabel, isMandatory: isMandatory),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          readOnly: true,
          key: fieldLabel == AppStrings.city
              ? _cityFieldKey
              : fieldLabel == AppStrings.stateProvince
              ? _stateFieldKey
              : fieldLabel == AppStrings.country
              ? _countryFieldKey
              : null,
          onTap: () => _showLocationDropdown(context, fieldLabel, controller),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            hintText: hintText,
            suffixIcon: Icon(
              Icons.arrow_drop_down,
              color: AppTheme.primaryTextColor,
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(
              vertical: defaultPadding,
              horizontal: defaultPadding,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: BorderSide(color: AppTheme.textFieldBorder),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: BorderSide(color: AppTheme.textFieldBorder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: const BorderSide(
                color: AppTheme.primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
          ),
          validator: validator,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
        ),
      ],
    );
  }

  void _showLocationDropdown(
    BuildContext context,
    String fieldLabel,
    TextEditingController controller,
  ) async {
    final RenderBox? renderBox =
        (fieldLabel == AppStrings.city
                    ? _cityFieldKey
                    : fieldLabel == AppStrings.stateProvince
                    ? _stateFieldKey
                    : _countryFieldKey)
                .currentContext
                ?.findRenderObject()
            as RenderBox?;

    if (renderBox == null) return;

    final Size size = renderBox.size;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    // Load options based on field type
    List<TableFilter> options = [];

    if (fieldLabel == AppStrings.country) {
      await _loadCountries(context);
      options = countryOptions.value;
    } else if (fieldLabel == AppStrings.stateProvince) {
      if (selectedCountry.value != null) {
        options = stateOptions.value;
      } else {
        final filterCubit = context.read<FilterCubit>();
        await filterCubit.getStatesFilterOptions();
        if (filterCubit.state is FilterLoaded) {
          final state = filterCubit.state as FilterLoaded;
          stateOptions.value = state.filterOptions;
          options = state.filterOptions;
        }
      }
    } else if (fieldLabel == AppStrings.city) {
      if (selectedState.value == null) {
        AppSnackBar.showSnackBar(
          context,
          AppStrings.pleaseSelectStateFirst,
          SnackBarType.warning,
        );
        return;
      }
      options = cityOptions.value;
    }

    if (options.isEmpty) {
      AppSnackBar.showSnackBar(
        context,
        'No options available for $fieldLabel',
        SnackBarType.warning,
      );
      return;
    }

    // Clear search controller
    _locationSearchController.clear();

    // Create a ValueNotifier for filtered options
    final filteredOptions = ValueNotifier<List<TableFilter>>(options);

    // Get currently selected ID for highlighting
    String? currentSelectedId;
    if (fieldLabel == AppStrings.country) {
      currentSelectedId = selectedCountry.value?.id;
    } else if (fieldLabel == AppStrings.stateProvince) {
      currentSelectedId = selectedState.value?.id;
    } else if (fieldLabel == AppStrings.city) {
      currentSelectedId = selectedCity.value?.id;
    }

    // Find the index of the selected item
    final selectedIndex = options.indexWhere(
      (option) => option.id == currentSelectedId,
    );

    final selected = await showMenu<TableFilter>(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy + size.height,
        offset.dx + size.width,
        0,
      ),
      color: AppTheme.white,
      items: [
        PopupMenuItem<TableFilter>(
          enabled: false,
          padding: EdgeInsets.zero,
          child: StatefulBuilder(
            builder: (context, setState) {
              return Container(
                width: size.width,
                color: AppTheme.white,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Search Field
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _locationSearchController,
                        decoration: InputDecoration(
                          hintText: 'Search...',
                          hintStyle: AppFonts.regularTextStyle(
                            14,
                            color: AppTheme.primaryTextColor,
                          ),
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 8,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: AppTheme.primaryTextColor,
                            size: 20,
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            if (value.isEmpty) {
                              filteredOptions.value = options;
                            } else {
                              filteredOptions.value = options
                                  .where(
                                    (option) => option.value
                                        .toLowerCase()
                                        .contains(value.toLowerCase()),
                                  )
                                  .toList();
                            }
                          });
                        },
                      ),
                    ),
                    // Options List with ScrollConfiguration
                    ValueListenableBuilder<List<TableFilter>>(
                      valueListenable: filteredOptions,
                      builder: (context, filtered, _) {
                        // Create scroll controller
                        final scrollController = ScrollController();
                        final selectedIndex = filtered.indexWhere(
                          (option) => option.id == currentSelectedId,
                        );
                        if (selectedIndex != -1) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (scrollController.hasClients) {
                              // Constants
                              const itemHeight =
                                  56.0; // Height of each ListTile
                              const viewportHeight =
                                  200.0; // Max height of dropdown

                              // Calculate offset to center the selected item
                              double targetOffset =
                                  (selectedIndex * itemHeight) -
                                  (viewportHeight / 2) +
                                  (itemHeight / 2);

                              // Ensure offset is within bounds
                              targetOffset = targetOffset.clamp(
                                0.0, // Min scroll
                                max(
                                  0.0,
                                  (filtered.length * itemHeight) -
                                      viewportHeight,
                                ), // Max scroll
                              );

                              // Animate to the calculated position
                              scrollController.animateTo(
                                targetOffset,
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeOutCubic,
                              );
                            }
                          });
                        }

                        return ScrollConfiguration(
                          behavior: ScrollConfiguration.of(
                            context,
                          ).copyWith(scrollbars: true),
                          child: Scrollbar(
                            controller: scrollController,
                            thumbVisibility: true, // Always show scrollbar
                            thickness: 6,
                            radius: const Radius.circular(4),
                            child: Container(
                              constraints: const BoxConstraints(maxHeight: 200),
                              child: filtered.isEmpty
                                  ? Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Text(
                                        noDataFound,
                                        style: AppFonts.regularTextStyle(
                                          14,
                                          color: AppTheme.ternaryTextColor,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    )
                                  : ListView.builder(
                                      controller: scrollController,
                                      shrinkWrap: true,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0,
                                        vertical: 4.0,
                                      ),
                                      itemCount: filtered.length,
                                      itemBuilder: (context, index) {
                                        final option = filtered[index];
                                        final isSelected =
                                            option.id == currentSelectedId;
                                        if (isSelected) {
                                          WidgetsBinding.instance.addPostFrameCallback((
                                            _,
                                          ) {
                                            if (scrollController.hasClients) {
                                              // Calculate the target offset to center the selected item
                                              final itemHeight =
                                                  56.0; // Height of each ListTile
                                              final viewportHeight =
                                                  200.0; // Max height of dropdown

                                              // Calculate offset to center the selected item
                                              double targetOffset =
                                                  (index * itemHeight) -
                                                  (viewportHeight / 2) +
                                                  (itemHeight / 2);

                                              // Ensure offset is within bounds
                                              targetOffset = targetOffset.clamp(
                                                0.0, // Min scroll
                                                max(
                                                  0.0,
                                                  (filtered.length *
                                                          itemHeight) -
                                                      viewportHeight,
                                                ), // Max scroll
                                              );

                                              // Animate to the calculated position
                                              scrollController.animateTo(
                                                targetOffset,
                                                duration: const Duration(
                                                  milliseconds: 300,
                                                ),
                                                curve: Curves.easeOutCubic,
                                              );
                                            }
                                          });
                                        }
                                        return ListTile(
                                          title: Text(
                                            option.value,
                                            style: AppFonts.regularTextStyle(
                                              14,
                                              color: isSelected
                                                  ? AppTheme.primaryColor
                                                  : AppTheme.primaryTextColor,
                                            ),
                                          ),
                                          tileColor: isSelected
                                              ? AppTheme.primaryColor
                                                    .withOpacity(0.1)
                                              : null,
                                          hoverColor: Colors.blueGrey.shade100,
                                          onTap: () {
                                            Navigator.pop(context, option);
                                          },
                                        );
                                      },
                                    ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );

    if (selected != null) {
      controller.text = selected.value;

      if (fieldLabel == AppStrings.country) {
        final oldCountryId = selectedCountry.value?.id;
        selectedCountry.value = selected;

        // Load states for the selected country
        await _loadStates(context, selected.id);

        // Check if current state belongs to this country
        if (selectedState.value != null) {
          final stateExists = stateOptions.value.any(
            (state) => state.id == selectedState.value?.id,
          );

          if (!stateExists) {
            // Current state doesn't belong to this country, clear state and city
            stateController.clear();
            selectedState.value = null;
            cityController.clear();
            cityOptions.value = [];
            selectedCity.value = null;
          } else if (selectedCity.value != null) {
            // State exists in this country, now check if city exists in the state
            // Reload cities to ensure we have the correct list
            await _loadCities(context, selectedState.value!.id!);
            final cityExists = cityOptions.value.any(
              (city) => city.id == selectedCity.value?.id,
            );

            if (!cityExists) {
              // City doesn't belong to this state, clear only city
              cityController.clear();
              selectedCity.value = null;
            }
          }
        }
      } else if (fieldLabel == AppStrings.stateProvince) {
        final oldStateId = selectedState.value?.id;
        selectedState.value = selected;

        // Load cities for the selected state
        await _loadCities(context, selected.id!);

        // If state changed, always clear city
        if (oldStateId != selected.id) {
          cityController.clear();
          selectedCity.value = null;
        } else {
          // State is same, check if current city belongs to this state
          if (selectedCity.value != null) {
            final cityExists = cityOptions.value.any(
              (city) => city.id == selectedCity.value?.id,
            );
            if (!cityExists) {
              cityController.clear();
              selectedCity.value = null;
            }
          }
        }
      } else if (fieldLabel == AppStrings.city) {
        selectedCity.value = selected;
      }
    }
  }

  Widget _buildDesktopLayout(BuildContext context) {
    bool isRegisterTab = selectedIndex.value == 0;
    final isTablet = Responsive.isTablet(context);

    return isTablet
        ? // Tablet layout - single column like mobile but with better spacing
          _buildMobileLayout(context)
        : // Desktop layout - two columns
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left Column - Broker Information
              Expanded(
                flex: isRegisterTab ? 1 : 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (isRegisterTab) ...[
                      _fieldHead(AppStrings.brokerInformation, 18),
                      const SizedBox(height: defaultPadding * 2),
                    ],

                    // First Name and Last Name
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildFormLabel(
                                AppStrings.firstName,
                                isMandatory: true,
                              ),
                              const SizedBox(height: 8),
                              _buildTextFormField(
                                firstNameController,
                                AppStrings.enterFirstName,
                                AppStrings.firstName,
                                isMandatory: true,
                                validator: (value) =>
                                    InputValidators.validateName(
                                      value,
                                      AppStrings.firstName,
                                    ),
                                inputFormatters: [
                                  nameInputFormatter,
                                  LengthLimitingTextInputFormatter(
                                    APIConsts.nameCharacterLimit,
                                  ),
                                ],
                              ),
                              _buildCharacterLimitHint(
                                showHint: showFirstNameHint,
                                characterLimit: APIConsts.nameCharacterLimit,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildFormLabel(
                                AppStrings.lastName,
                                isMandatory: true,
                              ),
                              const SizedBox(height: 8),
                              _buildTextFormField(
                                lastNameController,
                                AppStrings.enterLastName,
                                AppStrings.lastName,
                                isMandatory: true,
                                validator: (value) =>
                                    InputValidators.validateName(
                                      value,
                                      AppStrings.lastName,
                                    ),
                                inputFormatters: [
                                  nameInputFormatter,
                                  LengthLimitingTextInputFormatter(
                                    APIConsts.nameCharacterLimit,
                                  ),
                                ],
                              ),
                              _buildCharacterLimitHint(
                                showHint: showLastNameHint,
                                characterLimit: APIConsts.nameCharacterLimit,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: defaultPadding),

                    // Phone
                    _buildFormLabel(AppStrings.phone, isMandatory: true),
                    const SizedBox(height: 8),
                    _phoneNumTextFormField(),
                    const SizedBox(height: defaultPadding),

                    // Email
                    _buildFormLabel(AppStrings.email, isMandatory: true),
                    const SizedBox(height: 8),
                    _emailTextFormField(),
                    _buildEmailCharacterLimitHint(
                      showHint: showEmailHint,
                      controller: emailController,
                      characterLimit: APIConsts.emailCharacterLimit,
                    ),
                    const SizedBox(height: defaultPadding),

                    if (isRegisterTab) ...[
                      // Company
                      _buildFormLabel(AppStrings.company, isMandatory: true),
                      const SizedBox(height: 8),
                      _buildTextFormField(
                        companyController,
                        AppStrings.enterCompany,
                        AppStrings.company,
                        isMandatory: true,
                        validator: (value) =>
                            InputValidators.validateCompanyName(
                              value,
                              fieldLabel: AppStrings.company,
                              limit: 100,
                            ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExUtils.companyNameRegex,
                          ),
                          LengthLimitingTextInputFormatter(
                            APIConsts.companyNameCharacterLimit,
                          ),
                        ],
                      ),
                      _buildCharacterLimitHint(
                        showHint: companyNameHint,
                        characterLimit: APIConsts.companyNameCharacterLimit,
                      ),
                      const SizedBox(height: defaultPadding),
                      // Country and State row
                      _buildCountryStateRowDesktop(),
                      const SizedBox(height: defaultPadding),
                      // City and Postal Code row
                      _buildCityPostalCodeRowDesktop(),
                    ],
                  ],
                ),
              ),

              if (isRegisterTab) ...[
                SizedBox(
                  width:
                      defaultPadding * (Responsive.isDesktop(context) ? 2 : 1),
                ),
                // Right Column - Upload Documents
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _fieldHead(AppStrings.uploadDocuments, 18),
                      const SizedBox(height: defaultPadding * 2),

                      // E&O Insurance Certificate
                      _buildFormLabel(AppStrings.eoInsuranceCertificate),
                      const SizedBox(height: 8),
                      _buildUploadField(
                        AppStrings.chooseFileOrDragDrop,
                        AppStrings.pdfOrImageOnly,
                        eoInsuranceFile,
                        APIConsts.allowedFileExtensions,
                        context,
                      ),
                      const SizedBox(height: defaultPadding),

                      // Brokerage License
                      _buildFormLabel(AppStrings.brokerageLicense),
                      const SizedBox(height: 8),
                      _buildUploadField(
                        AppStrings.chooseFileOrDragDrop,
                        AppStrings.pdfOrImageOnly,
                        brokerageLicenseFile,
                        APIConsts.allowedFileExtensions,
                        context,
                      ),
                      const SizedBox(height: defaultPadding),

                      // Principal Broker ID
                      _buildFormLabel(AppStrings.principalBrokerId),
                      const SizedBox(height: 8),
                      _buildUploadField(
                        AppStrings.chooseFileOrDragDrop,
                        AppStrings.pdfOrImageOnly,
                        principalBrokerIdFile,
                        APIConsts.allowedFileExtensions,
                        context,
                      ),
                      const SizedBox(height: defaultPadding),

                      // Logo
                      _buildFormLabel(AppStrings.logo),
                      const SizedBox(height: 8),
                      _buildUploadField(
                        AppStrings.chooseImageOrDragDrop,
                        AppStrings.imageFormatsOnly,
                        logoFile,
                        APIConsts.imageExtensionsAllowed,
                        context,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          );
  }

  Row _buildCountryStateRowDesktop() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.country, isMandatory: true),
              const SizedBox(height: 8),
              _countryListenableBuilder(),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
              const SizedBox(height: 8),
              _stateListenableBuilder(),
            ],
          ),
        ),
      ],
    );
  }

  Row _buildCityPostalCodeRowDesktop() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLabelWithInfo(
                AppStrings.city,
                isMandatory: true,
                tooltipMessage: AppStrings.cityTooltipRegForm,
              ),
              const SizedBox(height: 8),
              _cityListenableBuilder(),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                postalCodeController,
                AppStrings.postalCodeEg,
                AppStrings.postalZipCode,
                isMandatory: true,
                validator: (value) => InputValidators.validateZipCode(value),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9-]')),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController, {
    String? Function(String?)? validatorValueLeftController,
    String? Function(String?)? validatorValueRightController,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                leftController,
                (leftController == cityController)
                    ? AppStrings.enterCity
                    : (leftController == postalCodeController)
                    ? postalCodeEg
                    : '',
                leftLabel,
                // (leftController == postalCodeController) ? postalCodeEg : '',
                isMandatory: true,
                validator: validatorValueLeftController,
                inputFormatters: (leftController == postalCodeController)
                    ? [FilteringTextInputFormatter.allow(RegExp(r'[0-9-]'))]
                    : [],
              ),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                rightController,
                (rightController == stateController)
                    ? AppStrings.enterState
                    : (rightController == countryController)
                    ? AppStrings.enterCountry
                    : '',
                rightLabel,
                isMandatory: true,
                validator: validatorValueRightController,
                inputFormatters: (rightController == countryController)
                    ? [FilteringTextInputFormatter.deny(RegExp(r'[0-9]'))]
                    : [],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context, BrokerRegisterState state, user) {
    final isSmallMobile = Responsive.isSmallMobile(context);

    return isSmallMobile
        ? Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: AppButton(
                  label: AppStrings.clear,
                  backgroundColor: AppTheme.scaffoldBgColor,
                  foregroundColor: AppTheme.primaryTextColor,
                  borderRadius: isSmallMobile ? 50 : 25,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding * 2,
                    vertical: isSmallMobile
                        ? defaultPadding
                        : defaultPadding / 2,
                  ),
                  onPressed: () => _showClearDataAlert(context),
                ),
              ),
              const SizedBox(height: defaultPadding),
              SizedBox(
                width: double.infinity,
                child: AppButton(
                  label: AppStrings.register,
                  backgroundColor: AppTheme.roundIconColor,
                  foregroundColor: Colors.white,
                  borderRadius: isSmallMobile ? 50 : 25,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding * 2,
                    vertical: isSmallMobile
                        ? defaultPadding
                        : defaultPadding / 2,
                  ),
                  onPressed: () => _submitForm(context, user),
                ),
              ),
            ],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AppButton(
                label: AppStrings.clear,
                backgroundColor: AppTheme.scaffoldBgColor,
                foregroundColor: AppTheme.primaryTextColor,
                borderRadius: 25,
                padding: const EdgeInsets.symmetric(
                  horizontal: defaultPadding * 2.5,
                  vertical: defaultPadding / 2,
                ),
                onPressed: () => _showClearDataAlert(context),
              ),
              const SizedBox(width: defaultPadding),
              AppButton(
                label: AppStrings.register,
                backgroundColor: AppTheme.roundIconColor,
                foregroundColor: Colors.white,
                borderRadius: 25,
                padding: const EdgeInsets.symmetric(
                  horizontal: defaultPadding * 2,
                  vertical: defaultPadding / 2,
                ),
                onPressed: () => _submitForm(context, user),
              ),
            ],
          );
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText,
    String fieldLabel, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? prefixText,
  }) {
    // Create base formatters list
    List<TextInputFormatter> finalFormatters = [];

    if (controller != phoneController ||
        controller != emailController ||
        controller != postalCodeController) {
      finalFormatters.add(WhitespaceFormatter());
    }

    // Add any additional formatters passed as parameter
    if (inputFormatters != null) {
      finalFormatters.addAll(inputFormatters);
    }

    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      // validator: validator,
      validator: (value) {
        if (isMandatory) {
          if (value == null || value.isEmpty) {
            // Use fieldLabel instead of hintText
            return '$fieldLabel is required';
          } else if (value.trim().isEmpty) {
            return AppStrings.whiteSpaceValidation;
          }
        }
        if (validator != null) {
          return validator(value);
        }
        return null;
      },
      keyboardType: keyboardType,
      inputFormatters: finalFormatters,
      isMobile: false,
      prefixText: prefixText,
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
    BuildContext context,
  ) {
    return ValueListenableBuilder<PlatformFile?>(
      valueListenable: fileNotifier,
      builder: (context, file, child) {
        final isSmallMobile = Responsive.isSmallMobile(context);
        // Check if this is an image field based on allowed extensions
        final bool isImageField = allowedExtensions.every(
          (ext) => [
            'jpg',
            'jpeg',
            'png',
            'gif',
            'bmp',
            'webp',
          ].contains(ext.toLowerCase()),
        );

        return buildDottedBorderContainerWithRadius(
          borderRadius: 25.0,
          child: Container(
            width: double.infinity,
            height: isSmallMobile ? 100 : 120,
            padding: EdgeInsets.all(
              isSmallMobile ? defaultPadding / 2 : defaultPadding,
            ),
            decoration: BoxDecoration(
              color: file != null
                  ? Colors.green.shade50
                  : AppTheme.docUploadBgColor,
              borderRadius: BorderRadius.circular(25),
              border: file != null
                  ? Border.all(color: Colors.green.shade200)
                  : null,
            ),
            child: file != null
                ? Stack(
                    children: [
                      Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: isSmallMobile ? 16 : 20,
                            ),
                            SizedBox(width: isSmallMobile ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    file.name,
                                    style: AppFonts.mediumTextStyle(
                                      isSmallMobile ? 12 : 14,
                                      color: Colors.green.shade700,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    formatFileSize(file.size),
                                    style: AppFonts.regularTextStyle(
                                      isSmallMobile ? 10 : 12,
                                      color: Colors.green.shade600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Row(
                          children: [
                            // ----------- PREVIEW (EYE) ICON BUTTON ----------- //
                            Tooltip(
                              message: AppStrings.preview,
                              child: IconButton(
                                onPressed: () =>
                                    FilePreviewUtils.previewFile(context, file),
                                icon: Icon(
                                  Icons.remove_red_eye_outlined,
                                  color: Colors.green,
                                  size: isSmallMobile ? 16 : 20,
                                ),
                                padding: EdgeInsets.zero, // keep size compact
                                constraints:
                                    const BoxConstraints(), // remove extra padding
                                splashRadius: 18,
                              ),
                            ),

                            SizedBox(width: isSmallMobile ? 6 : 10),

                            // ----------- CLOSE ICON BUTTON ----------- //
                            Tooltip(
                              message: AppStrings.close,
                              child: IconButton(
                                onPressed: () => fileNotifier.value = null,
                                icon: Icon(
                                  Icons.close,
                                  color: Colors.red,
                                  size: isSmallMobile ? 16 : 20,
                                ),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                splashRadius: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () {
                          if (isImageField) {
                            _pickImage(
                              context,
                              fileNotifier,
                              allowedExtensions,
                            );
                          } else {
                            _pickFile(context, fileNotifier, allowedExtensions);
                          }
                        },
                        icon: Image.asset(
                          '$iconAssetpath/upload.png',
                          height: isSmallMobile ? 14 : 16,
                          width: isSmallMobile ? 14 : 16,
                        ),
                        label: Text(
                          AppStrings.upload,
                          style: AppFonts.mediumTextStyle(
                            isSmallMobile ? 12 : 14,
                            color: AppTheme.black,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppTheme.primaryTextColor,
                          elevation: 0,
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallMobile ? 8 : 12,
                            vertical: isSmallMobile ? 4 : 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                        ),
                      ),
                      SizedBox(height: isSmallMobile ? 4 : 8),
                      Text(
                        hintText,
                        textAlign: TextAlign.center,
                        style: AppFonts.mediumTextStyle(
                          isSmallMobile ? 10 : 12,
                          color: AppTheme.black,
                        ),
                      ),
                      Text(
                        formatText,
                        textAlign: TextAlign.center,
                        style: AppFonts.regularTextStyle(
                          isSmallMobile ? 9 : 12,
                          color: AppTheme.ternaryTextColor,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        // fileNotifier.value = result.files.first;
        final file = result.files.first;

        // Extract the file extension and convert to lowercase
        final fileExtension = file.extension?.toLowerCase();

        final bool isEmptyFile = kIsWeb
            ? (file.bytes == null || file.bytes!.isEmpty)
            : (file.size == 0);

        if (isEmptyFile) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.selectedFileIsEmpty,
            SnackBarType.error,
          );
          return;
        }

        // Check if the extension is actually allowed
        if (fileExtension == null ||
            !allowedExtensions.contains(fileExtension)) {
          AppSnackBar.showSnackBar(
            context,
            '${AppStrings.invalidFileType}${allowedExtensions.join(", ")}',
            SnackBarType.error,
          );
          return;
        }

        // Check if file size exceeds 25MB
        if (file.size > maxFileSizeInBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileSizeExceeded,
            SnackBarType.error,
          );
          return;
        }

        // PDF integrity check: detect corrupted PDFs by validating header/footer
        try {
          if (file.extension?.toLowerCase() == 'pdf') {
            final isCorrupted = await isPdfCorrupted(file);
            if (isCorrupted) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.selectedPdfAppearsToBeCorruptedOrInvalid,
                SnackBarType.error,
              );
              return;
            }
          }
        } catch (e, st) {
          debugPrint('PDF validation error (non-fatal): $e\n$st');
          AppSnackBar.showSnackBar(
            context,
            AppStrings.couldNotFullyValidatePdf,
            SnackBarType.error,
          );
        }
        // If file size is valid, update the notifier
        fileNotifier.value = file;
      }
    } catch (e) {
      // Handle error
      debugPrint('Error picking file: $e');
    }
  }

  Future<void> _pickImage(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Extract the file extension and convert to lowercase
        final fileExtension = file.extension?.toLowerCase();

        final bool isEmptyFile = kIsWeb
            ? (file.bytes == null || file.bytes!.isEmpty)
            : (file.size == 0);

        if (isEmptyFile) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.selectedFileIsEmpty,
            SnackBarType.error,
          );
          return;
        }

        // Check if the extension is actually allowed
        if (fileExtension == null ||
            !allowedExtensions.contains(fileExtension)) {
          AppSnackBar.showSnackBar(
            context,
            '${AppStrings.invalidFileType}${allowedExtensions.join(", ")}',
            SnackBarType.error,
          );
          return;
        }

        // Images have a max size of 1 MB
        const int maxImageSizeBytes = 1 * 1024 * 1024; // 1 MB

        // Check if image size exceeds 1 MB
        if (file.size > maxImageSizeBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.imageSizeExceeded,
            SnackBarType.error,
          );
          return;
        }

        // If image size is valid, update the notifier
        fileNotifier.value = file;
      }
    } catch (e) {
      // Handle error
      debugPrint('Error picking image: $e');
    }
  }

  List<FileUploadData> _prepareFilesForUpload() {
    List<FileUploadData> files = [];

    void addFile(String docType, PlatformFile? pickedFile) {
      if (pickedFile != null) {
        files.add(
          FileUploadData(
            categoryType: 'USER',
            documentType: docType,
            file: pickedFile,
          ),
        );
      }
    }

    addFile('EO_INSURANCE', eoInsuranceFile.value);
    addFile('BROKERAGE_LICENSE', brokerageLicenseFile.value);
    addFile('PRINCIPAL_BROKER_ID', principalBrokerIdFile.value);
    addFile('COMPANY_LOGO', logoFile.value);

    return files;
  }

  _submitForm(BuildContext context, User? user) async {
    if (selectedIndex.value == 1) {
      //invite tab
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Under development')));
      return;
    }
    if (_formKey.currentState!.validate() && user != null) {
      final phoneNumberCorrected =
          PhoneNumberFormatter.formatPhoneNumberForRequestBody(
            phoneController.text.trim(),
          );
      final brokerPayload = {
        'firstName': firstNameController.text.trim(),
        'lastName': lastNameController.text.trim(),
        'phone': phoneNumberCorrected,
        'email': emailController.text.trim(),
        'companyName': companyController.text.trim(),
        'cityId': selectedCity.value?.id != null
            ? int.tryParse(selectedCity.value!.id)
            : null,
        'stateId': selectedState.value?.id != null
            ? int.tryParse(selectedState.value!.id)
            : null,
        'zipCode': postalCodeController.text.trim(),
        'countryId': selectedCountry.value?.id != null
            ? int.tryParse(selectedCountry.value!.id)
            : null,
        "recruiterId": user.userId,
      };
      final filesToUpload = _prepareFilesForUpload();
      final brokerRegisterCubit = context.read<BrokerRegisterCubit>();

      await brokerRegisterCubit.registerBrokerWithFiles(
        brokerPayload: brokerPayload,
        files: filesToUpload,
      );

      final state = brokerRegisterCubit.state;

      if (state is BrokerRegisterError) {
        await AppSnackBar.showSnackBar(
          context,
          state.error,
          SnackBarType.error,
        );
      } else if (state is BrokerRegisterSuccess ||
          state is BrokerRegisterSuccessWithoutFiles) {
        final successUserId = state is BrokerRegisterSuccess
            ? (state.userId ?? '')
            : state is BrokerRegisterSuccessWithoutFiles
            ? (state.userId ?? '')
            : '';

        bool isStatusUpdated = await _statusUpdateApi(
          successUserId,
          brokerRegisterCubit,
          context,
        );
        if (isStatusUpdated) {
          _clearForm();
          await SuccessSnackBar.showSnackBar(
            context,
            state is BrokerRegisterSuccess
                ? brokerRegisterSuccess
                : brokerRegisterSuccessWithoutFiles,
            SnackBarType.success,
          );
        } else {
          await AppSnackBar.showSnackBar(
            context,
            brokerRegisterStatusFailure,
            SnackBarType.error,
          );
        }
      } else {
        await AppSnackBar.showSnackBar(
          context,
          brokerRegisterStatusFailure,
          SnackBarType.error,
        );
      }
    } else {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.pleaseFillRequiredFields,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  /// Invoke status update api when:
  /// Brokerage registered without choosing files
  /// Brokerage registered by selecting files(after successfull file upload)
  Future<bool> _statusUpdateApi(
    String? userId,
    BrokerRegisterCubit brokerageRegisterCubit,
    BuildContext context,
  ) async {
    if (userId != null) {
      await brokerageRegisterCubit.handleBrokerageStatusUpdate(userId, true);
      final uploadState = brokerageRegisterCubit.state;
      if (uploadState is BrokerRegisterStatusUpdated) {
        return true;
      } else if (uploadState is BrokerRegisterStatusUpdateFailed) {
        AppSnackBar.showSnackBar(
          context,
          uploadState.message,
          SnackBarType.error,
        );
      }
    } else {
      await AppSnackBar.showSnackBar(
        context,
        failedToUploadBrokerageRegStatusUserIDEmpty,
        SnackBarType.error,
      );
    }
    return false;
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    companyController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    eoInsuranceFile.value = null;
    brokerageLicenseFile.value = null;
    principalBrokerIdFile.value = null;
    logoFile.value = null;
    selectedCity.value = null;
    selectedState.value = null;
    selectedCountry.value = null;
    stateOptions.value = [];
    cityOptions.value = [];
    companyNameHint.value = false;
    showFirstNameHint.value = false;
    showLastNameHint.value = false;
    showEmailHint.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_formKey.currentState != null) {
        _formKey.currentState!.reset();
      }
    });
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return showAlertDialogue(
          context,
          title: AppStrings.clearData,
          content: AppStrings.clearDataConfirmation,
          primaryColor: AppTheme.primaryBlueColor,
          positiveButtonText: AppStrings.ok,
          negativeButtonText: AppStrings.cancel,
          onPositivePressed: () {
            Navigator.of(context).pop(true);
            _clearForm();
            // Any other logic you want
          },
        );
      },
    );
  }
}
