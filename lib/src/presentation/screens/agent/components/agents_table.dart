import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/app_snack_bar.dart';
import '../../../../core/utils/column_mapping_utils.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/format_currency_dollar.dart';
import '../../../../core/utils/regex.dart';
import '../../../../domain/models/user.dart';
import '../../dashboard/components/commission_revenue_info.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_theme.dart';
import '../../../../domain/models/agent_model.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsTable extends HookWidget {
  final bool showEditOptions;
  final bool showRecruits;
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;
  final Function(String userId, String userName)? onNavigateToCommissionInfo;

  const AgentsTable({
    super.key,
    this.showEditOptions = false,
    this.showRecruits = false,
    required this.onNavigateToAgentNetworkAgent,
    required this.onNavigateToCommissionInfo,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getAgentBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    // Get user from UserCubit
    final user = context.watch<UserCubit>().state.user;

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      //agent
      agentName,
      agentLevel,
      agentJoinDate,
      agentRefferedBy,
      agentState,
      agentCity,
      agentTotalSales,
      agentTotalRevenue,
      agentCommission,
      agentRevenueShare,
    ];

    final sortColumn = useState<String>('created_at');
    final sortOrder = useState<String>('ASC');
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalElements = useState(0);
    final ValueNotifier<String?> searchString = useState('');
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final currentFilters = useState<Map<String, dynamic>>({});
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');

    useEffect(() {
      Future.microtask(() async {
        if (context.mounted) {
          await _fetchAgents(
            context,
            user,
            selectedDate: selectedDate.value,
            sortBy: sortByDefault,
            sortDirection: sortOrder.value,
            searchString: searchString.value ?? "",
            page: currentpage.value,
          );
        }
      });
      return null;
    }, [user?.userId]);

    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);

    // Check if user is available
    if (user == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }
    return BlocConsumer<AgentCubit, AgentState>(
      listener: (context, state) {},
      builder: (context, state) {
        List<AgentModel> agentData = [];
        bool isLoading = false;
        String? errorMessage;

        if (state is AgentLoading) {
          isLoading = true;
        } else if (state is AgentLoaded) {
          agentData = state.agents;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.totalPages;
            totalElements.value = state.totalCount ?? 0;
          });
        } else if (state is AgentError) {
          errorMessage = state.message;
        }

        void handleSort(String columnName, bool ascending) async {
          String backendColumnName;
          if (columnName.isEmpty) {
            sortColumn.value = '';
            sortOrder.value = 'ASC';
            backendColumnName = 'created_at';
          } else {
            backendColumnName = _getBackendColumnName(columnName);
            sortColumn.value = columnName;
            sortOrder.value = ascending ? 'ASC' : 'DESC';
          }

          if (context.mounted) {
            await _fetchAgents(
              context,
              user,
              selectedDate: selectedDate.value,
              sortBy: backendColumnName,
              sortDirection: sortOrder.value,
              searchString: searchString.value ?? "",
              page: currentpage.value,
            );
          }
        }

        if (errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? Icons.lock_outline
                      : Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? authenticationRequired
                      : errorLoadingData,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? pleaseLoginToAccessAgentData
                      : '$errorPrefix$errorMessage',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    if (context.mounted) {
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        sortBy: sortColumn.value,
                        sortDirection: sortOrder.value,
                        searchString: searchString.value ?? "",
                        page: currentpage.value,
                      );
                    }
                  },
                  child: const Text(retry),
                ),
              ],
            ),
          );
        }

        final isDesktop = Responsive.isDesktop(context);

        return LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //Customized table layout
                Flexible(
                  child: CustomDataTableWidget<AgentModel>(
                    data: agentData,
                    title: agents,
                    titleIcon: "$iconAssetpath/user.png",
                    searchHint: searchAgent,
                    searchFn: (agent) =>
                        agent.fullName +
                        agent.depthLevel +
                        AppDateFormatter.formatDateMMddyyyy(agent.joiningDate) +
                        agent.recruiterName +
                        agent.state +
                        agent.city +
                        agent.totalSales.toString() +
                        agent.salesVolume.toString() +
                        agent.salesCommission.toString(),
                    // Dynamic filtering system
                    filterColumnNames: [
                      //agentName, // name
                      //agentState, // state
                      //agentRole, // role
                      agentJoinDate, // join date
                    ],
                    filterValueExtractors: {
                      //agentName: (agent) => agent.fullName,
                      //agentState: (agent) => agent.state,
                      //agentRole: (agent) => agent.level,
                      agentJoinDate: (agent) =>
                          agent.joiningDate?.toIso8601String() ?? "",
                    },
                    dateFilterColumns: const [
                      agentJoinDate, // Join date should use calendar picker
                    ],
                    showCrossButtonForFilter: const {agentJoinDate: true},
                    showSortIconColumns: [
                      agentName,
                      agentLevel,
                      agentJoinDate,
                      agentRefferedBy,
                      agentState,
                      agentCity,
                      agentTotalSales,
                      agentTotalRevenue,
                      agentCommission,
                      agentRevenueShare,
                    ],
                    dateFilterBannerTexts: const {
                      agentJoinDate: 'Users joined on or after',
                    },
                    columnNames: formattedHeaders,
                    cellBuilders: [
                      (agent) => agent.fullName, // agentName
                      (agent) => agent.depthLevel, // agentLevel
                      (agent) => AppDateFormatter.formatDateMMddyyyy(
                        agent.joiningDate,
                      ),
                      (agent) => agent.recruiterName, // agentRefferedBy
                      (agent) => agent.state, // agentState
                      (agent) => agent.city, // agentCity
                      (agent) => agent.totalSales.toString(), // agentTotalSales
                      (agent) => '${formatCurrencyDollar(agent.salesVolume)}',
                      (agent) =>
                          '${formatCurrencyDollar(agent.salesCommission)}',
                      (agent) => '${formatCurrencyDollar(agent.revenueShare)}',
                    ],

                    /// to show icons before the cell content. eg: username and user icon
                    iconCellBuilders: [
                      (agent) => TableCellData(
                        text: agent.fullName,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 28,
                      ),
                      null, // level
                      (agent) => TableCellData(
                        text: agent.recruiterName,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 28,
                      ),
                      null, // joinDate
                      null, // state
                      null, // city
                      null, // totalSales
                      null, // totalRevenue
                      null, // commission
                      null, //revenue share
                    ],

                    /// Boolean flags to indicate which columns use icon cell builders. can enable/disable by setting this flag
                    useIconBuilders: [
                      true, // name - use icon
                      false, // level
                      false, // joinDate
                      true, // referredBy - use icon
                      false, // state
                      false, // city
                      false, // totalSales
                      false, // totalRevenue
                      false, // commission
                      false, // revenue share
                    ],

                    /// Widget builders for styled cells - none needed for this table
                    widgetCellBuilders: [
                      null, // name - use text
                      null, // level - use text
                      null, // joinDate - use text
                      null, // referredBy - use text
                      null, // totalSales - use text
                      null, // state - use text
                      null, // city - use text
                      null, // total revenue - use text
                      null, // commission - use text
                      null, // revenue share
                    ],
                    // Boolean flags to indicate which columns use widget builders
                    useWidgetBuilders: [
                      false, // name
                      false, // level
                      false, // referredBy
                      false, // joinDate
                      false, // state
                      false, // city
                      false, // totalSales
                      false, // totalRevenue
                      false, // commission
                      false, // revenue share
                    ],
                    actionBuilders: [
                      (context, agent) => ActionButtonEye(
                        onPressed: () => _onAgentAction(context, agent),
                        isCompact: true,
                        isMobile: false,
                      ),
                      (context, agent) => ActionButtonEye(
                        onPressed: () =>
                            _handleCommissionInfoNavigation(context, agent),
                        isCompact: true,
                        isMobile: false,
                        icon:
                            "$iconAssetpath/link_icon.png", // optional custom icon asset
                        padding: 2.0,
                        name: agent.fullName,
                      ),
                      //TODO: implement later
                      /*if (showEditOptions) ...[
                        (context, agent) => ActionButtonEye(
                          onPressed: () =>
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Under development!')),
                              ),
                          isCompact: true,
                          isMobile: false,
                          padding: 8,
                          icon: '$iconAssetpath/table_edit.png',
                        ),
                        (context, agent) => ActionButtonEye(
                          onPressed: () =>
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Under development!')),
                              ),
                          isCompact: true,
                          isMobile: false,
                          padding: 8,
                          icon: '$iconAssetpath/delete.png',
                        ),
                      ],*/
                    ],
                    mobileCardBuilder: (context, agent) =>
                        _buildMobileAgentCard(agent, context),
                    onSort: handleSort,
                    emptyStateMessage: noDataAvailable,
                    useMinHeight: isDesktop,
                    minHeight: constraints.maxHeight,
                    pageCount: pageCount.value,
                    isLoading: state is AgentLoading,
                    totalElements: totalElements.value,
                    //TODO: Remove later
                    // onDateFilterChanged: (value) async {
                    //   selectedDate.value = value;
                    //   await _fetchAgents(
                    //     context,
                    //     user,
                    //     selectedDate: value,
                    //     page: currentpage.value,
                    //     searchString: searchString.value ?? "",
                    //   );
                    // },
                    currentPageIndex: currentpage.value,
                    onAllFiltersChanged: (allFilters) async {
                      currentFilters.value = allFilters;
                      currentpage.value = 0;
                      DateTime? joinDateValue;
                      // Extract dropdown filters
                      if (allFilters.containsKey(agentJoinDate)) {
                        joinDateValue = allFilters[agentJoinDate];
                        selectedDate.value = joinDateValue;
                      } else {
                        selectedDate.value = null;
                      }
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        sortBy: sortColumn.value,
                        sortDirection: sortOrder.value,
                        page: 0,
                        searchString: searchString.value ?? "",
                      );
                    },
                    handleTableSearch: (value) async {
                      // Cancel any pending search
                      searchDebouncer.value?.cancel();

                      // Store the search value immediately for UI feedback
                      searchString.value = value;

                      // Don't search if value hasn't changed
                      if (lastSearchValue.value == value) {
                        return;
                      }

                      lastSearchValue.value = value;

                      // Debounce: wait 300ms before making the API call
                      searchDebouncer.value = Timer(
                        const Duration(milliseconds: 300),
                        () async {
                          currentpage.value = 0;
                          await _fetchAgents(
                            context,
                            user,
                            selectedDate: selectedDate.value,
                            sortBy: sortColumn.value,
                            sortDirection: sortOrder.value,
                            page: 0,
                            searchString: value,
                          );
                        },
                      );
                    },
                    handlePagination: (page) async {
                      if (sortColumn.value.isNotEmpty &&
                          RegExUtils.startsWithUppercase.hasMatch(
                            sortColumn.value,
                          )) {
                        String backendColumnName = _getBackendColumnName(
                          sortColumn.value,
                        );
                        sortColumn.value = backendColumnName;
                      }
                      currentpage.value = page;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        sortBy: sortColumn.value,
                        sortDirection: sortOrder.value,
                        searchString: searchString.value ?? "",
                        page: page,
                      );
                    },
                    onResetFilters: ({required bool isFilterOptionOnlyReload}) {
                      if (!isFilterOptionOnlyReload) {
                        selectedDate.value =
                            null; // DateTime? type (null for no filter)
                        searchString.value = ''; // String? type (empty string)
                        currentpage.value =
                            1; // int type (reset to first page, usually 0 or 1)
                        sortColumn.value =
                            'created_at'; // String type (default sort column, if needed)
                        sortOrder.value =
                            'ASC'; // String type (default sort order, if needed)
                        _fetchAgents(
                          context,
                          user,
                          selectedDate: selectedDate.value,
                          sortBy: sortColumn.value,
                          sortDirection: sortOrder.value,
                          searchString: searchString.value,
                          page: currentpage.value,
                        );
                      }
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
    // );
  }

  _fetchAgents(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    String sortBy = sortByDefault,
    String sortDirection = "ASC",
    required int page,
    required String? searchString,
  }) {
    if (context.mounted && (user?.userId != null || user?.userId != '')) {
      final dateString = AppDateFormatter.formatToUtcIso8601(selectedDate);

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortBy, // "created_at",
        "sortDirection": sortDirection,
        "searchString": searchString,
        "joiningDate": dateString,
        "userId": user?.userId,
      };
      context.read<AgentCubit>().getAgents(requestBody: payload);
    }
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    // Navigate to agent detail or show action
    if (onNavigateToAgentNetworkAgent != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetworkAgent!(agent);
      } catch (e) {
        // If no matching broker found, show error message
        AppSnackBar.showSnackBar(
          context,
          'Broker not found: ${agent.fullName}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      AppSnackBar.showSnackBar(
        context,
        'Action clicked for ${agent.fullName}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  void _handleCommissionInfoNavigation(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    // Navigate to agent detail or show action
    if (onNavigateToCommissionInfo != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToCommissionInfo!(agent.userId, agent.fullName);
      } catch (e) {
        // If no matching broker found, show error message
        AppSnackBar.showSnackBar(
          context,
          'Commission details not found for ${agent.fullName}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      AppSnackBar.showSnackBar(
        context,
        'Commission details not found for ${agent.fullName}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectableText(
            agent.fullName,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SelectableText('$agentLevel: ${agent.depthLevel}'),
          SelectableText(
            '$agentJoinDate: ${AppDateFormatter.formatDateMMddyyyy(agent.joiningDate)}',
          ),
          SelectableText('$agentRefferedBy: ${agent.recruiterName}'),
          SelectableText('$agentState: ${agent.state}'),
          SelectableText('$agentCity: ${agent.city}'),

          SelectableText('$agentTotalSales: ${agent.totalSales}'),
          SelectableText(
            '$agentTotalRevenue: ${formatCurrencyDollar(agent.salesVolume)}',
          ),
          SelectableText('$agentCommission: ${agent.salesCommission}'),
          SelectableText('$agentAdditionalNotes: ${agent.additionalNotes}'),
          SelectableText(
            '$agentTotalRevenue: ${formatCurrencyDollar(agent.revenueShare)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // View Agent Details Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isMobile: true,
                    name: agent.fullName,
                  ),
                ),
                const SizedBox(height: 8), // Spacing between buttons
                // View Commission Revenue Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () {
                      if (onNavigateToCommissionInfo != null) {
                        onNavigateToCommissionInfo!(
                          agent.userId,
                          agent.fullName,
                        );
                      }
                    },
                    isMobile: true,
                    icon: "$iconAssetpath/link_icon.png",
                    padding: 2.0,
                    name: agent.fullName,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
