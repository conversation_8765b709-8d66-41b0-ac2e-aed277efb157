import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import '/src/core/enum/user_role.dart';
import '/src/domain/models/info_card.dart';
import '/src/domain/repository/get_infocard_repository.dart';
import '/src/presentation/cubit/infocard/cubit/infocard_state.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/network/api_config.dart';
import '/src/domain/models/info_card_item_model.dart';

class DashboardCubit extends Cubit<DashboardCubitState> {
  DashboardCubit(this._infoCardRepository) : super(DashboardInitial());

  final GetInfocardRepository _infoCardRepository;
  final String type = "TOTAL";
  // final String type = "LAST_MONTH";

  Future<void> fetchAllDashboardInfoCardsitems(UserRole role) async {
    try {
      emit(DashboardCardsLoading());

      List<InfoCardData> infoCards = [];

      Future<InfoCardItemModel?> safeApiCall(
        Future<InfoCardItemModel> apiCall,
      ) async {
        try {
          return await apiCall;
        } catch (e) {
          debugPrint("API Call Failed [fetchAllDashboardInfoCardsitems]: $e");
          return null; // Continue flow even if fails
        }
      }

      switch (role) {
        case UserRole.admin:
          {
            final totalBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                type,
              ),
            );
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );
            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );
            final totalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                type,
              ),
            );
            final lastMonthBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                "LAST_MONTH",
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );
            final yearTotalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                "YEAR",
              ),
            );
            final yearTotalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                "YEAR",
              ),
            );

            final results = await Future.wait([
              totalBrokeragesFuture,
              totalAgentsFuture,
              totalSalesFuture,
              totalRevenueFuture,
              lastMonthBrokeragesFuture,
              lastMonthAgentsFuture,
              yearTotalSalesFuture,
              yearTotalRevenueFuture,
            ]);

            final totalBrokerages = results[0];
            final totalAgents = results[1];
            final totalSales = results[2];
            final totalRevenue = results[3];
            final lastMonthBrokerages = results[4];
            final lastMonthAgents = results[5];
            final yearSales = results[6];
            final yearRevenue = results[7];

            infoCards = [
              InfoCardData(
                title: 'Total Brokerages',
                value: totalBrokerages != null
                    ? totalBrokerages.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/brokers.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthBrokerages != null
                    ? lastMonthBrokerages.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? totalAgents.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sales.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearSales != null
                    ? yearSales.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/sales_count.png',
              ),
              InfoCardData(
                title: 'Sales Volume',
                value: totalRevenue != null
                    ? formatCurrency(totalRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearRevenue != null
                    ? formatCurrency(yearRevenue.data.value)
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
            ];
            break;
          }
        case UserRole.platformOwner:
          {
            final totalBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                type,
              ),
            );
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );
            // final totalSalesFuture = safeApiCall(
            //   _infoCardRepository.getInfocardItemValue(
            //     APIConfig.totalSales,
            //     type,
            //   ),
            // );
            final totalSubRevenue = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSubscriptionRevenue,
                type,
              ),
            );
            final lastMonthBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                "LAST_MONTH",
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );
            final yearSubRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                "YEAR",
              ),
            );
            final yearRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSubscriptionRevenue,
                "YEAR",
              ),
            );

            final results = await Future.wait([
              totalBrokeragesFuture,
              totalAgentsFuture,
              // totalSalesFuture,
              totalSubRevenue,
              lastMonthBrokeragesFuture,
              lastMonthAgentsFuture,
              yearSubRevenueFuture,
              yearRevenueFuture,
            ]);

            final totalBrokerages = results[0];
            final totalAgents = results[1];
            // final totalSales = results[2];
            final totalSRevenue = results[2];
            final lastMonthBrokerages = results[3];
            final lastMonthAgents = results[4];
            final yearSubRevenue = results[5];
            final yearRevenue = results[6];

            infoCards = [
              InfoCardData(
                title: 'Total Brokerages',
                value: totalBrokerages != null
                    ? totalBrokerages.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/brokers.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthBrokerages != null
                    ? lastMonthBrokerages.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? totalAgents.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Total Subscription',
                value: totalBrokerages != null
                    ? totalBrokerages.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sales.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearSubRevenue != null
                    ? yearSubRevenue.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Sales Volume',
                value: totalSRevenue != null
                    ? formatCurrency(totalSRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearRevenue != null
                    ? formatCurrency(yearRevenue.data.value)
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
            ];
            break;
          }
        case UserRole.brokerage:
          {
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );

            final totalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                type,
              ),
            );
            final totalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                type,
              ),
            );
            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );

            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );
            final yearTotalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                "YEAR",
              ),
            );
            final yearTotalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                "YEAR",
              ),
            );
            final yearTotalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                "YEAR",
              ),
            );

            final results = await Future.wait([
              totalAgentsFuture,
              totalCommissionsFuture,
              lastMonthAgentsFuture,
              totalRevenueFuture,
              totalSalesFuture,
              yearTotalSalesFuture,
              yearTotalRevenueFuture,
              yearTotalCommissionsFuture,
            ]);

            final totalAgents = results[0];
            final totalCommissions = results[1];
            final lastMonthAgents = results[2];
            final totalRevenue = results[3];
            final totalSales = results[4];
            final yearSales = results[5];
            final yearRevenue = results[6];
            final yearCommissions = results[7];

            infoCards = [
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sold_home.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearSales != null
                    ? yearSales.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/sales_count.png',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? formatNumber(totalAgents.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Sales Volume',
                value: totalRevenue != null
                    ? formatCurrency(totalRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearRevenue != null
                    ? formatCurrency(yearRevenue.data.value)
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
              InfoCardData(
                title: 'Total Commissions',
                value: totalCommissions != null
                    ? formatCurrency(totalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearCommissions != null
                    ? formatCurrency(yearCommissions.data.value)
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
            ];
            break;
          }
        case UserRole.agent:
          {
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );

            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );

            final totalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                type,
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );

            final personalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissionsPersonal,
                type,
              ),
            );

            final yearSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                "YEAR",
              ),
            );
            final yearCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                "YEAR",
              ),
            );
            final yearPersonalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissionsPersonal,
                "YEAR",
              ),
            );

            final results = await Future.wait([
              totalAgentsFuture,
              totalCommissionsFuture,
              lastMonthAgentsFuture,
              personalCommissionsFuture,
              totalSalesFuture,
              yearSalesFuture,
              yearCommissionsFuture,
              yearPersonalCommissionsFuture,
            ]);

            final totalAgents = results[0];
            final totalCommissions = results[1];
            final lastMonthAgents = results[2];
            final personalCommissions = results[3];
            final totalSales = results[4];
            final yearTotalSales = results[5];
            final yearTotalCommissions = results[6];
            final yearPersonalCommissions = results[7];

            infoCards = [
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? formatNumber(totalAgents.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/user.png',
              ),
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sold_home.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearTotalSales != null
                    ? yearTotalSales.data.value.toString()
                    : 'N/A',
                footerIcon: '$iconAssetpath/sales_count.png',
              ),
              InfoCardData(
                title: 'Personal Commissions',
                value: personalCommissions != null
                    ? formatCurrency(personalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearPersonalCommissions != null
                    ? formatCurrency(
                        yearPersonalCommissions.data.value,
                        removeSymbol: true,
                      )
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
              InfoCardData(
                title: 'Total Commissions',
                value: totalCommissions != null
                    ? formatCurrency(totalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Current Year",
                additionalInfo: yearTotalCommissions != null
                    ? formatCurrency(
                        yearTotalCommissions.data.value,
                        removeSymbol: true,
                      )
                    : 'N/A',
                footerIcon: '$iconAssetpath/info_card_dollar.png',
              ),
            ];
            break;
          }
        default:
          {
            infoCards = [];
            break;
          }
      }

      emit(DashboardCardsSuccess(infoCards));
    } catch (e) {
      emit(DashboardCardsFailure(e.toString()));
    }
  }

  String formatCurrency(double value, {bool removeSymbol = false}) {
    if (value < 1000) {
      return removeSymbol
          ? value.toStringAsFixed(0)
          : '\$${value.toStringAsFixed(0)}';
    } else if (value < 1000000) {
      String formatted = (value / 1000).toStringAsFixed(1);
      if (formatted.endsWith('.0')) {
        formatted = formatted.substring(0, formatted.length - 2);
      }
      return removeSymbol ? '${formatted}K' : '\$${formatted}K';
    } else if (value < 1000000000) {
      String formatted = (value / 1000000).toStringAsFixed(1);
      if (formatted.endsWith('.0')) {
        formatted = formatted.substring(0, formatted.length - 2);
      }
      return removeSymbol ? '${formatted}M' : '\$${formatted}M';
    } else {
      String formatted = (value / 1000000000).toStringAsFixed(1);
      if (formatted.endsWith('.0')) {
        formatted = formatted.substring(0, formatted.length - 2);
      }
      return removeSymbol ? '${formatted}B' : '\$${formatted}B';
    }
  }

  String formatNumber(double value) {
    return value >= 1000
        ? '${(value / 1000).toStringAsFixed(0)}K'
        : value.toString();
  }
}
