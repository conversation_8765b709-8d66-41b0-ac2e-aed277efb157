// date_selection_mode.dart

enum DateFilterMode {
  singleDate, // Only single date selection
  dateRange, // Only date range selection
  both, // Both single date and date range (with quick options)
}

// Add this class to define calendar configuration per column
class DateColumnConfig {
  final String columnName;
  final DateFilterMode mode;
  final List<String> quickOptions; // For dateRange mode: ['Last 7 Days', 'Last 30 Days', 'Custom Range']
  final bool showSingleDateOption;
  final bool showClearOption;

  const DateColumnConfig({
    required this.columnName,
    required this.mode,
    this.quickOptions = const [
      'Last 7 Days',
      'Last 30 Days',
      'Last 90 Days',
      'Custom Range',
    ],
    this.showSingleDateOption = false,
    this.showClearOption = false,
  });
}
