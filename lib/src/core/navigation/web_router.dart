import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/presentation/screens/dashboard/components/commission_revenue_info.dart';
import '../../presentation/cubit/user/user_cubit.dart';
import '../../presentation/screens/auth/password_recovery_screen.dart';
import '../../presentation/screens/auth/email_verification_screen.dart';
import '../../presentation/screens/office_staff/register_office_staff_screen.dart';
import '../../presentation/screens/auth/create_password_screen.dart';
import '../enum/user_role.dart';
import '/src/presentation/screens/layout/signup_layout.dart';
import '../../presentation/screens/agent/agent_registration_screen.dart';
import '../../presentation/screens/broker/register_broker_screen.dart';
import '/main_layout_screen.dart';
import '../../presentation/screens/auth/auth_wrapper.dart';
import '../../data/repository/auth_data_repository.dart';
import '../../data/repository/get_access_token_repository_impl.dart';

class RouteSegments {
  static const login = '';
  static const mainLayout = 'home';
  static const dashboard = 'dashboard';
  static const brokerages = 'brokerages';
  static const agents = 'agents';
  static const sales = 'sales';
  static const reports = 'reports';
  static const registerBroker = 'register-brokerage';
  static const registerAgent = 'register-agent';
  static const registerStaff = 'register-office-staff';
  static const agentNetwork = 'agent-network';
  static const saleReviewDoc = 'sale-review-doc';
  static const signupBroker = 'signup-brokerage';
  static const signupAgent = 'signup-agent';
  static const createPassword = 'create-password';
  static const uploadDocument = 'upload-closing-document';
  static const emailVerification = 'email-verification';
  static const forgotPassword = 'forgot-password';
  static const resetPassword = 'reset-password';
  static const commissionRevenueInfo = 'commission-revenue-info';
  static const profile = 'profile';
}

enum AppRoutes {
  login('/'),
  mainLayout('/${RouteSegments.mainLayout}'),
  dashboard('/${RouteSegments.mainLayout}/${RouteSegments.dashboard}'),
  brokerages('/${RouteSegments.mainLayout}/${RouteSegments.brokerages}'),
  agents('/${RouteSegments.mainLayout}/${RouteSegments.agents}'),
  sales('/${RouteSegments.mainLayout}/${RouteSegments.sales}'),
  reports('/${RouteSegments.mainLayout}/${RouteSegments.reports}'),
  registerBroker(
    '/${RouteSegments.mainLayout}/${RouteSegments.registerBroker}',
  ),
  registerAgent('/${RouteSegments.mainLayout}/${RouteSegments.registerAgent}'),
  registerOfficeStaff(
    '/${RouteSegments.mainLayout}/${RouteSegments.registerStaff}',
  ),
  agentNetwork('/${RouteSegments.mainLayout}/${RouteSegments.agentNetwork}'),
  saleReviewDoc('/${RouteSegments.mainLayout}/${RouteSegments.saleReviewDoc}'),

  signupBroker('/${RouteSegments.signupBroker}'),
  signupAgent('/${RouteSegments.signupAgent}'),
  createPassword('/${RouteSegments.createPassword}'),
  uploadDocument(
    '/${RouteSegments.mainLayout}/${RouteSegments.uploadDocument}',
  ),
  commissionRevenueInfo(
    '/${RouteSegments.mainLayout}/${RouteSegments.commissionRevenueInfo}',
  ),
  emailVerification('/${RouteSegments.emailVerification}'),
  forgotPassword('/${RouteSegments.forgotPassword}'),
  resetPassword('/${RouteSegments.resetPassword}'),
  profile('/${RouteSegments.mainLayout}/${RouteSegments.profile}');

  const AppRoutes(this.path);
  final String path;
}

// Helper function to check if user is authenticated
Future<bool> _isAuthenticatedAsync() async {
  final authRepository = AuthDataRepository();
  final token = authRepository.accessToken;

  // If JWT already in memory, user is authenticated
  if (token != null && token.isNotEmpty) {
    return true;
  }

  // No JWT in memory, try to refresh from HttpOnly cookie
  try {
    final getAccessTokenRepository = GetAccessTokenRepositoryImpl();

    // Call refresh token endpoint (no refresh token needed since using cookies)
    final newToken = await getAccessTokenRepository.getAccessToken('');

    if (newToken != null) {
      // Save tokens to AuthDataRepository
      authRepository.setTokens(newToken.jwt, newToken.refreshToken);
      return true;
    }

    return false;
  } catch (e) {
    // Refresh failed (no cookie or invalid cookie)
    return false;
  }
}

/// Helper function to check if a tab is valid for main layout (/home/<USER>
bool _isValidMainLayoutTab(String tab) {
  // Only include routes that should be accessible under /home/
  // Exclude standalone routes like reset-password, forgot-password, etc.
  const validMainLayoutTabs = {
    RouteSegments.dashboard,
    RouteSegments.brokerages,
    RouteSegments.agents,
    RouteSegments.sales,
    RouteSegments.reports,
    RouteSegments.registerBroker,
    RouteSegments.registerAgent,
    RouteSegments.registerStaff,
    RouteSegments.agentNetwork,
    RouteSegments.saleReviewDoc,
    RouteSegments.uploadDocument,
    RouteSegments.commissionRevenueInfo,
    RouteSegments.profile,
  };

  return validMainLayoutTabs.contains(tab);
}

/// Handles role-based and validity-based redirects for tabs
String? _redirectPathForRole(BuildContext context, String tab) {
  final userRole = context.read<UserCubit>().state.user?.role;
  final dashboard = AppRoutes.dashboard.path;

  // First, validate if the tab is valid for main layout (/home/<USER>
  // If tab is not a valid main layout tab, redirect to dashboard
  if (!_isValidMainLayoutTab(tab)) {
    return dashboard;
  }

  // Then proceed with role-based validation
  switch (tab) {
    case RouteSegments.reports:
      return dashboard;
    case RouteSegments.registerBroker:
      if (userRole != UserRole.admin) return dashboard;
      break;
    case RouteSegments.registerAgent:
      if (userRole != UserRole.agent && userRole != UserRole.brokerage) {
        return dashboard;
      }
      break;
    case RouteSegments.registerStaff:
      if (userRole != UserRole.brokerage) return dashboard;
      break;
  }

  return null;
}

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login.path,
  redirect: (context, state) async {
    // Clear visible snackbar before redirect
    ScaffoldMessenger.maybeOf(context)?.clearSnackBars();
    final isAuthenticated = await _isAuthenticatedAsync();
    final isOnLoginPage =
        state.matchedLocation == AppRoutes.login.path ||
        state.matchedLocation == AppRoutes.createPassword.path;
    final isOnPublicPage =
        state.matchedLocation == AppRoutes.signupAgent.path ||
        state.matchedLocation == AppRoutes.createPassword.path ||
        state.matchedLocation == AppRoutes.emailVerification.path ||
        state.matchedLocation == AppRoutes.forgotPassword.path;

    // If not authenticated and trying to access protected route, redirect to login
    if (!isAuthenticated && !isOnLoginPage && !isOnPublicPage) {
      return AppRoutes.login.path;
    }

    // If authenticated and on login page, redirect to dashboard
    if (isAuthenticated && isOnLoginPage) {
      return AppRoutes.dashboard.path;
    }

    if (isAuthenticated && isOnLoginPage) {
      return AppRoutes.dashboard.path;
    }

    // No redirect needed
    return null;
  },
  routes: [
    GoRoute(
      path: AppRoutes.login.path,
      builder: (context, state) => const AuthWrapper(),
    ),
    GoRoute(
      path: '/home/<USER>',
      redirect: (context, state) {
        final tab = state.pathParameters['tab'] ?? 'dashboard';

        // Check if the tab corresponds to a restricted route for brokerage users
        final path = _redirectPathForRole(context, tab);

        return path; // No redirect needed
      },
      builder: (context, state) {
        final tab = state.pathParameters['tab'] ?? 'dashboard';
        return MainLayoutScreen(initialTab: tab);
      },
    ),
    GoRoute(
      path: AppRoutes.mainLayout.path,
      redirect: (context, state) => AppRoutes.dashboard.path,
    ),
    GoRoute(
      path: AppRoutes.signupBroker.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        return SignupLayoutScreen(child: RegisterBrokerScreen());
      },
    ),
    GoRoute(
      path: AppRoutes.signupAgent.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];

        final extras = state.extra as Map<String, dynamic>?;
        final idToken = extras?["idToken"];
        final signinType = extras?['signinType'] ?? SigninType.none;

        return SignupLayoutScreen(
          child: AgentRegistrationScreen(
            inviteId: token,
            isSignUp: true,
            idToken: idToken,
            signinType: signinType,
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.signupAgent.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        return SignupLayoutScreen(child: RegisterOfficeStaffScreen());
      },
    ),
    GoRoute(
      path: AppRoutes.createPassword.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        String? userId = state.uri.queryParameters['user-id'];
        String? email = state.uri.queryParameters['email'];

        final extra = state.extra as Map<String, dynamic>?;

        // when pass values from signup
        email ??= extra?['email'] as String?;
        userId ??= extra?['userId'] as String?;

        return SignupLayoutScreen(
          child: CreatePasswordScreen(
            inviteId: token,
            email: email,
            userId: userId,
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.emailVerification.path,
      builder: (context, state) =>
          SignupLayoutScreen(child: EmailVerificationScreen()),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword.path,
      builder: (context, state) {
        String? token = state.uri.queryParameters['token'];

        final extra = state.extra as Map<String, dynamic>?;

        final recoveryType =
            extra?['type'] as PasswordRecoveryType? ??
            PasswordRecoveryType.forgot;

        return recoveryType == PasswordRecoveryType.forgot
            ? SignupLayoutScreen(
                child: PasswordRecoveryScreen(
                  recoveryType: recoveryType,
                  token: token,
                ),
              )
            : PasswordRecoveryScreen(recoveryType: recoveryType, token: '');
      },
    ),
    GoRoute(
      path: AppRoutes.resetPassword.path,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;

        final recoveryType =
            extra?['type'] as PasswordRecoveryType? ??
            PasswordRecoveryType.reset;

        return SignupLayoutScreen(
          child: PasswordRecoveryScreen(recoveryType: recoveryType, token: ''),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.commissionRevenueInfo.path,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;

        final userId = extra?['userId'] as String? ?? '';

        return CommissionRevenuewInfoScreen(userId: userId);
      },
    ),
  ],
);
