import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/utils/format_currency_dollar.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/enum/user_role.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/column_mapping_utils.dart';
import '../../../core/utils/format_phone_number.dart';
import '../dashboard/components/commission_revenue_info.dart';
import '/src/domain/models/filter/table_filter.dart';
import '../../cubit/filter/filter_cubit.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '../../../core/enum/user_status.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../domain/models/broker_api.dart';
import '../../../domain/models/user.dart';
import '../../cubit/broker/broker_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/tables/action_button_eye.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';

class BrokerageListScreen extends HookWidget {
  final Function(Brokers)? onNavigateToAgentNetwork;
  final Function(String userId, String userName, {bool isBrokerageList})?
  onNavigateToCommissionInfo;

  BrokerageListScreen({
    super.key,
    required this.onNavigateToAgentNetwork,
    required this.onNavigateToCommissionInfo,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getBrokerBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final sortedBrokers = useState<List<Brokers>>([]);
    final brokerFilterOptions = useState<List<TableFilter>>([]);
    final userStatusFilterOptions = useState<List<TableFilter>>([]);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalBrokers = useState(0);
    final ValueNotifier<String?> searchString = useState('');
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final ValueNotifier<bool?> isActive = useState(null);
    final ValueNotifier<String?> associatedBrokerage = useState(null);
    final ValueNotifier<String?> sortBy = useState("created_at");
    final ValueNotifier<String?> sortOrder = useState("ASC");
    final currentFilters = useState<Map<String, dynamic>>({});
    final ValueNotifier<String?> selectedState = useState(null);
    final ValueNotifier<String?> previousState = useState(null);
    final ValueNotifier<String?> selectedCity = useState(null);
    final stateFilterOptions = useState<List<TableFilter>>([]);
    final cityFilterOptions = useState<List<TableFilter>>([]);

    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role.toString() ?? "";
    final filterCubit = context.read<FilterCubit>();
    final filterSearchTexts = useState<Map<String, String>>({});
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListStateColumnHeader,
      brokerListCityColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
      brokerListTotalRevenueColumnHeader,
      brokerListCommissionColumnHeader,
      brokerListrevenueShareColumnHeader,
      brokerListStatusColumnHeader,
    ];

    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);

    void handleSort(String columnName, bool ascending) async {
      if (columnName.isEmpty) {
        sortBy.value = "created_at";
        sortOrder.value = "ASC";

        if (context.mounted) {
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
            isActive: isActive.value,
            associatedBrokerage: associatedBrokerage.value,
            sortColumn: "created_at",
            sortOrder: "ASC",
            cityId: selectedCity.value,
            stateId: selectedState.value,
          );
        }
        return;
      }
      // Map the display column name to the backend field name
      String backendColumnName = _getBackendColumnName(columnName);
      sortBy.value = backendColumnName;
      sortOrder.value = ascending ? 'ASC' : 'DESC';

      if (context.mounted) {
        await _fetchBrokers(
          context,
          user,
          selectedDate: selectedDate.value,
          page: currentpage.value,
          searchString: searchString.value,
          isActive: isActive.value,
          associatedBrokerage: associatedBrokerage.value,
          sortColumn: backendColumnName,
          sortOrder: sortOrder.value,
          cityId: selectedCity.value,
          stateId: selectedState.value,
        );
      }
    }

    useEffect(() {
      Future.microtask(() async {
        if (context.mounted) {
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
            isActive: isActive.value,
            associatedBrokerage: associatedBrokerage.value,
            sortColumn: sortBy.value,
            sortOrder: sortOrder.value,
            cityId: selectedCity.value,
            stateId: selectedState.value,
          );
          await updateDropDownOptions(
            filterCubit,
            brokerFilterOptions,
            userStatusFilterOptions,
            stateFilterOptions,
            cityFilterOptions,
          );
        }
      });
      return null;
    }, []);

    List<String> getFilterColumnNames() {
      List<String> filterColumns = [];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        filterColumns.add(brokerListNameColumnHeader);
      }
      filterColumns.addAll([
        brokerListJoinDateColumnHeader,
        brokerListStatusColumnHeader,
      ]);

      return filterColumns;
    }

    final filterColumnNames = getFilterColumnNames();

    return BlocConsumer<BrokerCubit, BrokerState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        if (state is BrokerLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.brokerApi?.totalPages ?? 0;
            sortedBrokers.value = state.brokerApi?.brokers ?? [];
            totalBrokers.value = state.brokerApi?.totalElements ?? 0;
          });
        }

        return CustomDataTableWidget<Brokers>(
          data: sortedBrokers.value,
          title: brokersTab,
          titleIcon: "$iconAssetpath/user.png",
          searchHint: searchHint,
          searchFn: (broker) =>
              broker.fullName +
              broker.phone +
              broker.email +
              broker.city +
              broker.state +
              AppDateFormatter.formatDateMMddyyyy(broker.joiningDate) +
              broker.totalDownlineAgents.toString() +
              broker.totalSales.toString() +
              broker.salesVolume.toString(),
          // Dynamic filtering system
          filterColumnNames: [
            brokerListNameColumnHeader,
            brokerListJoinDateColumnHeader,
            brokerListStatusColumnHeader,
            brokerListStateColumnHeader,
            brokerListCityColumnHeader,
          ],
          filterValueExtractors: {
            brokerListJoinDateColumnHeader: (broker) {
              return AppDateFormatter.formatDateMMddyyyy(broker.joiningDate);
            },
          },
          // Date filter configuration - specify which columns should use date filters
          dateFilterColumns: const [
            brokerListJoinDateColumnHeader, // Join date should use calendar picker
          ],
          dateFilterBannerTexts: const {
            brokerListJoinDateColumnHeader: 'Users joined on or after',
          },
          filterOptions: {
            if (brokerFilterOptions != null)
              brokerListNameColumnHeader: brokerFilterOptions.value,
            if (userStatusFilterOptions.value.isNotEmpty)
              brokerListStatusColumnHeader: userStatusFilterOptions.value,
            if (stateFilterOptions.value.isNotEmpty)
              brokerListStateColumnHeader: stateFilterOptions.value,
            if (cityFilterOptions.value.isNotEmpty)
              brokerListCityColumnHeader: cityFilterOptions.value,
          },
          filterOnChangeConfig: {
            brokerListNameColumnHeader: false,
            brokerListStatusColumnHeader: false,
            brokerListStateColumnHeader: true,
            brokerListCityColumnHeader: true,
          },
          onFilterChange:
              (
                filterKey,
                selectedValue, [
                clearDependentFilters,
                searchString,
              ]) async {
                if (searchString != null) {
                  // Store search text for any filter that uses search
                  filterSearchTexts.value = {
                    ...filterSearchTexts.value,
                    filterKey: searchString,
                  };
                }
                if (filterKey == brokerListStateColumnHeader &&
                    searchString == null) {
                  filterSearchTexts.value = {
                    ...filterSearchTexts.value,
                    brokerListCityColumnHeader: '',
                  };
                  if (previousState.value != selectedValue) {
                    if (clearDependentFilters != null) {
                      clearDependentFilters([brokerListCityColumnHeader]);
                    }
                    previousState.value = selectedValue;
                  }
                  selectedState.value = selectedValue;
                  await filterCubit.getCitiesFilterOptions(
                    selectedState: selectedValue,
                    searchString: null,
                  );
                  final citiesState = filterCubit.state;
                  if (citiesState is FilterLoaded) {
                    cityFilterOptions.value = citiesState.filterOptions;
                  }
                }

                // Handle city search - this will be called as user types
                if (filterKey == brokerListCityColumnHeader &&
                    searchString != null) {
                  await filterCubit.getCitiesFilterOptions(
                    selectedState: selectedState.value ?? selectedValue,
                    searchString: searchString,
                  );
                  final citiesState = filterCubit.state;
                  if (citiesState is FilterLoaded) {
                    cityFilterOptions.value = citiesState.filterOptions;
                  }
                }
              },
          filterSearchConfig: {
            brokerListNameColumnHeader: true,
            brokerListStatusColumnHeader: false,
            brokerListStateColumnHeader: true,
            brokerListCityColumnHeader: true,
          },
          showCrossButtonForFilter: const {
            brokerListNameColumnHeader: true,
            brokerListJoinDateColumnHeader: true,
            brokerListStatusColumnHeader: true,
            brokerListStateColumnHeader: true,
            brokerListCityColumnHeader: true,
          },
          columnNames: formattedHeaders,
          cellBuilders: [
            (broker) => broker.fullName,
            (broker) => PhoneUtils.formatPhoneNumber(broker.phone),
            (broker) => broker.email,
            (broker) => AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
            (broker) => broker.state,
            (broker) => broker.city,
            (broker) => broker.totalDownlineAgents.toString(),
            (broker) => '${broker.totalSales}',
            (broker) => '${formatCurrencyDollar(broker.salesVolume)}',
            (broker) => '${formatCurrencyDollar(broker.salesCommission)}',
            (broker) => '${formatCurrencyDollar(broker.revenueShare)}',

            (broker) => broker.isActive
                ? UserStatus.active.value
                : UserStatus.inactive.value,
          ],
          iconCellBuilders: [
            (broker) => TableCellData(
              text: broker.fullName,
              leftIconAsset: "$iconAssetpath/agent_round.png",
              iconSize: 30,
            ),
            null, // contact - no icon
            null, // email - no icon
            null, // address - no icon
            null, // joinDate - no icon
            null, // agents - no icon
            null, // totalSales - no icon
            null, // totalrevenue - no icon
            null, // commision- no icon
            null, // city - no icon
            null, // revenue share
            null, // status - no icon
          ],
          useIconBuilders: [
            true, // name - use icon
            false, // contact - use text
            false, // email - use text
            false, // address - use text
            false, // joinDate - use text
            false, // agents - use text
            false, // totalSales - use text
            false, // totalrevenue - use text
            false, // commision - use text
            false, // city - use text
            false, //revenue share
            false, // status - use text
          ],
          widgetCellBuilders: [
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            (context, broker) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: broker.isActive
                    ? AppTheme.agentStatusActiveBg.withAlpha(36)
                    : AppTheme.agentStatusInactiveBg.withAlpha(36),
                borderRadius: BorderRadius.circular(
                  20,
                ), // More rounded for oval shape
              ),
              child: Text(
                broker.isActive
                    ? UserStatus.active.value
                    : UserStatus.inactive.value,
                style: AppFonts.mediumTextStyle(
                  12,
                  color: broker.isActive
                      ? AppTheme
                            .statusActiveText // Darker green text
                      : AppTheme.statusInactiveText,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          useWidgetBuilders: [
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            true, // status
          ],
          actionBuilders: [
            (context, broker) => ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isCompact: true,
              isMobile: false,
              name: broker.fullName,
              tooltipMessage: AppStrings.viewAgentHierarchy,
            ),
            (context, agent) => ActionButtonEye(
              onPressed: () {
                onNavigateToCommissionInfo!(
                  agent.userId,
                  agent.fullName,
                  isBrokerageList: true,
                );
              },
              isCompact: true,
              isMobile: false,
              icon:
                  "$iconAssetpath/link_icon.png", // optional custom icon asset
              padding: 2.0,
              name: agent.fullName,
              tooltipMessage: AppStrings.viewAgentHierarchy,
            ),
          ],

          mobileCardBuilder: (context, broker) =>
              _buildMobileBrokerCard(broker, context),
          onSort: handleSort,
          emptyStateMessage: noDataAvailable,
          pageCount: pageCount.value,
          totalElements: totalBrokers.value,
          isLoading: state is BrokerLoading,
          useExpandedView: false,
          // onDateFilterChanged: (value) async {
          //   selectedDate.value = value;
          //   await _fetchBrokers(
          //     context,
          //     user,
          //     selectedDate: value,
          //     page: currentpage.value,
          //     searchString: searchString.value,
          //   );
          // },
          currentPageIndex: currentpage.value,
          onAllFiltersChanged: (allFilters) async {
            currentFilters.value = allFilters;
            currentpage.value = 0;
            DateTime? joiningDate;
            String? statusId;
            String? brokerageId;

            if (allFilters.containsKey(brokerListNameColumnHeader)) {
              String? brokerageId =
                  allFilters[brokerListNameColumnHeader] as String?;
              associatedBrokerage.value = brokerageId;
            } else {
              associatedBrokerage.value = null;
            }
            // Handle joining date filter
            if (allFilters.containsKey(brokerListJoinDateColumnHeader)) {
              joiningDate =
                  allFilters[brokerListJoinDateColumnHeader] as DateTime?;
              selectedDate.value = joiningDate;
            } else {
              selectedDate.value = null;
            }

            // Handle status filter
            if (allFilters.containsKey(brokerListStatusColumnHeader)) {
              statusId = allFilters[brokerListStatusColumnHeader] as String?;
              final selectedFilter = userStatusFilterOptions.value.firstWhere(
                (filter) => filter.id == statusId,
                orElse: () => TableFilter(id: '', key: '', value: ''),
              );
              if (selectedFilter.value == UserStatus.active.value) {
                isActive.value = true;
              } else if (selectedFilter.value == UserStatus.inactive.value) {
                isActive.value = false;
              } else if (selectedFilter.value ==
                  UserStatus.pendingVerification.value) {
                isActive.value = null;
              } else {
                isActive.value = null;
              }
            } else {
              isActive.value = null;
            }

            // Handle state filter
            if (allFilters.containsKey(brokerListStateColumnHeader)) {
              String? stateId =
                  allFilters[brokerListStateColumnHeader] as String?;

              if (previousState.value != stateId) {
                selectedCity.value = null;
                previousState.value = stateId;
              }
              selectedState.value = stateId;
            } else {
              selectedState.value = null;
              selectedCity.value = null;
            }

            // Handle city filter
            if (allFilters.containsKey(brokerListCityColumnHeader)) {
              String? cityId =
                  allFilters[brokerListCityColumnHeader] as String?;
              selectedCity.value = cityId;
            } else {
              selectedCity.value = null;
            }

            // Fetch brokers with updated filters
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              page: 0,
              searchString: searchString.value,
              isActive: isActive.value,
              associatedBrokerage: associatedBrokerage.value,
              sortColumn: sortBy.value,
              sortOrder: sortOrder.value,
              cityId: selectedCity.value,
              stateId: selectedState.value,
            );
          },
          handleTableSearch: (value) async {
            // Cancel any pending search
            searchDebouncer.value?.cancel();

            // Store the search value immediately for UI feedback
            searchString.value = value;

            // Don't search if value hasn't changed
            if (lastSearchValue.value == value) {
              return;
            }

            lastSearchValue.value = value;

            // Debounce: wait 300ms before making the API call
            searchDebouncer.value = Timer(
              const Duration(milliseconds: 300),
              () async {
                currentpage.value = 0;
                await _fetchBrokers(
                  context,
                  user,
                  selectedDate: selectedDate.value,
                  page: 0,
                  searchString: value,
                  isActive: isActive.value,
                  associatedBrokerage: associatedBrokerage.value,
                  sortColumn: sortBy.value,
                  sortOrder: sortOrder.value,
                  cityId: selectedCity.value,
                  stateId: selectedState.value,
                );
              },
            );
          },
          handlePagination: (page) async {
            currentpage.value = page;
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              searchString: searchString.value,
              page: page,
              isActive: isActive.value,
              associatedBrokerage: associatedBrokerage.value,
              sortColumn: sortBy.value,
              sortOrder: sortOrder.value,
              cityId: selectedCity.value,
              stateId: selectedState.value,
            );
          },
          filterTooltips: {
            brokerListCityColumnHeader: AppStrings.cityTooltipFilter,
          },
          filterInitialSearchText: filterSearchTexts.value,
          onResetFilters: ({required bool isFilterOptionOnlyReload}) async {
            if (isFilterOptionOnlyReload) {
              filterSearchTexts.value = {};
              //reset filter option api
              await updateDropDownOptions(
                filterCubit,
                brokerFilterOptions,
                userStatusFilterOptions,
                stateFilterOptions,
                cityFilterOptions,
              );
            } else {
              filterSearchTexts.value = {};
              //reset filter option api
              await updateDropDownOptions(
                filterCubit,
                brokerFilterOptions,
                userStatusFilterOptions,
                stateFilterOptions,
                cityFilterOptions,
              );
              selectedDate.value = null; // DateTime? type (null for no filter)
              searchString.value = ''; // String? type (empty string)
              currentpage.value =
                  1; // int type (reset to first page, usually 0 or 1)
              isActive.value =
                  null; // bool? type (null for no filter, true/false for specific status)
              associatedBrokerage.value =
                  null; // String? type (null for no filter)
              sortBy.value =
                  'created_at'; // String? type (default sort column, or set to '')
              sortOrder.value =
                  'ASC'; // String? type (default sort order, or set to '')
              previousState.value = null;
              selectedState.value = null;
              selectedCity.value = null;
              await _fetchBrokers(
                context,
                user,
                selectedDate: selectedDate.value,
                searchString: searchString.value,
                page: currentpage.value,
                isActive: isActive.value,
                associatedBrokerage: associatedBrokerage.value,
                sortColumn: sortBy.value,
                sortOrder: sortOrder.value,
                cityId: selectedCity.value,
                stateId: selectedState.value,
              );
            }
          },
        );
      },
    );
  }

  Future<void> updateDropDownOptions(
    FilterCubit filterCubit,
    ValueNotifier<List<TableFilter>> brokerFilterOptions,
    ValueNotifier<List<TableFilter>> userStatusFilterOptions,
    ValueNotifier<List<TableFilter>> stateFilterOptions,
    ValueNotifier<List<TableFilter>> cityFilterOptions,
  ) async {
    await filterCubit.getBrokerageFilterOptions();
    final state = filterCubit.state;
    if (state is FilterLoaded) {
      brokerFilterOptions.value = state.filterOptions;
    }

    await filterCubit.getUserStatusFilterOptions();
    final statusState = filterCubit.state;
    if (statusState is FilterLoaded) {
      userStatusFilterOptions.value = statusState.filterOptions;
    }

    await filterCubit.getStatesFilterOptions(selectedCountry: '');
    final statesState = filterCubit.state;
    if (statesState is FilterLoaded) {
      stateFilterOptions.value = statesState.filterOptions;
    }

    await filterCubit.getCitiesFilterOptions(
      selectedState: null,
      searchString: null,
    );
    final citiesState = filterCubit.state;
    if (citiesState is FilterLoaded) {
      cityFilterOptions.value = citiesState.filterOptions;
    }
  }

  void _onBrokerAction(BuildContext context, Brokers broker) {
    if (onNavigateToAgentNetwork != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetwork!(broker);
      } catch (e) {
        // If no matching broker found, show error message
        AppSnackBar.showSnackBar(
          context,
          'Broker not found: ${broker.fullName}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      AppSnackBar.showSnackBar(
        context,
        'Action clicked for ${broker.fullName}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Widget _buildMobileBrokerCard(Brokers broker, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SelectableText(
                broker.fullName,
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SelectableText(
            '$brokerListContactColumnHeader: ${PhoneUtils.formatPhoneNumber(broker.phone)}',
          ),
          SelectableText('$brokerListEmailColumnHeader: ${broker.email}'),
          SelectableText(
            '$brokerListJoinDateColumnHeader: ${AppDateFormatter.formatDateMMddyyyy(broker.joiningDate)}',
          ),
          SelectableText('$brokerListStateColumnHeader: ${broker.state}'),
          SelectableText('$brokerListCityColumnHeader: ${broker.city}'),
          SelectableText(
            '$brokerListAgentsColumnHeader: ${broker.totalDownlineAgents}',
          ),
          SelectableText(
            '$brokerListTotalSalesColumnHeader: ${broker.totalSales}',
          ),
          SelectableText(
            '$brokerListTotalRevenueColumnHeader: ${formatCurrencyDollar(broker.salesVolume)}',
          ),
          SelectableText(
            '$brokerListCommissionColumnHeader: ${formatCurrencyDollar(broker.salesCommission)}',
          ),
          SelectableText(
            '$brokerListrevenueShareColumnHeader: ${formatCurrencyDollar(broker.revenueShare)}',
          ),
          SelectableText(
            '$brokerListStatusColumnHeader: ${broker.isActive ? UserStatus.active.value : UserStatus.inactive.value}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // View Agent Details Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () => _onBrokerAction(context, broker),
                    isMobile: true,
                    name: broker.fullName,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                ),
                const SizedBox(height: 8), // Spacing between buttons
                // View Commission Revenue Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () {
                      if (onNavigateToCommissionInfo != null) {
                        onNavigateToCommissionInfo!(
                          broker.userId,
                          broker.fullName,
                          isBrokerageList: true,
                        );
                      }
                    },
                    isMobile: true,
                    icon: "$iconAssetpath/link_icon.png",
                    padding: 2.0,
                    name: broker.fullName,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _fetchBrokers(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required int page,
    required String? searchString,
    required bool? isActive,
    required String? associatedBrokerage,
    required String? sortColumn,
    required String? sortOrder,
    required String? cityId,
    required String? stateId,
  }) async {
    // String? formattedDate = selectedDate != null
    //     ? AppDateFormatter.formatDateyyyyMMdd(selectedDate)
    //     : null;

    final dateString = AppDateFormatter.formatToUtcIso8601(selectedDate);

    final payload = {
      "page": page > 0 ? page - 1 : 0,
      "size": 10,
      "sortBy": sortColumn ?? "created_at",
      "sortDirection": sortOrder ?? "ASC",
      "searchString": searchString,
      "associatedBrokerageId": associatedBrokerage,
      "agentId": null,
      "active": isActive,
      "joiningDate": dateString,
      "userId": user?.userId,
      "cityId": cityId,
      "stateId": stateId,
    };
    await context.read<BrokerCubit>().fetchBrokers(payload);
  }
}
