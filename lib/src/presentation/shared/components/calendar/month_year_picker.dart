import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';

class PickerPosition {
  final double left;
  final double top;

  const PickerPosition({required this.left, required this.top});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PickerPosition && other.left == left && other.top == top;
  }

  @override
  int get hashCode => left.hashCode ^ top.hashCode;
}

class MonthYearPicker extends StatefulWidget {
  final DateTime initialDate;
  final ValueChanged<DateTime> onDateSelected;
  final VoidCallback onCancel;
  final GlobalKey anchorKey;
  final DateTime? minDate;
  final DateTime? maxDate;

  const MonthYearPicker({
    Key? key,
    required this.initialDate,
    required this.onDateSelected,
    required this.onCancel,
    required this.anchorKey,
    this.minDate,
    this.maxDate,
  }) : super(key: key);

  @override
  State<MonthYearPicker> createState() => _MonthYearPickerState();
}

class _MonthYearPickerState extends State<MonthYearPicker> {
  late int changedYear;
  late int selectedYear;
  late int selectedMonth;
  bool showingYears = false;
  int yearPageIndex = 0;

  final DateTime today = DateTime.now();
  // final DateTime firstDate = DateTime(2015, 1, 1);
  // late final DateTime lastDate = DateTime(today.year, 12, 31);
  late final DateTime firstDate;
  late final DateTime lastDate;

  @override
  void initState() {
    super.initState();
    // Use provided min/max dates or defaults.
    firstDate = widget.minDate ?? DateTime(2015, 1, 1);
    lastDate = widget.maxDate ?? DateTime(today.year, 12, 31);

    selectedYear = widget.initialDate.year.clamp(firstDate.year, lastDate.year);
    selectedMonth = widget.initialDate.month;

    // Validate selected month against date constraints
    if (selectedYear == lastDate.year && selectedMonth > lastDate.month) {
      selectedMonth = lastDate.month;
    }
    if (selectedYear == firstDate.year && selectedMonth < firstDate.month) {
      selectedMonth = firstDate.month;
    }
    // if (selectedYear == today.year && selectedMonth > today.month) {
    //   selectedMonth = today.month;
    // }
    final yearIndex = selectedYear - firstDate.year;
    yearPageIndex = yearIndex ~/ 12;
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double pickerWidth = _calculatePickerWidth(screenWidth);
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.light(
          primary: AppTheme.primaryBlueColor,
          onPrimary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
        ),
      ),
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.white,
        child: SizedBox(
          width: pickerWidth,
          height: 350,
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              Expanded(
                child: showingYears ? _buildYearGrid() : _buildMonthGrid(),
              ),
              const SizedBox(height: 4),
              _buildActionButtons(),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: _shouldDisableLeftArrow()
                ? null
                : () {
                    setState(() {
                      if (showingYears) {
                        if (yearPageIndex > 0) {
                          yearPageIndex--;
                        }
                      } else {
                        selectedYear = (selectedYear - 1)
                            .clamp(firstDate.year, lastDate.year)
                            .toInt();
                      }
                    });
                  },
            icon: Icon(
              Icons.chevron_left,
              color: _shouldDisableLeftArrow()
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.black,
            ),
          ),
          InkWell(
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            mouseCursor: SystemMouseCursors.click,
            onTap: () {
              setState(() {
                showingYears = !showingYears;
                if (showingYears) {
                  final yearIndex = selectedYear - firstDate.year;
                  yearPageIndex = yearIndex ~/ 12;
                }
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                showingYears
                    ? _getYearPageRangeText()
                    : selectedYear.toString(),
                style: AppFonts.semiBoldTextStyle(16),
              ),
            ),
          ),
          InkWell(
            mouseCursor: _shouldDisableRightArrow()
                ? SystemMouseCursors.basic
                : SystemMouseCursors.click,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: IconButton(
              onPressed: _shouldDisableRightArrow()
                  ? null
                  : () {
                      setState(() {
                        if (showingYears) {
                          final totalYears = lastDate.year - firstDate.year + 1;
                          final maxPageIndex = (totalYears - 1) ~/ 12;
                          if (yearPageIndex < maxPageIndex) {
                            yearPageIndex++;
                          }
                        } else {
                          selectedYear = (selectedYear + 1)
                              .clamp(firstDate.year, lastDate.year)
                              .toInt();
                        }
                      });
                    },
              icon: Icon(
                Icons.chevron_right,
                color: _shouldDisableRightArrow()
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getYearPageRangeText() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final endYear = startYear + (remainingYears > 12 ? 11 : remainingYears - 1);
    return '$startYear - $endYear';
  }

  bool _shouldDisableRightArrow() {
    if (showingYears) {
      final totalYears = lastDate.year - firstDate.year + 1;
      final maxPageIndex = (totalYears - 1) ~/ 12;

      // Disable if we're at last page
      if (yearPageIndex >= maxPageIndex) {
        return true;
      }

      // Check if the next page would contain only years beyond maxDate
      final nextPageStartYear = firstDate.year + ((yearPageIndex + 1) * 12);
      return nextPageStartYear > lastDate.year;
    } else {
      // For month navigation, disable if we're at the max allowed year
      return selectedYear >= lastDate.year;
    }
  }

  bool _shouldDisableLeftArrow() {
    if (showingYears) {
      // For year navigation, disable if we're at the first page
      return yearPageIndex <= 0;
    } else {
      // For month navigation, disable if we're at the min allowed year
      return selectedYear <= firstDate.year;
    }
  }

  Widget _buildYearGrid() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final yearsToShow = remainingYears > 12 ? 12 : remainingYears;

    List<int> years = [];
    for (int i = 0; i < 12; i++) {
      years.add(startYear + i);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.8,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: years.length,
        itemBuilder: (context, index) {
          final year = years[index];
          final isSelected = year == selectedYear;
          final isDisabled = year < firstDate.year || year > lastDate.year;

          return Material(
            child: InkWell(
              // mouseCursor: SystemMouseCursors.click,
              mouseCursor: isDisabled
                  ? SystemMouseCursors.basic
                  : SystemMouseCursors.click,
              onTap: isDisabled
                  ? null
                  : () {
                      setState(() {
                        selectedYear = year;
                        // Reset selected month if selected year is now the current year and month > today.month
                        // if (selectedYear == today.year &&
                        //     selectedMonth > today.month) {
                        //   selectedMonth = today.month;
                        // }

                        // Adjust selected month if needed
                        if (selectedYear == lastDate.year &&
                            selectedMonth > lastDate.month) {
                          selectedMonth = lastDate.month;
                        }
                        if (selectedYear == firstDate.year &&
                            selectedMonth < firstDate.month) {
                          selectedMonth = firstDate.month;
                        }
                        showingYears = false;
                      });
                    },
              child: Container(
                decoration: BoxDecoration(
                  // color: isSelected ? AppTheme.primaryBlueColor : Colors.white,
                  color: isDisabled
                      ? Colors.grey.shade100
                      : (isSelected ? AppTheme.primaryBlueColor : Colors.white),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    // color: isSelected
                    //     ? AppTheme.primaryBlueColor
                    //     : Colors.grey.shade300,
                    color: isDisabled
                        ? Colors.grey.shade300
                        : (isSelected
                              ? AppTheme.primaryBlueColor
                              : Colors.grey.shade300),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    year.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      // color: isSelected ? Colors.white : Colors.black,
                      color: isDisabled
                          ? Colors.grey.shade400
                          : (isSelected ? Colors.white : Colors.black),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMonthGrid() {
    // final maxMonth = selectedYear < today.year ? 12 : today.month;
    // Determine min and max months based on selected year and date constraints
    int minMonth = 1;
    int maxMonth = 12;

    if (selectedYear == firstDate.year) {
      minMonth = firstDate.month;
    }

    if (selectedYear == lastDate.year) {
      maxMonth = lastDate.month;
    } else if (selectedYear > lastDate.year) {
      maxMonth = 0;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.5,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: 12, //maxMonth,
        itemBuilder: (context, index) {
          final month = index + 1;
          final isSelected = month == selectedMonth;
          final isDisabled = month < minMonth || month > maxMonth;

          return Material(
            child: InkWell(
              // mouseCursor: SystemMouseCursors.click,
              mouseCursor: isDisabled
                  ? SystemMouseCursors.basic
                  : SystemMouseCursors.click,
              onTap: isDisabled
                  ? null
                  : () {
                      setState(() {
                        selectedMonth = month;
                      });
                    },
              child: Container(
                decoration: BoxDecoration(
                  // color: isSelected ? AppTheme.primaryBlueColor : Colors.white,
                  color: isDisabled
                      ? Colors.grey.shade100
                      : (isSelected ? AppTheme.primaryBlueColor : Colors.white),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    // color: isSelected
                    //     ? AppTheme.primaryBlueColor
                    //     : Colors.grey.shade300,
                    color: isDisabled
                        ? Colors.grey.shade300
                        : (isSelected
                              ? AppTheme.primaryBlueColor
                              : Colors.grey.shade300),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    DateFormat('MMM').format(DateTime(selectedYear, month)),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      // color: isSelected ? Colors.white : Colors.black,
                      color: isDisabled
                          ? Colors.grey.shade400
                          : (isSelected ? Colors.white : Colors.black),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Cancel button styled like calendar cancel (white bg, border)
        Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: widget.onCancel,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding,
                vertical: defaultPadding / 2,
              ),
              decoration: BoxDecoration(
                color: Colors.white, // always white background
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.comboBoxBorder),
              ),
              child: Text(
                AppStrings.cancel,
                style: AppFonts.regularTextStyle(
                  13,
                  color: AppTheme.tableDataFont,
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Confirm button styled like calendar Apply button
        Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              widget.onDateSelected(DateTime(selectedYear, selectedMonth, 1));
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding,
                vertical: defaultPadding / 2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.selectedComboBoxBorder,
                border: Border.all(color: AppTheme.comboBoxBorder),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                AppStrings.confirmLabel,
                style: AppFonts.regularTextStyle(12, color: AppTheme.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

double _calculatePickerWidth(double screenWidth) {
  if (screenWidth <= 480) {
    return (screenWidth - 40).clamp(280, screenWidth - 20);
  } else if (screenWidth <= 800) {
    return (screenWidth * 0.8).clamp(300, 400);
  } else {
    return 320;
  }
}
