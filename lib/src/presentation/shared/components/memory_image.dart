import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';

class MemoryImageWidget extends StatelessWidget {
  final String? base64Data;
  final String cacheKey; // unique key for storing memory
  final String fallbackAssetPath;
  final double? scale;
  final BoxFit fit;

  const MemoryImageWidget({
    super.key,
    required this.base64Data,
    required this.cacheKey,
    required this.fallbackAssetPath,
    this.scale,
    this.fit = BoxFit.contain,
  });

  Uint8List? _decodeBase64(String? data) {
    if (data == null || data.isEmpty) return null;
    try {
      final cleaned = data.split(',').last;
      return base64Decode(cleaned);
    } catch (_) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 1️⃣ Try cache first
    Uint8List? bytes = ImageMemoryCache.get(cacheKey);

    // 2️⃣ Decode only if not cached
    if (bytes == null) {
      bytes = _decodeBase64(base64Data);

      if (bytes != null) {
        ImageMemoryCache.set(cacheKey, bytes);
      }
    }

    // 3️⃣ Render memory image if exists
    if (bytes != null && bytes.isNotEmpty) {
      return Image.memory(
        bytes,
        fit: fit,
        scale: scale ?? 0.0,
        gaplessPlayback: true, // prevents flicker on rebuild
        errorBuilder: (context, error, stack) =>
            Image.asset(fallbackAssetPath, scale: scale, fit: fit),
      );
    }

    return Image.asset(fallbackAssetPath, scale: scale, fit: fit);
  }
}

class ImageMemoryCache {
  static final Map<String, Uint8List> _cache = {};

  static Uint8List? get(String key) => _cache[key];

  static void set(String key, Uint8List bytes) {
    _cache[key] = bytes;
  }
}
