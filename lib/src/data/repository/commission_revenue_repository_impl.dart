import 'package:dio/dio.dart';
import 'package:neorevv/src/domain/models/commission_revenue_info_model.dart';
import 'package:neorevv/src/domain/repository/commission_repository.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';

class CommissionRevenueRepositoryImpl implements CommissionRepository {
  CommissionRevenueRepositoryImpl();

  @override
  Future<CommissionRevenueInfoResponse> fetchCommissionByUser(
    Map<String, dynamic> requestBody,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final url = APIConfig.commissionRevenueByUser;
      final response = await dio.post(url, data: requestBody);

      if (response.statusCode == 200) {
        try {
          final apiResponse = CommissionRevenueInfoResponse.fromJson(
            response.data,
          );
          return apiResponse;
        } catch (parseError) {
          rethrow;
        }
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgents);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
