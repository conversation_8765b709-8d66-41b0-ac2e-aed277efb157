import 'dart:async';

/// A singleton service that notifies listeners when sales data should be refreshed
/// This is used to refresh the sales screen after document uploads complete
class SalesRefreshNotifier {
  static final SalesRefreshNotifier _instance =
      SalesRefreshNotifier._internal();

  factory SalesRefreshNotifier() => _instance;

  SalesRefreshNotifier._internal();

  final _refreshController = StreamController<void>.broadcast();

  /// Stream that emits when sales data should be refreshed
  Stream<void> get refreshStream => _refreshController.stream;

  /// Notify all listeners that sales data should be refreshed
  void notifyRefresh() {
    if (!_refreshController.isClosed) {
      _refreshController.add(null);
    }
  }

  /// Dispose the stream controller
  void dispose() {
    _refreshController.close();
  }
}
