import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppDateFormatter {
  AppDateFormatter._();

  static String formatDateMMddyyyy(DateTime? date) {
    if (date == null) {
      return '';
    }
    return DateFormat('MM-dd-yyyy').format(date);
  }

  static String formatDateyyyyMMdd(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  static DateTime? parseStringToDateMMddyyyy(String dateString) {
    try {
      return DateFormat('MM-dd-yyyy').parse(dateString);
    } catch (e) {
      return null;
    }
  }

  //DateFormat('MMMM yyyy').
  static String formatCalendarHeader(DateTime date) {
    return DateFormat('MMMM yyyy').format(date);
  }

  /// format joining dates, sale dates, listing dates
  static DateTime? formatMonthYearJson(String? date, {bool isDefault = false}) {
    if (date == null || date.isEmpty) {
      return null;
    }
    return isDefault
        ? DateFormat("MM-dd-yyyy").parse(date)
        : formatToLocalTimeZone(date);
  }

  //DateFormat('yyyy-MM').format
  static String formatMonthYear(DateTime date) {
    return DateFormat('yyyy-MM').format(date);
  }

  static convertDateToApiFormat(String dateString) {
    try {
      final DateTime date = DateFormat("MM-dd-yyyy").parse(dateString);
      return DateFormat("yyyy-MM-dd").format(date);
    } catch (e) {
      // return the original string if parsing fails
      return dateString;
    }
  }

  static DateTime? formatToLocalTimeZone(String? date) {
    if (date == null || date.isEmpty) {
      return null;
    }
    return DateTime.tryParse(date)?.toLocal();
  }

  static String? formatToUtcIso8601(DateTime? date) {
    if (date == null) {
      return null;
    }
    return date.toUtc().toIso8601String();
  }
}
