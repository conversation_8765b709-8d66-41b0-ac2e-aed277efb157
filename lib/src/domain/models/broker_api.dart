// To parse this JSON data, do
//
//     final brokerApi = brokerApiFrom<PERSON>son(jsonString);

import 'dart:convert';

import 'package:intl/intl.dart';

import '../../core/utils/date_formatter.dart';

BrokerApi brokerApiFromJson(String str) => BrokerApi.fromJson(json.decode(str));

String brokerApiToJson(BrokerApi data) => json.encode(data.toJson());

class BrokerApi {
  List<Brokers> brokers;
  Pageable? pageable;
  int totalElements;
  int totalPages;
  bool last;
  int size;
  int number;
  BrokerSort? sort;
  int numberOfElements;
  bool first;
  bool empty;

  BrokerApi({
    required this.brokers,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  Broker<PERSON><PERSON> copyWith({
    List<Brokers>? content,
    Pageable? pageable,
    int? totalElements,
    int? totalPages,
    bool? last,
    int? size,
    int? number,
    BrokerSort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,
  }) => BrokerApi(
    brokers: content ?? this.brokers,
    pageable: pageable ?? this.pageable,
    totalElements: totalElements ?? this.totalElements,
    totalPages: totalPages ?? this.totalPages,
    last: last ?? this.last,
    size: size ?? this.size,
    number: number ?? this.number,
    sort: sort ?? this.sort,
    numberOfElements: numberOfElements ?? this.numberOfElements,
    first: first ?? this.first,
    empty: empty ?? this.empty,
  );

  factory BrokerApi.fromJson(Map<String, dynamic> json) => BrokerApi(
    brokers: json["content"] != null
        ? List<Brokers>.from(json["content"].map((x) => Brokers.fromJson(x)))
        : [],
    pageable: Pageable.fromJson(json["pageable"]),
    totalElements: json["totalElements"] ?? 0,
    totalPages: json["totalPages"] ?? 0,
    last: json["last"] ?? false,
    size: json["size"] ?? 0,
    number: json["number"] ?? 0,
    sort: BrokerSort.fromJson(json["sort"]),
    numberOfElements: json["numberOfElements"] ?? 0,
    first: json["first"] ?? false,
    empty: json["empty"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "content": List<dynamic>.from(brokers.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "totalElements": totalElements,
    "totalPages": totalPages,
    "last": last,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "numberOfElements": numberOfElements,
    "first": first,
    "empty": empty,
  };
}

class Brokers {
  String userId;
  String fullName;
  String email;
  String phone;
  String state;
  String city;
  DateTime? joiningDate;
  String companyName;
  double salesVolume;
  double revenueShare;
  int totalDownlineAgents;
  int totalSales;
  double salesCommission;
  String recruiterName;
  String associatedBrokerageId;
  String associatedBrokerageFullName;
  int depthLevel;
  bool isActive;

  Brokers({
    required this.userId,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.state,
    required this.city,
    required this.joiningDate,
    required this.companyName,
    required this.salesVolume,
    required this.totalDownlineAgents,
    required this.totalSales,
    required this.salesCommission,
    required this.recruiterName,
    required this.associatedBrokerageId,
    required this.associatedBrokerageFullName,
    required this.depthLevel,
    required this.isActive,
    required this.revenueShare,
  });

  Brokers copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? phone,
    String? state,
    String? city,
    DateTime? joiningDate,
    String? companyName,
    double? salesVolume,
    double? revenueShare,
    int? totalDownlineAgents,
    int? totalSales,
    double? salesCommission,
    String? recruiterName,
    String? associatedBrokerageId,
    String? associatedBrokerageFullName,
    int? depthLevel,
    bool? isActive,
  }) => Brokers(
    userId: userId ?? this.userId,
    fullName: fullName ?? this.fullName,
    email: email ?? this.email,
    phone: phone ?? this.phone,
    state: state ?? this.state,
    city: city ?? this.city,
    joiningDate: joiningDate ?? this.joiningDate,
    companyName: companyName ?? this.companyName,
    salesVolume: salesVolume ?? this.salesVolume,
    revenueShare: revenueShare ?? this.revenueShare,
    totalDownlineAgents: totalDownlineAgents ?? this.totalDownlineAgents,
    totalSales: totalSales ?? this.totalSales,
    salesCommission: salesCommission ?? this.salesCommission,
    recruiterName: recruiterName ?? this.recruiterName,
    associatedBrokerageId: associatedBrokerageId ?? this.associatedBrokerageId,
    associatedBrokerageFullName:
        associatedBrokerageFullName ?? this.associatedBrokerageFullName,
    depthLevel: depthLevel ?? this.depthLevel,
    isActive: isActive ?? this.isActive,
  );

  factory Brokers.fromJson(Map<String, dynamic> json) => Brokers(
    userId: json["userId"] ?? "",
    fullName: json["fullName"] ?? "",
    email: json["email"] ?? "",
    phone: json["phone"] ?? "",
    state: json["state"] ?? "",
    city: json["city"] ?? "",
    joiningDate: AppDateFormatter.formatMonthYearJson(json["joiningDate"]),
    companyName: json["companyName"] ?? "",
    salesVolume: (json["salesVolume"] ?? 0) * 1.0,
    revenueShare: (json["revenueShare"] ?? 0) * 1.0,
    totalDownlineAgents: json["totalDownlineAgents"] ?? 0,
    totalSales: json["totalSales"] ?? 0,
    salesCommission: (json["salesCommission"] ?? 0) * 1.0,
    recruiterName: json["recruiterName"] ?? "",
    associatedBrokerageId: json["associatedBrokerageId"] ?? "",
    associatedBrokerageFullName: json["associatedBrokerageFullName"] ?? "",
    depthLevel: json["depthLevel"] ?? 0,
    isActive: json["isActive"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "fullName": fullName,
    "email": email,
    "phone": phone,
    "state": state,
    "city": city,
    "joiningDate": joiningDate,
    "companyName": companyName,
    "salesVolume": salesVolume,
    "revenueShare": revenueShare,
    "totalDownlineAgents": totalDownlineAgents,
    "totalSales": totalSales,
    "salesCommission": salesCommission,
    "recruiterName": recruiterName,
    "associatedBrokerageId": associatedBrokerageId,
    "associatedBrokerageFullName": associatedBrokerageFullName,
    "depthLevel": depthLevel,
    "isActive": isActive,
  };
}

class Pageable {
  int pageNumber;
  int pageSize;
  BrokerSort? sort;
  int offset;
  bool paged;
  bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  Pageable copyWith({
    int? pageNumber,
    int? pageSize,
    BrokerSort? sort,
    int? offset,
    bool? paged,
    bool? unpaged,
  }) => Pageable(
    pageNumber: pageNumber ?? this.pageNumber,
    pageSize: pageSize ?? this.pageSize,
    sort: sort ?? this.sort,
    offset: offset ?? this.offset,
    paged: paged ?? this.paged,
    unpaged: unpaged ?? this.unpaged,
  );

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
    pageNumber: json["pageNumber"] ?? 0,
    pageSize: json["pageSize"] ?? 0,
    sort: BrokerSort.fromJson(json["sort"]),
    offset: json["offset"] ?? 0,
    paged: json["paged"] ?? false,
    unpaged: json["unpaged"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "pageNumber": pageNumber,
    "pageSize": pageSize,
    "sort": sort?.toJson(),
    "offset": offset,
    "paged": paged,
    "unpaged": unpaged,
  };
}

class BrokerSort {
  bool sorted;
  bool unsorted;
  bool empty;

  BrokerSort({
    required this.sorted,
    required this.unsorted,
    required this.empty,
  });

  BrokerSort copyWith({bool? sorted, bool? unsorted, bool? empty}) =>
      BrokerSort(
        sorted: sorted ?? this.sorted,
        unsorted: unsorted ?? this.unsorted,
        empty: empty ?? this.empty,
      );

  factory BrokerSort.fromJson(Map<String, dynamic> json) => BrokerSort(
    sorted: json["sorted"] ?? false,
    unsorted: json["unsorted"] ?? false,
    empty: json["empty"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "sorted": sorted,
    "unsorted": unsorted,
    "empty": empty,
  };
}
