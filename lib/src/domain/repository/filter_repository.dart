import '../models/filter/table_filter.dart';

abstract class FilterRepository {
  Future<List<TableFilter>> getBrokerageFilterOptions();
  Future<List<TableFilter>> getAgentFilterOptions(String? userId, String? selectedValue, bool? includeSelf);
  Future<List<TableFilter>> getProperyTypeFilterOptions();
  Future<List<TableFilter>> getUserStatusFilterOptions();
  Future<List<TableFilter>> getCountiresFilterOptions();
  Future<List<TableFilter>> getStatesFilterOptions(String? selectedCountry);
  Future<List<TableFilter>> getCitiesFilterOptions(String? selectedState, String? searchString);
}
