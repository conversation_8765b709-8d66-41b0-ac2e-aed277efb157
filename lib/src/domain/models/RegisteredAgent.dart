// To parse this JSON data, do
//
//     final registeredAgent = registeredAgentFromJson(jsonString);

import 'dart:convert';

RegisteredAgent registeredAgentFromJson(String str) =>
    RegisteredAgent.fromJson(json.decode(str));

String registeredAgentToJson(RegisteredAgent data) =>
    json.encode(data.toJson());

class RegisteredAgent {
  String? id;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? organizationName;
  String? roleName;
  String? city;
  String? cityName;
  String? state;
  String? country;
  String? licenseNumber;
  String? zipCode;
  String? recruiterId;
  String? inviteCode;
  dynamic additionalInfo;
  List<UploadedDocument>? uploadedDocuments;

  RegisteredAgent({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.organizationName,
    this.roleName,
    this.city,
    this.cityName,
    this.state,
    this.country,
    this.licenseNumber,
    this.zipCode,
    this.recruiterId,
    this.inviteCode,
    this.additionalInfo,
    this.uploadedDocuments,
  });

  RegisteredAgent copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? organizationName,
    String? roleName,
    String? city,
    String? cityName,
    String? state,
    String? country,
    String? licenseNumber,
    String? zipCode,
    String? recruiterId,
    String? inviteCode,
    dynamic additionalInfo,
    List<UploadedDocument>? uploadedDocuments,
  }) => RegisteredAgent(
    id: id ?? this.id,
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    email: email ?? this.email,
    phone: phone ?? this.phone,
    organizationName: organizationName ?? this.organizationName,
    roleName: roleName ?? this.roleName,
    city: city ?? this.city,
    cityName: cityName ?? this.cityName,
    state: state ?? this.state,
    country: country ?? this.country,
    licenseNumber: licenseNumber ?? this.licenseNumber,
    zipCode: zipCode ?? this.zipCode,
    recruiterId: recruiterId ?? this.recruiterId,
    inviteCode: inviteCode ?? this.inviteCode,
    additionalInfo: additionalInfo ?? this.additionalInfo,
    uploadedDocuments: uploadedDocuments ?? this.uploadedDocuments,
  );

  factory RegisteredAgent.fromJson(Map<String, dynamic> json) =>
      RegisteredAgent(
        id: json["id"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        organizationName: json["organizationName"],
        roleName: json["roleName"],
        city: json["city"]?.toString(),
        cityName: json["cityName"],
        state: json["state"]?.toString(),
        country: json["country"]?.toString(),
        licenseNumber: json["licenseNumber"],
        zipCode: json["zipCode"],
        recruiterId: json["recruiterId"],
        inviteCode: json["inviteCode"],
        additionalInfo: json["additionalInfo"],
        uploadedDocuments: json["uploadedDocuments"] == null
            ? []
            : List<UploadedDocument>.from(
                json["uploadedDocuments"]!.map(
                  (x) => UploadedDocument.fromJson(x),
                ),
              ),
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "email": email,
    "phone": phone,
    "organizationName": organizationName,
    "roleName": roleName,
    "city": city,
    "cityName": cityName,
    "state": state,
    "country": country,
    "licenseNumber": licenseNumber,
    "zipCode": zipCode,
    "recruiterId": recruiterId,
    "inviteCode": inviteCode,
    "additionalInfo": additionalInfo,
    "uploadedDocuments": uploadedDocuments == null
        ? []
        : List<dynamic>.from(uploadedDocuments!.map((x) => x.toJson())),
  };
}

class UploadedDocument {
  String? fileName;
  int? fileSize;
  String? readableFileSize;

  UploadedDocument({this.fileName, this.fileSize, this.readableFileSize});

  factory UploadedDocument.fromJson(Map<String, dynamic> json) =>
      UploadedDocument(
        fileName: json["fileName"],
        fileSize: json["fileSize"] == null
            ? null
            : (json["fileSize"] is num
                  ? (json["fileSize"] as num).toInt()
                  : int.tryParse(json["fileSize"].toString())),
        readableFileSize: json["readableFileSize"],
      );

  Map<String, dynamic> toJson() => {
    "fileName": fileName,
    "fileSize": fileSize,
    "readableFileSize": readableFileSize,
  };
}
