import 'regex.dart';

class PhoneUtils {
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return phoneNumber;

    // Remove any spaces, dashes, parentheses, or other formatting, but keep the +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If it already starts with +1, extract the digits and reformat
    if (cleanNumber.startsWith('+1')) {
      String digits = cleanNumber.substring(2); // Remove +1
      if (digits.length == 10) {
        return '+1-${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}';
      }
    }

    // If it starts with 1 and has 11 digits total
    if (cleanNumber.startsWith('1') && cleanNumber.length == 11) {
      String digits = cleanNumber.substring(1); // Remove the leading 1
      if (digits.length == 10) {
        return '+1-${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}';
      }
    }

    // If it's a 10-digit number, add +1 and format
    if (cleanNumber.length == 10) {
      return '+1-${cleanNumber.substring(0, 3)}-${cleanNumber.substring(3, 6)}-${cleanNumber.substring(6)}';
    }

    // Return original if it doesn't match expected patterns
    return phoneNumber;
  }

  static String normalizePhoneForApi(
    String rawPhone, {
    String defaultCountryCode = '1',
  }) {
    final digits = rawPhone.replaceAll(RegExUtils.nonNumeric, '');

    if (digits.isEmpty) return '';

    if (digits.length == 10) {
      return '+$defaultCountryCode$digits';
    }

    if (digits.length == 11 && digits.startsWith(defaultCountryCode)) {
      return '+$digits';
    }

    // fallback: prefix whatever digits exist
    return '+$digits';
  }

  /// Return display-friendly phone like "*************"
  /// If rawPhone is empty returns "+<defaultCountryCode> " so the input shows prefix +1 and a trailing space.
  static String formatForDisplay(
    String rawPhone, {
    String defaultCountryCode = '1',
  }) {
    final digits = rawPhone.replaceAll(RegExUtils.nonNumeric, '');
    if (digits.isEmpty) return '+$defaultCountryCode ';
    // If exactly 10 digits, assume national number
    if (digits.length == 10) return '+$defaultCountryCode ${digits}';
    // If leading country code present (e.g. 1XXXXXXXXXX)
    if (digits.length > 10) {
      final prefixLen = digits.length - 10;
      final prefix = digits.substring(0, prefixLen);
      final national = digits.substring(prefixLen);
      return '+$prefix $national';
    }
    // Fallback - keep whatever and separate first digit as country code
    if (digits.length <= 10) return '+$defaultCountryCode $digits';
    return '+$digits';
  }
}
