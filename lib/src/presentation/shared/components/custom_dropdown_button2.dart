import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:neorevv/src/core/config/constants.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/models/filter/table_filter.dart';

class CustomDropdownButton2 extends StatefulWidget {
  final String hint;
  final List<TableFilter> items;
  final TableFilter? selectedItem;
  final String? selectedValue;
  final ValueChanged<String?> onChanged;
  final String? Function(String?)? validator;
  final bool enableSearch;
  final double? width;
  final double? height;
  final bool isRequired;
  final bool useApiSearch;
  final Function(String?)? onSearchChanged;
  final VoidCallback? onBeforeOpen;
  final bool showBorder;
  final double borderRadius; // NEW: Custom border radius
  final Color? fillColor; // NEW: Custom fill color
  final bool isMainContainerPaddingNeeded;
  final Color? hintTextColor;
  final Color? itemTextColor;
  final String? initialSearchText;
  final bool showInfoIcon;
  final String? infoIconTooltip;

  const CustomDropdownButton2({
    super.key,
    required this.hint,
    required this.items,
    this.selectedValue,
    this.selectedItem,
    required this.onChanged,
    this.validator,
    this.enableSearch = true,
    this.width,
    this.height = 50,
    this.isRequired = false,
    this.useApiSearch = false,
    this.onSearchChanged,
    this.onBeforeOpen,
    this.showBorder = true,
    this.borderRadius = 55, // Default to 8, can be set to 55 for rounded style
    this.fillColor,
    this.isMainContainerPaddingNeeded = true,
    this.hintTextColor = Colors.black,
    this.itemTextColor = Colors.black,
    this.initialSearchText,
    this.showInfoIcon = false,
    this.infoIconTooltip,
  });

  @override
  State<CustomDropdownButton2> createState() => _CustomDropdownButton2State();
}

class _CustomDropdownButton2State extends State<CustomDropdownButton2> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late ValueNotifier<String?> _valueNotifier;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  final FocusNode _focusNode = FocusNode();
  final FocusNode _searchFocusNode = FocusNode();
  final FocusNode _listFocusNode = FocusNode();
  int _focusedItemIndex = -1;
  bool _isKeyboardNavigation = false;
  final GlobalKey<FormFieldState<String>> _formFieldKey =
      GlobalKey<FormFieldState<String>>();
  final Map<int, GlobalKey> _itemKeys = {};
  TableFilter? _lastSelectedItem;

  @override
  void initState() {
    super.initState();
    _valueNotifier = ValueNotifier(_getValidatedValue(widget.selectedValue));
    try {
      if (_valueNotifier.value != null) {
        _lastSelectedItem = widget.items.firstWhere(
          (item) => item.id == _valueNotifier.value,
        );
      } else {
        _lastSelectedItem = widget.selectedItem;
      }
    } catch (_) {
      _lastSelectedItem = widget.selectedItem;
    }
    _focusNode.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _valueNotifier.value = _getValidatedValue(widget.selectedValue);
    });
  }

  @override
  void didUpdateWidget(CustomDropdownButton2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    final newValue = _getValidatedValue(widget.selectedValue);

    if (oldWidget.selectedValue != widget.selectedValue) {
      if (_valueNotifier.value != newValue) {
        _valueNotifier.value = newValue;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _formFieldKey.currentState?.didChange(newValue);
        });
      }
    }

    // update last selected TableFilter if selected value changed to a valid item
    if (newValue != null && newValue.isNotEmpty && newValue != 'Select') {
      try {
        final found = widget.items.firstWhere((item) => item.id == newValue);
        _lastSelectedItem = found;
      } catch (_) {
        // keep previous _lastSelectedItem
      }
    } else if (widget.selectedItem != null) {
      // if parent provided selectedItem directly update
      _lastSelectedItem = widget.selectedItem;
    } else if (widget.selectedValue == null || widget.selectedValue!.isEmpty) {
      // clear last selected if selectedValue is null or empty
      _lastSelectedItem = null;
    }

    if (_isOpen && oldWidget.items != widget.items) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateOverlay();
        _scrollToSelectedItemWithRetry();
      });
    }
  }

  String? _getValidatedValue(String? value) {
    // if (value == null || widget.items.isEmpty) return null;
    // final exists = widget.items.any((item) => item.id == value);
    // return exists ? value : null;
    return value;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _toggleDropdown() {
    if (_isOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _closeDropdown({bool returnFocus = true}) {
    setState(() {
      _isOpen = false;
      _focusedItemIndex = -1;
      _isKeyboardNavigation = false;
    });
    _removeOverlay();
    _itemKeys.clear(); // Clear item keys when dropdown closes

    if (returnFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _focusNode.requestFocus();
        }
      });
    }
  }

  void _selectValue(String? validatedValue) {
    // update last selected TableFilter only if we can resolve an item for the id
    if (validatedValue != null &&
        validatedValue.isNotEmpty &&
        validatedValue != 'Select') {
      try {
        final found = widget.items.firstWhere((i) => i.id == validatedValue);
        _lastSelectedItem = found;
      } catch (_) {
        // keep previous _lastSelectedItem
      }
    } else {
      _lastSelectedItem = null;
    }
    _valueNotifier.value = validatedValue;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formFieldKey.currentState?.didChange(validatedValue);
      _formFieldKey.currentState?.validate();
    });
    setState(() {
      _isOpen = false;
      _focusedItemIndex = -1;
      _isKeyboardNavigation = false;
    });
    _removeOverlay();
    _itemKeys.clear(); // Clear item keys when dropdown closes

    widget.onChanged(validatedValue);

    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });
  }

  void _openDropdown() {
    widget.onBeforeOpen?.call();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    if (widget.initialSearchText != null &&
        widget.initialSearchText!.isNotEmpty) {
      _searchController.text = widget.initialSearchText!;
      // Trigger search with initial text
      widget.onSearchChanged?.call(widget.initialSearchText);
    } else {
      _searchController.clear();
      widget.onSearchChanged?.call(null);
    }
    if (widget.initialSearchText == null || widget.initialSearchText!.isEmpty) {
      widget.onSearchChanged?.call(null);
    }
    setState(() {
      _isOpen = true;
    });

    // Use multiple frame callbacks to ensure the scroll controller is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.enableSearch) {
        _searchFocusNode.requestFocus();
      } else {
        _listFocusNode.requestFocus();
        final filteredItems = _getFilteredItems();
        _focusedItemIndex = filteredItems.indexWhere(
          (item) => item.id == _valueNotifier.value,
        );
        if (_focusedItemIndex == -1 && filteredItems.isNotEmpty) {
          _focusedItemIndex = 0;
        }
      }

      // Position cursor at end for initial search text - MOVED OUTSIDE THE IF BLOCK
      if (widget.enableSearch &&
          widget.initialSearchText != null &&
          widget.initialSearchText!.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 0), () {
          if (mounted && _searchFocusNode.hasFocus) {
            _searchController.selection = TextSelection.fromPosition(
              TextPosition(offset: _searchController.text.length),
            );
          }
        });
      }

      // Schedule scroll after the first frame to ensure ListView is built
      // Use a longer delay to ensure items are fully rendered
      Future.delayed(const Duration(milliseconds: 150), () {
        if (mounted && _isOpen) {
          _scrollToSelectedItemWithRetry();
        }
      });
    });
  }

  void _scrollToSelectedItemWithRetry({int retryCount = 0}) {
    if (_valueNotifier.value == null || widget.items.isEmpty) {
      return;
    }

    // If scroll controller doesn't have clients yet, retry up to 5 times
    if (!_scrollController.hasClients) {
      if (retryCount < 5) {
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted && _isOpen) {
            _scrollToSelectedItemWithRetry(retryCount: retryCount + 1);
          }
        });
      }
      return;
    }

    // Wait a bit more to ensure ListView items are rendered
    if (retryCount == 0) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _isOpen) {
          _scrollToSelectedItem();
        }
      });
    } else {
      _scrollToSelectedItem();
    }
  }

  void _scrollToSelectedItem() {
    if (_valueNotifier.value == null ||
        widget.items.isEmpty ||
        !_scrollController.hasClients) {
      return;
    }

    final filteredItems = _getFilteredItems();
    final selectedIndex = filteredItems.indexWhere(
      (item) => item.id == _valueNotifier.value,
    );

    if (selectedIndex == -1) {
      return;
    }

    // Try to get actual item height from rendered item
    double itemHeight = 44.0; // Default fallback

    // Try to measure from any rendered item
    for (var entry in _itemKeys.entries) {
      final key = entry.value;
      if (key.currentContext != null) {
        try {
          final RenderBox? box =
              key.currentContext!.findRenderObject() as RenderBox?;
          if (box != null && box.hasSize) {
            itemHeight = box.size.height;
            break; // Found a valid measurement
          }
        } catch (e) {
          // Continue to next item
        }
      }
    }

    final double viewportHeight = _scrollController.position.viewportDimension;
    final double itemTop = selectedIndex * itemHeight;
    final double itemBottom = itemTop + itemHeight;

    // Calculate target offset to center the item
    double targetOffset = itemTop - (viewportHeight / 2) + (itemHeight / 2);

    final double maxScroll = _scrollController.position.maxScrollExtent;

    // Clamp the offset to valid scroll range
    final double clampedOffset = targetOffset.clamp(0.0, maxScroll);

    // Use jumpTo for immediate positioning, or animateTo for smooth scroll
    _scrollController.animateTo(
      clampedOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _scrollToFocusedItem() {
    if (_focusedItemIndex == -1 || !_scrollController.hasClients) {
      return;
    }

    // Try to get the actual item's RenderBox for precise positioning
    final itemKey = _itemKeys[_focusedItemIndex];
    if (itemKey?.currentContext != null) {
      try {
        final RenderBox? itemBox =
            itemKey!.currentContext!.findRenderObject() as RenderBox?;
        final RenderBox? scrollBox =
            _scrollController.position.context.notificationContext
                    ?.findRenderObject()
                as RenderBox?;

        if (itemBox != null && scrollBox != null) {
          // Get the item's position relative to the scrollable area
          final itemPosition = itemBox.localToGlobal(
            Offset.zero,
            ancestor: scrollBox,
          );
          final itemHeight = itemBox.size.height;

          final double currentScrollOffset = _scrollController.offset;
          final double viewportHeight =
              _scrollController.position.viewportDimension;

          // Calculate item's position in the scroll view
          final double itemTop = currentScrollOffset + itemPosition.dy;
          final double itemBottom = itemTop + itemHeight;

          final double visibleTop = currentScrollOffset;
          final double visibleBottom = currentScrollOffset + viewportHeight;

          const double padding = 8.0; // Small padding for better visibility
          double? targetOffset;

          // If item is above the visible area, scroll up to show it
          if (itemTop < visibleTop) {
            targetOffset = (itemTop - padding).clamp(0.0, double.infinity);
          }
          // If item is below the visible area, scroll down to show it
          else if (itemBottom > visibleBottom) {
            targetOffset = itemBottom - viewportHeight + padding;
          }

          if (targetOffset != null) {
            final double maxScroll = _scrollController.position.maxScrollExtent;
            final double clampedOffset = targetOffset.clamp(0.0, maxScroll);

            _scrollController.animateTo(
              clampedOffset,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
            );
          }
          return;
        }
      } catch (e) {
        // Fall through to the fallback calculation
      }
    }

    // Fallback: Use estimated item height if we can't get the actual position
    const double estimatedItemHeight =
        44.0; // Estimated based on padding + text
    const double padding = 16.0;

    final double itemTop = _focusedItemIndex * estimatedItemHeight;
    final double itemBottom = itemTop + estimatedItemHeight;

    final double currentScrollOffset = _scrollController.offset;
    final double viewportHeight = _scrollController.position.viewportDimension;
    final double visibleTop = currentScrollOffset;
    final double visibleBottom = currentScrollOffset + viewportHeight;

    double? targetOffset;

    if (itemTop < visibleTop) {
      targetOffset = (itemTop - padding).clamp(0.0, double.infinity);
    } else if (itemBottom > visibleBottom) {
      targetOffset = itemBottom - viewportHeight + padding;
    }

    if (targetOffset != null) {
      final double maxScroll = _scrollController.position.maxScrollExtent;
      final double clampedOffset = targetOffset.clamp(0.0, maxScroll);

      _scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
      );
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;

    return OverlayEntry(
      builder: (context) => StatefulBuilder(
        builder: (context, overlaySetState) {
          return GestureDetector(
            onTap: () => _closeDropdown(returnFocus: true),
            behavior: HitTestBehavior.translucent,
            child: Stack(
              children: [
                Positioned(
                  width: size.width,
                  child: CompositedTransformFollower(
                    link: _layerLink,
                    showWhenUnlinked: false,
                    offset: Offset(0.0, size.height + 5.0),
                    child: GestureDetector(
                      onTap: () {},
                      child: Material(
                        elevation: 4.0,
                        borderRadius: BorderRadius.circular(8),
                        child: _buildDropdownList(overlaySetState),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDropdownList(StateSetter overlaySetState) {
    final filteredItems = _getFilteredItems();
    final hasSearchText =
        widget.enableSearch && _searchController.text.isNotEmpty;

    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.comboBoxBorder),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.enableSearch)
            Padding(
              padding: const EdgeInsets.all(8),
              child: RawKeyboardListener(
                focusNode: FocusNode(skipTraversal: true),
                onKey: (event) {
                  if (event is RawKeyDownEvent) {
                    if (event.logicalKey == LogicalKeyboardKey.tab) {
                      final isShiftPressed =
                          HardwareKeyboard.instance.isShiftPressed;
                      if (!isShiftPressed) {
                        _listFocusNode.requestFocus();
                        overlaySetState(() {
                          _isKeyboardNavigation = true;
                          // Start from selected item if exists, otherwise start from first item
                          if (filteredItems.isNotEmpty) {
                            final selectedIndex = filteredItems.indexWhere(
                              (item) => item.id == _valueNotifier.value,
                            );
                            _focusedItemIndex = selectedIndex != -1
                                ? selectedIndex
                                : 0;
                          } else {
                            _focusedItemIndex = -1;
                          }
                        });
                        if (_focusedItemIndex != -1) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _scrollToFocusedItem();
                          });
                        }
                      }
                    } else if (event.logicalKey == LogicalKeyboardKey.escape) {
                      _closeDropdown(returnFocus: true);
                    } else if (event.logicalKey ==
                        LogicalKeyboardKey.arrowDown) {
                      _listFocusNode.requestFocus();
                      overlaySetState(() {
                        _isKeyboardNavigation = true;
                        // Start from selected item if exists, otherwise start from first item
                        if (filteredItems.isNotEmpty) {
                          final selectedIndex = filteredItems.indexWhere(
                            (item) => item.id == _valueNotifier.value,
                          );
                          _focusedItemIndex = selectedIndex != -1
                              ? selectedIndex
                              : 0;
                        } else {
                          _focusedItemIndex = -1;
                        }
                      });
                      if (_focusedItemIndex != -1) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _scrollToFocusedItem();
                        });
                      }
                    }
                  }
                },
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    hintText: 'Search...',
                    hintStyle: AppFonts.regularTextStyle(
                      14,
                      color: Colors.grey[600]!,
                    ),
                    isDense: true,
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 8,
                    ),
                  ),
                  onChanged: (searchText) {
                    // if (searchText.isEmpty) {
                    //   widget.onSearchChanged?.call(null);
                    // } else {
                    widget.onSearchChanged?.call(searchText);
                    // }
                    if (!widget.useApiSearch) {
                      overlaySetState(() {
                        _isKeyboardNavigation = false;
                        _focusedItemIndex = -1;
                      });
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _scrollToSelectedItem();
                      });
                    } else {
                      if (searchText.isEmpty) {
                        widget.onSearchChanged?.call('');
                      }
                    }
                  },
                  onSubmitted: (value) {
                    _listFocusNode.requestFocus();
                    overlaySetState(() {
                      _isKeyboardNavigation = true;
                      // Start from selected item if exists, otherwise start from first item
                      if (filteredItems.isNotEmpty) {
                        final selectedIndex = filteredItems.indexWhere(
                          (item) => item.id == _valueNotifier.value,
                        );
                        _focusedItemIndex = selectedIndex != -1
                            ? selectedIndex
                            : 0;
                      } else {
                        _focusedItemIndex = -1;
                      }
                    });
                    if (_focusedItemIndex != -1) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _scrollToFocusedItem();
                      });
                    }
                  },
                ),
              ),
            ),
          Flexible(
            child: filteredItems.isEmpty
                ? Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      hasSearchText
                          ? 'No results found for "${_searchController.text}"'
                          : noDataFound,
                      style: AppFonts.regularTextStyle(
                        14,
                        color: Colors.grey[600]!,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                : Focus(
                    focusNode: _listFocusNode,
                    onKeyEvent: (node, event) {
                      if (event is KeyDownEvent || event is KeyRepeatEvent) {
                        if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
                          overlaySetState(() {
                            _isKeyboardNavigation = true;
                            if (_focusedItemIndex < filteredItems.length - 1) {
                              _focusedItemIndex++;
                            }
                          });
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _scrollToFocusedItem();
                          });
                          return KeyEventResult.handled;
                        } else if (event.logicalKey ==
                            LogicalKeyboardKey.arrowUp) {
                          overlaySetState(() {
                            _isKeyboardNavigation = true;
                            if (_focusedItemIndex > 0) {
                              _focusedItemIndex--;
                            } else if (widget.enableSearch) {
                              _searchFocusNode.requestFocus();
                              _focusedItemIndex = -1;
                              _isKeyboardNavigation = false;
                            }
                          });
                          if (_focusedItemIndex >= 0) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _scrollToFocusedItem();
                            });
                          }
                          return KeyEventResult.handled;
                        } else if (event.logicalKey ==
                            LogicalKeyboardKey.enter) {
                          if (event is KeyDownEvent) {
                            if (_focusedItemIndex >= 0 &&
                                _focusedItemIndex < filteredItems.length) {
                              final item = filteredItems[_focusedItemIndex];
                              final validatedValue = _getValidatedValue(
                                item.id,
                              );
                              _selectValue(validatedValue);
                              return KeyEventResult.handled;
                            }
                          }
                        } else if (event.logicalKey == LogicalKeyboardKey.tab) {
                          if (event is KeyDownEvent) {
                            final isShiftPressed =
                                HardwareKeyboard.instance.isShiftPressed;
                            if (isShiftPressed && widget.enableSearch) {
                              _searchFocusNode.requestFocus();
                              overlaySetState(() {
                                _isKeyboardNavigation = false;
                                _focusedItemIndex = -1;
                              });
                              return KeyEventResult.handled;
                            } else {
                              _closeDropdown(returnFocus: false);
                              return KeyEventResult.ignored;
                            }
                          }
                        } else if (event.logicalKey ==
                            LogicalKeyboardKey.escape) {
                          if (event is KeyDownEvent) {
                            _closeDropdown(returnFocus: true);
                            return KeyEventResult.handled;
                          }
                        }
                      }
                      return KeyEventResult.ignored;
                    },
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      child: ListView.builder(
                        controller: _scrollController,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          final isSelected = _valueNotifier.value == item.id;
                          final isFocused = _focusedItemIndex == index;

                          final bool isNoDataItem =
                              item.id == 'Select' && item.value == noDataFound;

                          // Create or reuse a key for this item
                          _itemKeys.putIfAbsent(index, () => GlobalKey());

                          if (isNoDataItem) {
                            return MouseRegion(
                              key: _itemKeys[index],
                              cursor: SystemMouseCursors.basic,
                              child: AbsorbPointer(
                                absorbing: true,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  child: Text(
                                    item.value,
                                    style: AppFonts.regularTextStyle(
                                      14,
                                      color: Colors.grey[600]!,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            );
                          }

                          return MouseRegion(
                            key: _itemKeys[index],
                            onHover: (_) {
                              // Reset keyboard navigation when mouse actively moves
                              if (_isKeyboardNavigation) {
                                overlaySetState(() {
                                  _isKeyboardNavigation = false;
                                });
                              }
                            },
                            child: InkWell(
                              onTap: () {
                                final validatedValue = _getValidatedValue(
                                  item.id,
                                );
                                _selectValue(validatedValue);
                              },
                              onHover: (hovering) {
                                // Update focus on hover only if not using keyboard
                                if (hovering && !_isKeyboardNavigation) {
                                  overlaySetState(() {
                                    _focusedItemIndex = index;
                                  });
                                }
                              },
                              child: Container(
                                // padding: const EdgeInsets.only(
                                //   left: 0,
                                //   right: 16,
                                //   top: 12,
                                //   bottom: 12,
                                // ),
                                // margin: const EdgeInsets.symmetric(
                                //   horizontal: 16,
                                // ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: isFocused
                                      ? AppTheme.primaryBlueColor.withOpacity(
                                          0.1,
                                        )
                                      : null,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  item.value,
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: isSelected
                                        ? AppTheme.primaryBlueColor
                                        : widget.itemTextColor,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  List<TableFilter> _getFilteredItems() {
    if (!widget.enableSearch ||
        widget.useApiSearch ||
        _searchController.text.isEmpty) {
      return widget.items;
    }

    return widget.items.where((item) {
      return item.value.toLowerCase().contains(
        _searchController.text.toLowerCase(),
      );
    }).toList();
  }

  KeyEventResult _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        if (!_isOpen) {
          _openDropdown();
          return KeyEventResult.handled;
        }
      } else if (event.logicalKey == LogicalKeyboardKey.tab) {
        if (_isOpen) {
          final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
          if (!isShiftPressed) {
            if (widget.enableSearch) {
              _searchFocusNode.requestFocus();
            } else {
              _listFocusNode.requestFocus();
              final filteredItems = _getFilteredItems();
              setState(() {
                // Start from selected item if exists, otherwise start from first item
                if (filteredItems.isNotEmpty) {
                  final selectedIndex = filteredItems.indexWhere(
                    (item) => item.id == _valueNotifier.value,
                  );
                  _focusedItemIndex = selectedIndex != -1 ? selectedIndex : 0;
                } else {
                  _focusedItemIndex = -1;
                }
              });
            }
            return KeyEventResult.handled;
          }
        }
      } else if (event.logicalKey == LogicalKeyboardKey.escape) {
        if (_isOpen) {
          _closeDropdown(returnFocus: true);
          return KeyEventResult.handled;
        }
      } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        if (!_isOpen) {
          _openDropdown();
          return KeyEventResult.handled;
        }
      }
    }
    return KeyEventResult.ignored;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _valueNotifier.dispose();
    _focusNode.dispose();
    _searchFocusNode.dispose();
    _listFocusNode.dispose();
    _scrollController.dispose();
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<String?>(
      valueListenable: _valueNotifier,
      builder: (context, currentValue, _) {
        return FormField<String>(
          key: _formFieldKey,
          initialValue: currentValue,
          validator: widget.validator,
          builder: (FormFieldState<String> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                CompositedTransformTarget(
                  link: _layerLink,
                  child: Focus(
                    focusNode: _focusNode,
                    onKeyEvent: (node, event) {
                      return _handleKeyEvent(event);
                    },
                    child: Container(
                      width: widget.width,
                      height: widget.height,
                      decoration: BoxDecoration(
                        color:
                            (widget.fillColor == null ||
                                widget.fillColor == Colors.transparent)
                            ? Colors.grey.shade50
                            : widget.fillColor,
                        border: widget.showBorder
                            ? Border.all(
                                color: field.hasError
                                    ? Colors.red
                                    : _focusNode.hasFocus
                                    ? AppTheme.primaryColor
                                    : AppTheme.comboBoxBorder,
                                width: _focusNode.hasFocus ? 2.0 : 1.0,
                              )
                            : null,
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                      ),
                      child: _buildDropdown(
                        field,
                        widget.isMainContainerPaddingNeeded,
                      ),
                    ),
                  ),
                ),
                if (field.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5, left: 12),
                    child: Text(
                      field.errorText!,
                      style: AppFonts.regularTextStyle(12, color: Colors.red),
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildDropdown(
    FormFieldState<String> field,
    bool isMainContainerPaddingNeeded,
  ) {
    TableFilter? selectedItem;
    if (_valueNotifier.value != null && _valueNotifier.value!.isNotEmpty) {
      try {
        selectedItem = widget.items.firstWhere(
          (item) => item.id == _valueNotifier.value,
        );
      } catch (_) {
        selectedItem = _lastSelectedItem ?? widget.selectedItem;
      }
    } else {
      selectedItem = widget.selectedItem;
    }
    final bool hasValidSelection =
        _valueNotifier.value != null &&
        _valueNotifier.value!.isNotEmpty &&
        _valueNotifier.value != 'Select';

    final displayText = hasValidSelection && selectedItem != null
        ? selectedItem.value
        : widget.hint;

    final isPlaceholder = !hasValidSelection;
    final Color effectiveTextColor = widget.onChanged == null
        ? Colors.grey[400]!
        : (!isPlaceholder
              ? (widget.itemTextColor ?? AppTheme.black)
              : (widget.hintTextColor ?? Colors.grey[600]!));
    return InkWell(
      onTap: widget.onChanged != null
          ? () {
              _focusNode.requestFocus();
              _toggleDropdown();
            }
          : null,
      canRequestFocus: false,
      borderRadius: BorderRadius.circular(widget.borderRadius),
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Container(
        padding: (isMainContainerPaddingNeeded)
            ? const EdgeInsets.symmetric(horizontal: 16)
            : const EdgeInsets.only(
                left: 4,
                top: 4,
                bottom: 4,
                right: defaultPadding,
              ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Text(
                      displayText,
                      style: AppFonts.regularTextStyle(
                        14,
                        color: effectiveTextColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // Show info icon inside dropdown when no value is selected
                  if (widget.showInfoIcon &&
                      widget.infoIconTooltip != null &&
                      !hasValidSelection) ...[
                    const SizedBox(width: 6),
                    MouseRegion(
                      cursor: SystemMouseCursors.basic,
                      child: Tooltip(
                        richMessage: WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: Container(
                            constraints: const BoxConstraints(maxWidth: 250),
                            child: Text(
                              widget.infoIconTooltip!,
                              softWrap: true,
                              maxLines: 4,
                              style: AppFonts.regularTextStyle(
                                13,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        preferBelow: false,
                        verticalOffset: 10,
                        child: Icon(
                          Icons.info_outline,
                          size: 16,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (widget.onChanged != null)
              Icon(
                _isOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: Colors.grey,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
