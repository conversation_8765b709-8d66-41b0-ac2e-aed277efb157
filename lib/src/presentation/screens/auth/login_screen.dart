import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/core/utils/app_snack_bar.dart';

import '../../../core/navigation/web_router.dart';
import '../../../core/services/firebase_auth_service.dart';
import '../../../data/repository/auth_data_repository.dart';
import '../agent/agent_registration_screen.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import 'components/auth_background.dart';
import 'components/social_login_button.dart';
import 'components/custom_checkbox.dart';
import 'components/primary_button.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final emailFocusNode = FocusNode();
    final passwordFocusNode = FocusNode();
    final obscurePassword = useState(true);
    final rememberMeState = useState(false);
    final emailError = useState<String?>(null);
    final passwordError = useState<String?>(null);
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isLoading = useState(false);

    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: AuthBackground(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: Responsive.isMobile(context) ? double.infinity : 1000,
              minHeight: Responsive.isMobile(context)
                  ? size.height * 0.8
                  : size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Card(
                color: Colors.transparent,
                elevation: Responsive.isMobile(context) ? 0 : 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: Responsive.isMobile(context) ? 0 : 40,
                  vertical: Responsive.isMobile(context) ? 0 : 20,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                  child: IntrinsicHeight(
                    child: Responsive.isMobile(context)
                        ? LoginRightPanel(
                            isMobile: true,
                            emailController: emailController,
                            passwordController: passwordController,
                            obscurePassword: obscurePassword,
                            rememberMeState: rememberMeState,
                            emailError: emailError,
                            passwordError: passwordError,
                            formKey: formKey,
                            emailFocusNode: emailFocusNode,
                            passwordFocusNode: passwordFocusNode,
                          )
                        : Row(
                            children: [
                              if (!Responsive.isSmallMobile(context))
                                Expanded(
                                  flex: Responsive.isTablet(context) ? 4 : 5,
                                  child: const LoginLeftPanel(),
                                ),
                              Expanded(
                                flex: Responsive.isTablet(context) ? 6 : 5,
                                child: ValueListenableBuilder(
                                  valueListenable: isLoading,
                                  builder: (context, _isLoading, child) {
                                    return LoginRightPanel(
                                      isMobile: false,
                                      emailController: emailController,
                                      passwordController: passwordController,
                                      obscurePassword: obscurePassword,
                                      rememberMeState: rememberMeState,
                                      emailError: emailError,
                                      passwordError: passwordError,
                                      formKey: formKey,
                                      emailFocusNode: emailFocusNode,
                                      passwordFocusNode: passwordFocusNode,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class LoginLeftPanel extends StatelessWidget {
  const LoginLeftPanel({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDesktop = Responsive.isDesktop(context);
    return Container(
      padding: const EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: AppTheme.loginBgColor.withValues(alpha: 0.65),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Title with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$iconAssetpath/login_title_icon.png',
                height: isDesktop ? 56 : 46,
                width: isDesktop ? 56 : 46,
              ),
              const SizedBox(width: 12),
              Text(
                appName,
                textAlign: TextAlign.center,
                style: AppFonts.boldTextStyle(
                  isDesktop ? 45 : 35,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: AppFonts.regularTextStyle(
                20,
                color: Colors.white,
              ).copyWith(height: 1.5),
              children: [
                TextSpan(text: appDescriptionP1),
                TextSpan(
                  text: appDescriptionP2,
                  style: AppFonts.boldTextStyle(20, color: Colors.white),
                ),
                TextSpan(text: appDescriptionP3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LoginRightPanel extends StatelessWidget {
  final bool isMobile;
  final FocusNode emailFocusNode;
  final FocusNode passwordFocusNode;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final ValueNotifier<bool> obscurePassword;
  final ValueNotifier<bool> rememberMeState;
  final ValueNotifier<String?> emailError;
  final ValueNotifier<String?> passwordError;
  final GlobalKey<FormState> formKey;

  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  LoginRightPanel({
    super.key,
    required this.isMobile,
    required this.emailController,
    required this.passwordController,
    required this.obscurePassword,
    required this.rememberMeState,
    required this.emailError,
    required this.passwordError,
    required this.formKey,
    required this.emailFocusNode,
    required this.passwordFocusNode,
  });

  bool _isFormValid() {
    final email = emailController.text.trim();
    final password = passwordController.text.trim();
    return InputValidators.validateEmail(email) == null &&
        InputValidators.validatePassword(password) == null;
  }

  Future<void> _handleLogin(BuildContext context) async {
    if (!formKey.currentState!.validate()) return;

    final payload = {
      "username": emailController.text.trim(),
      "password": passwordController.text.trim(),
      "rememberMe": rememberMeState.value,
    };

    final loginCubit = context.read<AuthCubit>();
    await loginCubit.login(payload);

    final state = loginCubit.state;

    if (state is AuthSuccess) {
      await _fetchUserInfo(context);
    } else if (state is AuthError) {
      if (context.mounted) {
        await AppSnackBar.showSnackBar(
          context,
          state.error,
          SnackBarType.error,
        );
      }
    }
  }

  Future<void> _handleGoogleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();

    // Ensure clean state before sign-in attempt
    await _firebaseAuthService.signOut();

    final signinResult = await _firebaseAuthService.signInWithGoogle();

    // Check if user canceled the Google Sign-In
    if (!signinResult.success) {
      // If the user cancelled the popup, just return silently without showing an error
      final isCancelled =
          signinResult.error?.toLowerCase().contains('cancelled') == true ||
          signinResult.error?.toLowerCase().contains('canceled') == true ||
          signinResult.error?.toLowerCase().contains('popup_closed') == true ||
          signinResult.idToken == null;

      if (isCancelled) {
        debugPrint('Google sign-in cancelled by user.');
        return;
      }

      // Otherwise, show error only if it's an actual failure
      if (context.mounted) {
        await AppSnackBar.showSnackBar(
          context,
          signinResult.error ?? 'Google sign-in failed',
          SnackBarType.error,
        );
      } else {
        debugPrint('Login Screen: Context not mounted, cannot show snackbar');
      }
      return;
    }

    // Proceed only when Firebase Auth succeeded
    await authCubit.signInWithGoogle(signinResult.idToken);
  }

  Future<void> _handleAppleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();

    // Ensure clean state before sign-in attempt
    await _firebaseAuthService.signOut();

    final signinResult = await _firebaseAuthService.signInWithApple(
      context: context,
    );

    // Check if Firebase Auth was successful before proceeding
    if (!signinResult.success) {
      // Show error immediately if Firebase Auth failed
      if (context.mounted && signinResult.showWarning) {
        await AppSnackBar.showSnackBar(
          context,
          signinResult.error ?? 'Apple sign-in failed',
          SnackBarType.error,
        );
      } else {
        debugPrint(
          'Login Screen: Context not mounted, cannot show Apple snackbar',
        );
      }
      return;
    }

    // Only proceed to backend authentication if Firebase Auth succeeded
    await authCubit.signInWithApple(signinResult.idToken);
  }

  _fetchUserInfo(BuildContext context) async {
    final userCubit = context.read<UserCubit>();

    await userCubit.getUserProfile();
    final state = userCubit.state;
    if (state is UserLoaded) {
      // Navigation will be handled by AuthWrapper
      debugPrint('User loaded successfully');
    } else if (state is UserError) {
      debugPrint('Error loading user: ${state.message}');
      if (context.mounted) {
        AppSnackBar.showSnackBar(
          context,
          '${state.message} while fetching user info',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMobileView = Responsive.isMobile(context);

    return Container(
      padding: EdgeInsets.all(
        isMobileView ? defaultPadding : defaultPadding * 2,
      ),
      margin: EdgeInsets.all(isMobileView ? defaultPadding * 2 : 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isMobileView ? 5 : 0),
      ),
      child: Form(
        key: formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (isMobileView) _buildHeader(),
            Text(
              loginTitle,
              style: AppFonts.semiBoldTextStyle(
                24,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 2),
            _buildSocialButtons(context),
            const SizedBox(height: defaultPadding * 1.8),
            Text(
              orContinueWithEmail,
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.orContinueWithColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 1.8),
            _buildEmailField(isMobileView),
            const SizedBox(height: defaultPadding),
            _buildPasswordField(isMobileView),
            const SizedBox(height: 8),
            _buildAuthOptionsRow(context),
            const SizedBox(height: defaultPadding * 1.5),
            _buildLoginBtn(context),
            const SizedBox(height: defaultPadding),
            _buildSignupView(context),
            const SizedBox(height: defaultPadding),
          ],
        ),
      ),
    );
  }

  /// 🔹 App header for mobile view
  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          appName,
          style: AppFonts.boldTextStyle(28, color: AppTheme.roundIconColor),
        ),
        const SizedBox(height: defaultPadding * 2),
      ],
    );
  }

  /// 🔹 Social login buttons row
  Widget _buildSocialButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child:
              _buildSocialButton<
                AuthSuccess,
                AuthGoogleError,
                AuthGoogleLoading
              >(
                context,
                icon: '$iconAssetpath/google.png',
                text: signInWithGmail,
                onSuccess: (ctx, state) async {
                  // await _fetchUserInfo(ctx);
                },
                onError: (ctx, state) async {
                  await _handleNewUserSocialSignin(
                    ctx,
                    state.error,
                    state.idToken,
                    SigninType.google,
                    userNotFound: state.userNotFound,
                  );
                },
                onPressed: () => _handleGoogleSignIn(context),
              ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child:
              _buildSocialButton<
                AuthAppleSuccess,
                AuthAppleError,
                AuthAppleLoading
              >(
                context,
                icon: '$iconAssetpath/apple.png',
                text: signInWithApple,
                onSuccess: (ctx, state) async => await _fetchUserInfo(ctx),
                onError: (ctx, state) async {
                  await _handleNewUserSocialSignin(
                    ctx,
                    state.error,
                    state.idToken,
                    SigninType.apple,
                    userNotFound: state.userNotFound,
                  );
                },
                onPressed: () => _handleAppleSignIn(context),
              ),
        ),
      ],
    );
  }

  // gmail/apple signin for new user
  _handleNewUserSocialSignin(
    BuildContext context,
    String error,
    String? idToken,
    SigninType signinType, {
    bool userNotFound = false,
  }) async {
    await AppSnackBar.showSnackBar(context, error, SnackBarType.error);
    if (userNotFound) {
      context.go(
        AppRoutes.signupAgent.path,
        extra: {'idToken': idToken, 'signinType': signinType},
      );
    }
  }

  /// 🔹 Generic Social Login Button with BlocConsumer
  Widget _buildSocialButton<
    TSuccess extends AuthState,
    TError extends AuthState,
    TLoading extends AuthState
  >(
    BuildContext context, {
    required String icon,
    required String text,
    required Future<void> Function(BuildContext, TSuccess) onSuccess,
    required Future<void> Function(BuildContext, TError) onError,
    required VoidCallback onPressed,
  }) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (ctx, state) async {
        if (state is TSuccess) {
          // await onSuccess(ctx, state);
          await _fetchUserInfo(ctx);
        } else if (state is TError) {
          await onError(ctx, state);
        }
      },
      builder: (ctx, state) {
        final isLoading = state is TLoading;
        return SocialLoginButton(
          icon: Image.asset(icon, height: 20, width: 20),
          text: isLoading ? 'Signing in...' : text,
          onPressed: isLoading ? null : onPressed,
        );
      },
    );
  }

  ValueListenableBuilder<String?> _buildEmailField(bool isMobileView) {
    return ValueListenableBuilder<String?>(
      valueListenable: emailError,
      builder: (_, errorText, __) {
        return Builder(
          builder: (context) {
            return AppTextField(
              controller: emailController,
              hintText: emailHint,
              errorText: errorText,
              validator: InputValidators.validateEmail,
              focusNode: emailFocusNode,
              isMobile: isMobileView,
              onFieldSubmitted: (_) {
                if (passwordController.text.isNotEmpty) {
                  _handleLogin(context);
                } else {
                  passwordFocusNode.requestFocus();
                }
              },
            );
          },
        );
      },
    );
  }

  ValueListenableBuilder<String?> _buildPasswordField(bool isMobileView) {
    return ValueListenableBuilder<String?>(
      valueListenable: passwordError,
      builder: (_, errorText, __) {
        return ValueListenableBuilder<bool>(
          valueListenable: obscurePassword,
          builder: (_, isObscured, __) {
            return Builder(
              builder: (context) => AppTextField(
                controller: passwordController,
                hintText: passwordHint,
                errorText: errorText,
                validator: InputValidators.validatePassword,
                isObscure: isObscured,
                showToggle: true,
                onToggleObscure: () =>
                    obscurePassword.value = !obscurePassword.value,
                focusNode: passwordFocusNode,
                isMobile: isMobileView,
                onFieldSubmitted: (_) => _handleLogin(context),
              ),
            );
          },
        );
      },
    );
  }

  ValueListenableBuilder<bool> _buildAuthOptionsRow(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: rememberMeState,
      builder: (_, isChecked, __) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomCheckbox(
              value: isChecked,
              onChanged: (v) => rememberMeState.value = v ?? false,
              text: rememberMe,
            ),
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  context.go(AppRoutes.emailVerification.path);
                },
                child: Text(
                  forgotPassword,
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: AppTheme.roundIconColor,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  BlocConsumer<AuthCubit, AuthState> _buildLoginBtn(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (_, state) async {},
      builder: (_, state) {
        return AnimatedBuilder(
          animation: Listenable.merge([emailController, passwordController]),
          builder: (_, __) {
            final isLoading = state is AuthLoading;
            return PrimaryButton(
              text: isLoading ? loggingIn : loginButton,
              height: 45,
              borderRadius: 25,
              onPressed: (isLoading || !_isFormValid())
                  ? null
                  : () => _handleLogin(context),
            );
          },
        );
      },
    );
  }

  Widget _buildSignupView(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 300;
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: dontHaveAccount,
            style: AppFonts.regularTextStyle(14, color: Colors.black54),
          ),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: InkWell(
              onTap: () {
                // Navigate to sign up screen
                context.go(AppRoutes.signupAgent.path);
              },
              child: Text(
                isSmallScreen ? "\n$signUp" : signUp,
                style: AppFonts.mediumTextStyle(
                  14,
                  color: AppTheme.roundIconColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
