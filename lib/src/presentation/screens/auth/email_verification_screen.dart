import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import '../../../core/config/app_strings.dart' as app_strings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/success_snack_bar.dart';
import '../../../core/utils/validators.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/elevated_button.dart';

class EmailVerificationScreen extends HookWidget {
  EmailVerificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final emailController = useTextEditingController();
    final emailFieldKey = useState(UniqueKey());

    final isMobile = Responsive.isMobile(context);

    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.all(defaultPadding),
        width: isMobile ? double.infinity : 550,
        decoration: BoxDecoration(
          color: AppTheme.roundIconColor,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: BlocConsumer<AuthCubit, AuthState>(
          listener: (context, state) {
            // TODO: implement listener
          },
          builder: (context, state) {
            return Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(25),

                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          vertical: defaultPadding * 2.5,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.roundIconColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            app_strings.verifyEmail,
                            style: AppFonts.semiBoldTextStyle(
                              24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),

                      // Form
                      Container(
                        decoration: BoxDecoration(
                          color: AppTheme.white,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        padding: const EdgeInsets.all(defaultPadding * 2.5),
                        child: Form(
                          key: formKey,
                          child: Column(
                            children: [
                              // text to show: Please enter your email address. A verification link will be sent to your registered inbox.
                              Text(
                                app_strings.pleaseEnterYourEmailAddress,
                                textAlign: TextAlign.center,
                                style: AppFonts.regularTextStyle(
                                  14,
                                  color: AppTheme.primaryTextColor,
                                ),
                              ),
                              const SizedBox(height: defaultPadding * 2),
                              // Username (email)
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  app_strings.emailAddressHead,
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: AppTheme.primaryTextColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: defaultPadding / 2),
                              AppTextField(
                                key: emailFieldKey.value,
                                controller: emailController,
                                hintText: app_strings.enterEmailAddress,
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) =>
                                    InputValidators.validateEmail(value),
                                isMobile: isMobile,
                              ),

                              const SizedBox(height: defaultPadding * 2),

                              // Buttons
                              _buildBtnView(
                                formKey,
                                emailController,
                                context,
                                emailFieldKey,
                              ),
                              const SizedBox(height: defaultPadding * 2),
                              _buildLoginCallbackView(context),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                state is EmailVerificationLoading
                    ? Positioned.fill(
                        child: Container(
                          color: Colors.transparent,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      )
                    : const SizedBox(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildBtnView(
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    BuildContext context,
    ValueNotifier<UniqueKey> emailFieldKey,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppButton(
          label: app_strings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () {
            emailController.clear();
            formKey.currentState?.reset();
            emailController.text = '';
            emailFieldKey.value = UniqueKey();
          },
        ),
        const SizedBox(width: defaultPadding),
        AppButton(
          label: app_strings.sendInviteLink,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              await _handleEmailVerification(context, emailController);
            }
          },
        ),
      ],
    );
  }

  Widget _buildLoginCallbackView(BuildContext context) {
    return InkWell(
      onTap: () {
        context.go(AppRoutes.login.path);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.arrow_back,
            color:
                AppTheme.secondaryTextColor, // purple arrow like in your image
            size: 20,
          ),
          const SizedBox(width: 6),
          Text(
            app_strings.backToLogin,
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.roundIconColor,
            ),
          ),
        ],
      ),
    );
  }

  _handleEmailVerification(
    BuildContext context,
    TextEditingController emailController,
  ) async {
    final authCubit = context.read<AuthCubit>();

    await authCubit.verifyEmail(emailController.text.trim());

    final state = authCubit.state;
    if (state is EmailVerificationSuccess) {
      await SuccessSnackBar.showSnackBar(
        context,
        state.message,
        SnackBarType.success,
      );
    } else if (state is EmailVerificationFailure) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }
}
