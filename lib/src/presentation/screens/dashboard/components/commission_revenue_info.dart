import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/column_mapping_utils.dart';
import '../../../../domain/models/commission_revenue_info_model.dart';
import '../../../cubit/commission_revenue_info/commission_revenue_cubit.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/format_currency_dollar.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class CommissionRevenuewInfoScreen extends HookWidget {
  final String? userId;
  final String? userName;
  const CommissionRevenuewInfoScreen({super.key, this.userId, this.userName});

  @override
  Widget build(BuildContext context) {
    final sortColumn = useState<String>('created_at');
    final sortOrder = useState<String>('ASC');
    // Fetch commission data when widget loads
    useEffect(() {
      if (userId != null && userId!.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context
              .read<CommissionRevenueCubit>()
              .fetchCommissionRevenueDetailsByUser(userId!);
        });
      } else {
        debugPrint(
          'CommissionRevenuewInfoScreen - userId is null or empty: $userId',
        );
      }
      return null;
    }, [userId]);
    return BlocBuilder<CommissionRevenueCubit, CommissionRevenueState>(
      builder: (context, state) {
        if (state is CommissionRevenueLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is CommissionRevenueError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error: ${state.message}',
                  style: AppFonts.regularTextStyle(14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (userId != null) {
                      context
                          .read<CommissionRevenueCubit>()
                          .fetchCommissionRevenueDetailsByUser(userId!);
                    }
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        void handleSort(String columnName, bool ascending) {
          // Check if columnName is empty (indicates reset to default sort)
          if (columnName.isEmpty) {
            sortColumn.value = 'created_at';
            sortOrder.value = 'ASC';

            if (context.mounted) {}
            return;
          }
          String backendColumnName = _getBackendColumnName(columnName);
          sortColumn.value = columnName;
          sortOrder.value = ascending ? 'ASC' : 'DESC';
          if (columnName == agentDirectRecruits) {
            return;
          }
        }

        final List<CommissionRevenueInfoModel> data =
            state is CommissionRevenueLoaded ? state.items : [];
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(top: 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: CustomDataTableWidget<CommissionRevenueInfoModel>(
                    data: data,
                    title: getTitle(context),
                    titleIcon: "$iconAssetpath/user.png",

                    //if no need to show the search and filter buttons, so need to pass false for the belw params
                    //for filters, if we not set specified filter columns then the filter button not visible, it should visible
                    //showFilterButton = true and spaecify the filter columns.
                    showFilterButton: false,
                    showSearchButton: false,
                    columnNames: [
                      columnCommissionRevenueInfoTransactionId,
                      columnCommissionRevenueInfoGrossCommission,
                      columnCommissionRevenueInfoPersonalCommision,
                      columnCommissionRevenueInfoRevenueShare,
                      columnCommissionRevenueInfoCommissionPercentage,
                    ],
                    showSortIconColumns: [],
                    cellBuilders: [
                      (t) => t.transactionId,
                      (t) =>
                          (t.grossCommission == 0.0 ||
                              t.grossCommission.abs() < 0.01)
                          ? '-'
                          : formatCurrencyDollar(t.grossCommission),
                      (t) =>
                          (t.personalCommission == 0.0 ||
                              t.personalCommission.abs() < 0.01)
                          ? '-'
                          : formatCurrencyDollar(t.personalCommission),
                      (t) =>
                          (t.revenueShare == 0.0 || t.revenueShare.abs() < 0.01)
                          ? '-'
                          : formatCurrencyDollar(t.revenueShare),
                      (t) =>
                          (t.commissionPercentage == 0.0 ||
                              t.commissionPercentage.abs() < 0.01)
                          ? '-'
                          : '${t.commissionPercentage}%',
                    ],
                    iconCellBuilders: [null, null, null, null, null],
                    useIconBuilders: [true, false, false, false, false],
                    useWidgetBuilders: [false, false, false, false, false],
                    widgetCellBuilders: [null, null, null, null, null],
                    actionBuilders: [],
                    onSort: handleSort,
                    mobileCardBuilder: (context, t) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.shade200,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '$columnCommissionRevenueInfoTransactionId : ${t.transactionId}',
                              style: AppFonts.boldTextStyle(
                                15,
                              ).copyWith(color: Colors.black87),
                            ),
                            const SizedBox(height: 12),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoGrossCommission :',
                              formatCurrencyDollar(t.grossCommission),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoPersonalCommision :',
                              formatCurrencyDollar(t.personalCommission),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoRevenueShare :',
                              formatCurrencyDollar(t.revenueShare),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoCommissionPercentage :',
                              '${t.commissionPercentage}%',
                            ),
                          ],
                        ),
                      );
                    },

                    pageCount: 1,
                    isLoading: state is CommissionRevenueLoading,
                    totalElements: data.length,
                    currentPageIndex: 0,
                    handleTableSearch: (value) {},
                    showPagination: false,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String getTitle(BuildContext context) {
    bool isMobile = Responsive.isMobile(context);
    String title = isMobile
        ? ((userName?.isNotEmpty ?? false)
              ? '$commissionRevenueInfoTitle of \n$userName'
              : commissionRevenueInfoTitle)
        : ((userName?.isNotEmpty ?? false)
              ? '$commissionRevenueInfoTitle of $userName'
              : commissionRevenueInfoTitle);
    return title;
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            label,
            style: AppFonts.regularTextStyle(
              14,
            ).copyWith(color: Colors.grey.shade700),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            value,
            style: AppFonts.regularTextStyle(
              14,
            ).copyWith(color: Colors.black87),
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }

  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getAgentBackendColumnName(displayColumnName);
  }
}
