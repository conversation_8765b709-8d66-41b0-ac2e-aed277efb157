import '../../core/enum/user_role.dart';
import '../../core/utils/date_formatter.dart';

class User {
  String firstName;
  String lastName;
  String userId;
  String roleId;
  String name;
  String roleName;
  UserRole role;
  String logo;
  String avatarUrl;
  String? email;
  String? phone;
  String? referralCode;
  DateTime? joiningDate;
  String? city;
  String? state;
  String? referredBy;
  String? level;

  User({
    required this.firstName,
    required this.lastName,
    required this.userId,
    required this.roleId,
    required this.name,
    required this.role,
    required this.roleName,
    required this.logo,
    required this.avatarUrl,
    required this.email,
    required this.phone,
    required this.referralCode,
    required this.joiningDate,
    required this.city,
    required this.state,
    required this.referredBy,
    required this.level,
  });

  User copyWith({
    String? firstName,
    String? lastName,
    String? name,
    UserRole? role,
    String? logo,
    String? avatarUrl,
    String? roleName,
    String? id,
    String? userRoleId,
    String? email,
    String? phone,
    String? referralCode,
    DateTime? joiningDate,
    String? city,
    String? state,
    String? referredBy,
    String? level,
  }) => User(
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    name: name ?? this.name,
    role: role ?? this.role,
    logo: logo ?? this.logo,
    avatarUrl: avatarUrl ?? this.avatarUrl,
    roleName: roleName ?? this.roleName,
    userId: id ?? this.userId,
    roleId: userRoleId ?? this.roleId,
    email: email ?? this.email,
    phone: phone ?? this.phone,
    referralCode: referralCode ?? this.referralCode,
    joiningDate: joiningDate ?? this.joiningDate,
    city: city ?? this.city,
    state: state ?? this.state,
    referredBy: referredBy ?? this.referredBy,
    level: level ?? this.level,
  );

  factory User.fromJson(Map<String, dynamic> json) => User(
    userId: json["id"] ?? "",
    firstName: json["firstName"],
    lastName: json["lastName"],
    name: json["name"],
    roleName: json["roleDisplayName"],
    role: stringToUserRole(json["userRoleId"] ?? ''),
    roleId: json["userRoleId"] ?? "",
    logo: json["logo"] ?? '',
    avatarUrl: json["avatarUrl"] ?? '',
    email: json["email"],
    phone: json["phone"],
    referralCode: json["referralCode"],
    joiningDate: json["joiningDate"] == null
        ? null
        : AppDateFormatter.formatMonthYearJson(json["joiningDate"]),
    city: json["city"],
    state: json["state"],
    referredBy: json["referredBy"],
    level: json["level"],
  );

  Map<String, dynamic> toJson() => {
    "firstName": firstName,
    "lastName": lastName,
    "name": name,
    "userRoleId": userRoleToString(role),
    "logo": logo,
    "avatarUrl": avatarUrl,
    "roleDisplayName": roleName,
    "id": userId,
    "email": email,
    "phone": phone,
    "referralCode": referralCode,
    "joiningDate": joiningDate?.toIso8601String(),
    "city": city,
    "state": state,
    "referredBy": referredBy,
    "level": level,
  };
}
