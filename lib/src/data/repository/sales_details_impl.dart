import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:neorevv/src/domain/models/lead_source_model.dart';
import 'package:neorevv/src/domain/models/representing_type_model.dart';
import 'package:neorevv/src/domain/models/transaction_type_model.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/sales.dart';
import '../../domain/models/sales_details.dart';
import '../../domain/repository/sales_details_repository.dart';

class SalesDetailsRepositoryImpl extends SalesDetailsRepository {
  SalesDetailsRepositoryImpl();

  static String baseUrl = APIConfig.baseUrl;
  static const String salesDetailsUrl = APIConfig.salesDetails;
  static const String fetchClosingDocumentDetailsUrl =
      APIConfig.closingSalesDocument;
  static const String commissionProcessingUrl =
      APIConfig.closingSalesDocumentCommission;
  static const fileUpload = APIConfig.uploadFile;
  static const transactionTypes = APIConfig.transactionTypes;
  static const representingTypes = APIConfig.representingTypes;
  static const leadSourceTypes = APIConfig.leadSourceTypes;
  @override
  Future<SalesDetailsApi?> getSalesDetails(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(salesDetailsUrl, data: payload);

      if (response.statusCode == 200) {
        final Map<String, dynamic> dataList = response.data ?? {};
        final salesList = SalesDetailsApi.fromJson(dataList);
        return salesList;
        // return Sales.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchSalesDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<SalesClosingDocDetailsApi?> getSalesClosingDocumentDetails(
    String salesID,
    String requestingUserId,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        '$fetchClosingDocumentDetailsUrl/$salesID/closing-document',
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> dataList = response.data ?? {};
        final salesDetails = SalesClosingDocDetailsApi.fromJson(dataList);
        return salesDetails;
        // return Sales.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchSalesDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<dynamic> uploadSalesClosingDocumentFile(
    Map<String, dynamic> requestBody, {
    CancelToken? cancelToken,
  }) async {
    try {
      debugPrint('Starting upload with cancel token: $cancelToken');
      final dio = await DioClient.getDioForMultipart();

      // Create FormData for multipart upload
      FormData formData = FormData();
      // Extract progress callback if provided
      Function(int, int)? onProgress = requestBody['onProgress'];

      // Add non-file fields
      requestBody.forEach((key, value) {
        if (key != 'file') {
          formData.fields.add(MapEntry(key, value.toString()));
        }
      });

      // Add file if present
      if (requestBody['file'] != null) {
        final file = requestBody['file'];
        if (file is MultipartFile) {
          debugPrint('file is MultipartFile');
          // Already multipart, just add
          formData.files.add(MapEntry('file', file));
        } else if (file is PlatformFile) {
          try {
            MultipartFile multipartFile = await _createMultipartFile(file);

            formData.files.add(MapEntry('file', multipartFile));
          } catch (e) {
            throw ApiException(
              message:
                  'Failed to process file: ${file.name} with error: ${e.toString()}',
              statusCode: 400,
            );
          }
        } else {
          throw ApiException(
            message: 'Invalid file type provided',
            statusCode: 400,
          );
        }
      } else {
        throw ApiException(
          message: 'No file provided for upload',
          statusCode: 400,
        );
      }

      final response = await dio.post(
        APIConfig.uploadFile,
        data: formData,
        options: Options(contentType: 'multipart/form-data'),
        cancelToken: cancelToken,
        onSendProgress:
            onProgress ??
            (int sent, int total) {
              // Default progress handler if none provided
              debugPrint(
                'Upload progress: ${((sent / total) * 100).toStringAsFixed(2)}%',
              );
            },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload agent file',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// Handles both web (bytes) and mobile/desktop (file path) platforms
  Future<MultipartFile> _createMultipartFile(PlatformFile file) async {
    if (kIsWeb) {
      // For web platform, use bytes
      if (file.bytes != null) {
        return MultipartFile.fromBytes(file.bytes!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File bytes not available for web upload: ${file.name}',
          statusCode: 400,
        );
      }
    } else {
      // For mobile/desktop platforms, use file path
      if (file.path != null) {
        return await MultipartFile.fromFile(file.path!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File path not available for mobile upload: ${file.name}',
          statusCode: 400,
        );
      }
    }
  }

  @override
  Future<dynamic> editSalesClosingDocument(
    Map<String, dynamic> requestBody,
    String salesID,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.put(
        '$fetchClosingDocumentDetailsUrl/$salesID/closing-document',
        data: requestBody,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to edit sales closing document',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<dynamic> updateSalesClosingDocUploadStatus(
    String salesId,
    Map<String, dynamic> requestBody,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.patch(
        '$fetchClosingDocumentDetailsUrl/$salesId/status',
        queryParameters: requestBody,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to update sales closing document upload status',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<dynamic> updateSalesClosingDocCommissionUpdate(String salesId) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post('$commissionProcessingUrl/$salesId');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to update sales closing document commission',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<TransactionType>> getTransactionTypes() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(transactionTypes);

      if (response.statusCode == 200) {
        final List<dynamic> dataList = response.data ?? [];
        return dataList.map((e) => TransactionType.fromJson(e)).toList();
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchTransactionTypes,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<LeadSource>> getLeadSourceTypes() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(leadSourceTypes);

      if (response.statusCode == 200) {
        final List<dynamic> dataList = response.data ?? [];
        return dataList.map((e) => LeadSource.fromJson(e)).toList();
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchTransactionTypes,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<List<RepresentingTypes>> getRepresentingTypes() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(representingTypes);

      if (response.statusCode == 200) {
        final List<dynamic> dataList = response.data ?? [];
        return dataList.map((e) => RepresentingTypes.fromJson(e)).toList();
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        failedToFetchTransactionTypes,
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> handleSaleDocStatusUpdate(String saleId) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.patch(
        '${APIConfig.salesUploadStatus}$saleId/upload-status',
        queryParameters: {"isUploaded": true},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey("status") &&
            data["status"] == "Success") {
          return true;
        }
        return false;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload sales upload status',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
