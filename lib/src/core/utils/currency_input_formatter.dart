import 'package:flutter/services.dart';
import '/src/core/utils/regex.dart';

class CurrencyInputFormatter extends TextInputFormatter {
  final bool allowDecimal;

  CurrencyInputFormatter({this.allowDecimal = true});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String text = newValue.text;
    int selectionIndex = newValue.selection.end;

    // Allow empty input
    if (text.isEmpty) return newValue;

    // Auto-fix ".5" → "0.5"
    if (text.startsWith('.') && allowDecimal) {
      text = '0$text';
      selectionIndex++; // shift cursor because we inserted "0"
    }
    if (text.length > 1 && text.startsWith('0') && !text.startsWith('0.')) {
      final stripped = text.replaceFirst(RegExp(r'^0+'), '');
      final newText = stripped.isEmpty ? '0' : stripped;
      final removed = text.length - newText.length;
      selectionIndex = (selectionIndex - removed).clamp(0, newText.length);
      text = newText;
    }
    // Regex rules
    final regex = allowDecimal
        ? RegExUtils
              .currencyRegexMax2Decimals // max 2 decimals
        : RegExUtils.currencyRegexNoDecimals; // only integers

    if (!regex.hasMatch(text)) {
      return oldValue;
    }

    return newValue.copyWith(
      text: text,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}
