import 'package:flutter/material.dart';

import '../enum/user_role.dart';
import 'app_strings.dart' as AppStrings;

// Tab configuration model
class TabConfig {
  final String title;
  final Widget content;
  final IconData icon;
  final bool hidden;
  final bool hideBreadcrumb;

  const TabConfig({
    required this.title,
    required this.content,
    required this.icon,
    this.hidden = false,
    this.hideBreadcrumb = false,
  });
}

// lsit of tabs for each role
List<String> tabList(UserRole? role) {
  switch (role) {
    case UserRole.platformOwner:
      return [
        AppStrings.dashboardTab,
        AppStrings.brokersTab,
        AppStrings.agentsTab,
        AppStrings.salesTab,
        // AppStrings.reportsTab,
        AppStrings.agentNetwork,
        AppStrings.salesDoc,
        AppStrings.commissionRevenueInfoTitle,
        AppStrings.userProfile,
      ];
    case UserRole.admin:
      return [
        AppStrings.dashboardTab,
        AppStrings.brokersTab,
        AppStrings.agentsTab,
        AppStrings.salesTab,
        // AppStrings.reportsTab,
        AppStrings.registerBroker,
        AppStrings.agentNetwork,
        AppStrings.salesDoc,
        AppStrings.commissionRevenueInfoTitle,
        AppStrings.userProfile,
      ];
    case UserRole.brokerage:
      return [
        AppStrings.dashboardTab,
        AppStrings.agentsTab,
        AppStrings.salesTab,
        // AppStrings.reportsTab,
        AppStrings.registerAgent,
        AppStrings.registerStaff,
        AppStrings.agentNetwork,
        AppStrings.salesDoc,
        AppStrings.commissionRevenueInfoTitle,
        AppStrings.userProfile,
      ];
    case UserRole.agent:
      return [
        AppStrings.dashboardTab,
        AppStrings.agentsTab,
        AppStrings.salesTab,
        // AppStrings.reportsTab,
        AppStrings.registerAgent,
        AppStrings.agentNetwork,
        AppStrings.salesDoc,
        AppStrings.uploadSalesDocument,
        AppStrings.commissionRevenueInfoTitle,
        AppStrings.userProfile,
      ];
    default:
      return [AppStrings.dashboardTab];
  }
}
