import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui_web' as ui_web;
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import '../theme/app_fonts.dart';
import '../theme/app_theme.dart';

class FilePreviewUtils {
  /// Show file preview in a dialog (supports PDF and images)
  static Future<void> previewFile(
    BuildContext context,
    PlatformFile file,
  ) async {
    // Determine file type
    final extension = file.extension?.toLowerCase();
    final isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
    final isPdf = extension == 'pdf';

    if (isImage) {
      await _showImagePreview(context, file);
    } else if (isPdf) {
      await _showPdfPreview(context, file);
    } else {
      // For other file types, show a simple info dialog
      await _showUnsupportedFileDialog(context, file);
    }
  }

  /// Show image preview in a dialog
  static Future<void> _showImagePreview(
    BuildContext context,
    PlatformFile file,
  ) async {
    await showDialog(
      context: context,
      builder: (ctx) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            child: Column(
              children: [
                // Header
                _buildPreviewHeader(ctx, file.name),
                // Image content
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: _buildImageWidget(file),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Show PDF preview in a dialog
  static Future<void> _showPdfPreview(
    BuildContext context,
    PlatformFile file,
  ) async {
    await showDialog(
      context: context,
      builder: (ctx) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            child: Column(
              children: [
                // Header
                _buildPreviewHeader(ctx, file.name),
                // PDF content
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: kIsWeb
                        ? _buildWebPdfPreview(file)
                        : _buildMobilePdfPreview(file, context),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build preview header
  static Widget _buildPreviewHeader(BuildContext context, String fileName) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlueColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              fileName,
              style: AppFonts.semiBoldTextStyle(
                18,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Build image widget for preview
  static Widget _buildImageWidget(PlatformFile file) {
    if (kIsWeb && file.bytes != null) {
      // Web: Use bytes directly
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: InteractiveViewer(
            minScale: 0.5,
            maxScale: 4.0,
            child: Image.memory(
              file.bytes!,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.red),
                      SizedBox(height: 16),
                      Text('Failed to load image'),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );
    } else if (!kIsWeb && file.path != null) {
      // Mobile: Use file path
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: InteractiveViewer(
            minScale: 0.5,
            maxScale: 4.0,
            child: Image.file(
              File(file.path!),
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.red),
                      SizedBox(height: 16),
                      Text('Failed to load image'),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );
    } else {
      return const Center(child: Text('No image data available'));
    }
  }

  /// Web PDF preview using Blob URL
  static Widget _buildWebPdfPreview(PlatformFile file) {
    if (file.bytes == null || file.bytes!.isEmpty) {
      return const Center(child: Text('No PDF data available'));
    }

    // Create a unique view type
    final viewType = 'pdf-preview-${DateTime.now().millisecondsSinceEpoch}';

    // Register the view factory
    ui_web.platformViewRegistry.registerViewFactory(viewType, (int viewId) {
      // Create Blob instead of data URL for better performance with large files
      final blob = html.Blob([file.bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);

      final iframe = html.IFrameElement()
        ..src = url
        ..style.border = 'none'
        ..style.width = '100%'
        ..style.height = '100%';

      return iframe;
    });

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: HtmlElementView(viewType: viewType),
      ),
    );
  }

  /// Mobile PDF preview (placeholder with option to open)
  static Widget _buildMobilePdfPreview(
    PlatformFile file,
    BuildContext context,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.picture_as_pdf, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('PDF Preview', style: AppFonts.mediumTextStyle(18)),
            const SizedBox(height: 8),
            Text(
              file.name,
              style: AppFonts.regularTextStyle(14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '${(file.size / (1024 * 1024)).toStringAsFixed(2)} MB',
              style: AppFonts.regularTextStyle(12, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                if (file.bytes == null || file.bytes!.isEmpty) {
                  return;
                }
                try {
                  final dir = await getTemporaryDirectory();
                  final tempFile = File('${dir.path}/${file.name}');
                  await tempFile.writeAsBytes(file.bytes!, flush: true);
                  await OpenFilex.open(tempFile.path);
                } catch (e) {
                  debugPrint('Error opening PDF: $e');
                }
              },
              icon: const Icon(Icons.open_in_new),
              label: const Text('Open in PDF Viewer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryBlueColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show dialog for unsupported file types
  static Future<void> _showUnsupportedFileDialog(
    BuildContext context,
    PlatformFile file,
  ) async {
    await showDialog(
      context: context,
      builder: (ctx) {
        return AlertDialog(
          title: Text(
            'Preview Not Available',
            style: AppFonts.semiBoldTextStyle(18),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Preview is not available for this file type.',
                style: AppFonts.regularTextStyle(14),
              ),
              const SizedBox(height: 16),
              Text(
                'File: ${file.name}',
                style: AppFonts.mediumTextStyle(14),
              ),
              Text(
                'Size: ${(file.size / 1024).toStringAsFixed(2)} KB',
                style: AppFonts.regularTextStyle(12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(),
              child: Text(
                'Close',
                style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryBlueColor),
              ),
            ),
          ],
        );
      },
    );
  }

  // Keep the old method for backward compatibility
  @Deprecated('Use showFilePreview instead')
  static Future<void> showPdfPreview(
    BuildContext context,
    Uint8List pdfBytes,
    String fileName,
  ) async {
    // Convert to PlatformFile format
    final file = PlatformFile(
      name: fileName,
      size: pdfBytes.length,
      bytes: pdfBytes,
    );
    await _showPdfPreview(context, file);
  }
}