import 'dart:async';

import 'package:dio/dio.dart' show Dio;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:neorevv/src/presentation/cubit/filter/filter_cubit.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/enum/user_role.dart';
import '../../../core/enum/user_status.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/column_mapping_utils.dart';
import '../../../core/utils/format_currency_dollar.dart';
import '../../../core/utils/format_phone_number.dart';
import '../../../core/utils/regex.dart';
import '../../../data/repository/commission_revenue_repository_impl.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../../domain/models/user.dart';
import '../../cubit/commission_revenue_info/commission_revenue_cubit.dart';
import '../dashboard/components/commission_revenue_info.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/date_formatter.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/domain/models/agent_model.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsListScreen extends HookWidget {
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;
  final Function(String userId, String userName, {bool isBrokerageList})?
  onNavigateToCommissionInfo;

  const AgentsListScreen({
    super.key,
    required this.onNavigateToAgentNetworkAgent,
    required this.onNavigateToCommissionInfo,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getAgentBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    final filterCubit = context.read<FilterCubit>();
    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role.toString() ?? "";
    final sortColumn = useState<String>('created_at');
    final sortOrder = useState<String>('ASC');
    final ValueNotifier<String?> searchString = useState('');
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalElements = useState(0);
    final brokerageFilterOptions = useState<List<TableFilter>>([]);
    final agentFilterOptions = useState<List<TableFilter>>([]);
    final statesFilterOptions = useState<List<TableFilter>>([]);
    final citiesFilterOptions = useState<List<TableFilter>>([]);
    final currentFilters = useState<Map<String, dynamic>>({});
    final associatedBrokerageId = useState<String>('');
    final filteredAgentId = useState<String>('');
    final isActive = useState<bool?>(null);
    final userStatusFilterOptions = useState<List<TableFilter>>([]);
    final ValueNotifier<String?> selectedState = useState(null);
    final ValueNotifier<String?> selectedCity = useState(null);
    final ValueNotifier<String?> previousState = useState(null);
    final ValueNotifier<String?> previousBrokerage = useState(null);
    final filterSearchTexts = useState<Map<String, String>>({});
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');

    // Add this useEffect to fetch initial data
    useEffect(() {
      Future.microtask(() async {
        if (context.mounted && user != null) {
          _fetchAgents(
            context,
            user,
            selectedDate: selectedDate.value,
            sortBy: sortByDefault,
            page: 0,
            searchString: searchString.value,
            sortDirection: sortOrder.value,
            associatedBrokerageId: associatedBrokerageId.value,
            agentId: filteredAgentId.value,
            active: isActive.value,
            cityId: selectedCity.value,
            stateId: selectedState.value,
          );
        }
        await updateDropDownOptions(
          filterCubit,
          brokerageFilterOptions,
          user,
          agentFilterOptions,
          userStatusFilterOptions,
          statesFilterOptions,
          citiesFilterOptions,
        );
      });
      return null;
    }, [user?.userId]);

    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);

    List<String> getFilterColumnNames() {
      List<String> filterColumns = [];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        filterColumns.add(agentRelatedBrokerage);
      }
      filterColumns.addAll([
        agentName,
        agentStatus,
        agentJoinDate,
        agentState,
        agentCity,
      ]);

      return filterColumns;
    }

    final filterColumnNames = getFilterColumnNames();
    // Get user from UserCubit
    final userCubit = context.read<UserCubit>().state;

    if (userCubit is! UserLoaded) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }

    return BlocConsumer<AgentCubit, AgentState>(
      listener: (BuildContext context, AgentState state) {
        if (state is AgentLoaded) {
          pageCount.value = state.totalPages;
          totalElements.value = state.totalCount;
        }
      },
      builder: (context, state) {
        List<AgentModel> agentData = [];
        String? errorMessage;
        if (state is AgentLoaded) {
          agentData = state.agents;
        } else if (state is AgentError) {
          errorMessage = state.message;
        }

        void handleSort(String columnName, bool ascending) async {
          // Check if columnName is empty (indicates reset to default sort)
          if (columnName.isEmpty) {
            sortColumn.value = 'created_at';
            sortOrder.value = 'ASC';

            if (context.mounted) {
              await _fetchAgents(
                context,
                user,
                selectedDate: selectedDate.value,
                sortBy: 'created_at',
                sortDirection: 'ASC',
                searchString: searchString.value ?? "",
                page: currentpage.value,
                associatedBrokerageId: associatedBrokerageId.value,
                agentId: filteredAgentId.value,
                active: isActive.value,
                cityId: selectedCity.value,
                stateId: selectedState.value,
              );
            }
            return;
          }
          String backendColumnName = _getBackendColumnName(columnName);
          sortColumn.value = backendColumnName;
          sortOrder.value = ascending ? 'ASC' : 'DESC';
          if (columnName == agentDirectRecruits) {
            return;
          }
          if (context.mounted) {
            await _fetchAgents(
              context,
              user,
              selectedDate: selectedDate.value,
              sortBy: backendColumnName,
              sortDirection: sortOrder.value,
              searchString: searchString.value ?? "",
              page: currentpage.value,
              associatedBrokerageId: associatedBrokerageId.value,
              agentId: filteredAgentId.value,
              active: isActive.value,
              cityId: selectedCity.value,
              stateId: selectedState.value,
            );
          }
        }

        if (errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? Icons.lock_outline
                      : Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? authenticationRequired
                      : errorLoadingData,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage.contains(unauthorizedStatus) ||
                          errorMessage.contains(unauthorizedText)
                      ? pleaseLoginToAccessAgentData
                      : '$errorPrefix$errorMessage',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    _fetchAgents(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      page: currentpage.value,
                      searchString: searchString.value,
                      sortBy: sortColumn.value,
                      sortDirection: sortOrder.value,
                      associatedBrokerageId: associatedBrokerageId.value,
                      agentId: filteredAgentId.value,
                      active: isActive.value,
                      cityId: selectedCity.value,
                      stateId: selectedState.value,
                    );
                  },
                  child: const Text(retry),
                ),
              ],
            ),
          );
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: CustomDataTableWidget<AgentModel>(
                data: agentData,
                title: agents,
                titleIcon: "$iconAssetpath/user.png",
                searchHint: searchAgent,
                searchFn: (agent) =>
                    agent.fullName +
                    agent.phone +
                    agent.email +
                    agent.state +
                    agent.city +
                    agent.depthLevel +
                    AppDateFormatter.formatDateMMddyyyy(agent.joiningDate) +
                    agent.recruiterName +
                    agent.totalDownlineAgents.toString() +
                    agent.totalSales.toString() +
                    agent.salesCommission.toString() +
                    agent.isActive.toString(),

                // Dynamic filtering system
                filterColumnNames: getFilterColumnNames(),
                dateFilterColumns: const [agentJoinDate],
                showCrossButtonForFilter: const {
                  agentRelatedBrokerage: true,
                  agentName: true,
                  agentStatus: true,
                  agentJoinDate: true,
                  agentState: true,
                  agentCity: true,
                },
                filterValueExtractors: {
                  agentRelatedBrokerage: (agent) => agent.associatedBrokerage,
                  agentName: (agent) => agent.fullName,
                  agentStatus: (agent) => agent.isActive
                      ? UserStatus.active.value
                      : UserStatus.inactive.value,
                },
                // Use filterData and brokerFilterData for dropdown options in the filter section, mapping id to value
                filterOptions: {
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    agentRelatedBrokerage: brokerageFilterOptions.value,
                  if (agentFilterOptions != null)
                    agentName: agentFilterOptions.value,
                  if (userStatusFilterOptions.value.isNotEmpty)
                    agentStatus: userStatusFilterOptions.value,
                  if (statesFilterOptions.value.isNotEmpty)
                    agentState: statesFilterOptions.value,
                  if (citiesFilterOptions.value.isNotEmpty)
                    agentCity: citiesFilterOptions.value,
                },
                filterOnChangeConfig: {
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    agentRelatedBrokerage:
                        true, // enable listener for this filter
                  agentName: false, // no listener for this filter
                  agentState: true,
                  agentCity: false,
                },
                filterSearchConfig: {
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    agentRelatedBrokerage: true,
                  agentName: true,
                  agentStatus: false,
                  agentState: true,
                  agentCity: true,
                  // ...
                },
                onFilterChange:
                    (
                      filterKey,
                      selectedValue, [
                      clearDependentFilters,
                      searchString,
                    ]) async {
                      if (searchString != null) {
                        // Store search text for any filter that uses search
                        filterSearchTexts.value = {
                          ...filterSearchTexts.value,
                          filterKey: searchString,
                        };
                      }
                      // If the filterKey is agentRelatedBrokerage, fetch agent filter options with selectedValue
                      if (filterKey == agentRelatedBrokerage) {
                        // Clear the dependent agent filter when brokerage changes
                        if (previousBrokerage.value != selectedValue) {
                          if (clearDependentFilters != null) {
                            clearDependentFilters([agentName]);
                          }
                          previousBrokerage.value = selectedValue;
                        }
                        await filterCubit.getAgentFilterOptions(
                          userId: user?.userId,
                          selectedValueParam: selectedValue,
                          includeSelf: false,
                        );
                        FilterState state = filterCubit.state;
                        if (state is FilterLoaded) {
                          agentFilterOptions.value = state.filterOptions;
                        }
                      }
                      // If the filterKey is agentState, fetch cities filter options with selected state
                      else if (filterKey == agentState) {
                        // Handle state filter changes (including clearing)
                        if (searchString == null) {
                          // This is a selection/clear event, not a search
                          filterSearchTexts.value = {
                            ...filterSearchTexts.value,
                            agentCity: '',
                          };

                          // Check if state was cleared (null or empty)
                          if (selectedValue == null || selectedValue.isEmpty) {
                            // State was cleared - clear city options and dependent filter
                            citiesFilterOptions.value = [];
                            if (clearDependentFilters != null) {
                              clearDependentFilters([agentCity]);
                            }
                            previousState.value = null;
                            selectedState.value = null;
                          } else {
                            // State was selected - load cities for that state
                            if (previousState.value != selectedValue) {
                              if (clearDependentFilters != null) {
                                clearDependentFilters([agentCity]);
                              }
                              previousState.value = selectedValue;
                            }
                            selectedState.value = selectedValue;

                            await filterCubit.getCitiesFilterOptions(
                              selectedState: selectedValue,
                              searchString: null,
                            );
                            FilterState state = filterCubit.state;
                            if (state is FilterLoaded) {
                              citiesFilterOptions.value = state.filterOptions;
                            }
                          }
                        }
                      }
                      // Handle city search - this will be called as user types
                      else if (filterKey == agentCity) {
                        // If searchString is provided (not null), update the search text
                        if (searchString != null) {
                          await filterCubit.getCitiesFilterOptions(
                            selectedState: selectedState.value ?? selectedValue,
                            searchString: searchString.isEmpty
                                ? ''
                                : searchString,
                          );
                          FilterState state = filterCubit.state;
                          if (state is FilterLoaded) {
                            citiesFilterOptions.value = state.filterOptions;
                          }
                        }
                      }
                    },
                columnNames: [
                  agentName,
                  agentContact,
                  agentEmail,
                  agentJoinDate,
                  agentState,
                  agentCity,
                  agentLevel,
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    agentRelatedBrokerage,
                  agentRefferedBy,
                  agentDirectRecruits,
                  agentTotalSales,
                  agentTotalRevenue,
                  agentCommission,
                  agentRevenueShare,
                  agentAdditionalNotes,
                  agentStatus,
                ],
                showSortIconColumns: [
                  agentName,
                  agentContact,
                  agentEmail,
                  agentJoinDate,
                  agentState,
                  agentCity,
                  agentLevel,
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    agentRelatedBrokerage,
                  agentRefferedBy,
                  agentTotalSales,
                  agentTotalRevenue,
                  agentCommission,
                  agentRevenueShare,
                  agentStatus,
                ],
                dateFilterBannerTexts: const {
                  agentJoinDate: 'Users joined on or after',
                },
                cellBuilders: [
                  (agent) => agent.fullName,
                  (agent) => PhoneUtils.formatPhoneNumber(agent.phone),
                  (agent) => agent.email,
                  (agent) =>
                      AppDateFormatter.formatDateMMddyyyy(agent.joiningDate),
                  (agent) => agent.state,
                  (agent) => agent.city,
                  (agent) => agent.depthLevel,
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    (agent) => agent.associatedBrokerage,
                  (agent) => agent.recruiterName,
                  (agent) => agent.totalDownlineAgents.toString(),
                  (agent) => agent.totalSales.toString(),
                  (agent) => formatCurrencyDollar(agent.salesVolume),
                  (agent) => formatCurrencyDollar(agent.salesCommission),
                  (agent) => formatCurrencyDollar(agent.revenueShare),
                  (agent) => agent.additionalNotes,
                  (agent) => agent.isActive
                      ? UserStatus.active.value
                      : UserStatus.inactive.value,
                ],
                iconCellBuilders: [
                  (agent) => TableCellData(
                    text: agent.fullName,
                    leftIconAsset: "$iconAssetpath/agent_round.png",
                    iconSize: 28,
                  ), // name - use icon
                  null, // contact
                  null, // email
                  null, // joinDate
                  null, // state
                  null, // city
                  null, // level
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    null, // associatedBrokerage
                  null, // referredBy
                  null, // totalAgents
                  null, // totalSales
                  null, // totalRevenue
                  null, // commission
                  null, //additional status
                  null, // status
                ],
                useIconBuilders: [
                  true, // name - use icon
                  false, // contact
                  false, // email
                  false, // joinDate
                  false, // state
                  false, // city
                  false, // level
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    false, // associatedBrokerage
                  false, // referredBy
                  false, // totalAgents
                  false, // totalSales
                  false, // totalRevenue
                  false, // commission
                  false, //additional info
                  false, // status
                ],
                widgetCellBuilders: [
                  null, // name - use icon builder
                  null, // contact - use text
                  null, // email - use text
                  null, // joinDate - use text
                  null, // state - use text
                  null, // city - use text
                  null, // level - use text
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    null, // associatedBrokerage - use text
                  null, // referredBy - use text
                  null, // totalAgents - use text
                  null, // totalSales - use text
                  null, // totalRevenue - use text
                  null, // commission - use text
                  (context, agent) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: agent.isActive
                          ? AppTheme.agentStatusActiveBg.withAlpha(36)
                          : AppTheme.agentStatusInactiveBg.withAlpha(36),
                      borderRadius: BorderRadius.circular(
                        20,
                      ), // More rounded for oval shape
                    ),
                    child: Text(
                      agent.isActive
                          ? UserStatus.active.value
                          : UserStatus.inactive.value,
                      style: AppFonts.mediumTextStyle(
                        12,
                        color: agent.isActive
                            ? AppTheme
                                  .statusActiveText // Darker green text
                            : AppTheme.statusInactiveText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  null,
                ],
                // Boolean flags to indicate which columns use widget builders
                useWidgetBuilders: [
                  false, // name
                  false, // contact
                  false, // email
                  false, // joinDate
                  false, // state
                  false, // city
                  false, // level
                  if (userRole == UserRole.admin.toString() ||
                      userRole == UserRole.platformOwner.toString())
                    false, // associatedBrokerage
                  false, // referredBy
                  false, // totalAgents
                  false, // totalSales
                  false, // totalRevenue
                  false, // commission
                  false, //additional info
                  true, // status - use widget builder
                ],
                actionBuilders: [
                  (context, agent) => ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isCompact: true,
                    isMobile: false,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                  (context, agent) => ActionButtonEye(
                    onPressed: () {
                      onNavigateToCommissionInfo != null
                          ? onNavigateToCommissionInfo!(
                              agent.userId,
                              agent.fullName,
                              isBrokerageList: false,
                            )
                          : () {};
                    },
                    isCompact: true,
                    isMobile: false,
                    icon:
                        "$iconAssetpath/link_icon.png", // optional custom icon asset
                    padding: 2.0,
                    name: agent.fullName,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                ],

                mobileCardBuilder: (context, agent) =>
                    _buildMobileAgentCard(agent, context, userRole),
                onSort: handleSort,
                emptyStateMessage: noDataAvailable,
                pageCount: pageCount.value,
                isLoading: state is AgentLoading,
                totalElements: totalElements.value,
                currentPageIndex: currentpage.value,
                handleTableSearch: (value) async {
                  // Cancel any pending search
                  searchDebouncer.value?.cancel();

                  // Store the search value immediately for UI feedback
                  searchString.value = value;

                  // Don't search if value hasn't changed
                  if (lastSearchValue.value == value) {
                    return;
                  }

                  lastSearchValue.value = value;

                  // Debounce: wait 300ms before making the API call
                  searchDebouncer.value = Timer(
                    const Duration(milliseconds: 300),
                    () async {
                      currentpage.value = 0;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        page: 0,
                        searchString: value,
                        sortDirection: sortOrder.value,
                        associatedBrokerageId: associatedBrokerageId.value,
                        agentId: filteredAgentId.value,
                        active: isActive.value,
                        sortBy: sortColumn.value,
                        cityId: selectedCity.value,
                        stateId: selectedState.value,
                      );
                    },
                  );
                },
                handlePagination: (page) async {
                  if (sortColumn.value.isNotEmpty &&
                      RegExUtils.startsWithUppercase.hasMatch(
                        sortColumn.value,
                      )) {
                    String backendColumnName = _getBackendColumnName(
                      sortColumn.value,
                    );
                    sortColumn.value = backendColumnName;
                  }
                  currentpage.value = page;
                  await _fetchAgents(
                    context,
                    user,
                    selectedDate: selectedDate.value,
                    searchString: searchString.value ?? "",
                    page: page,
                    sortDirection: sortOrder.value,
                    associatedBrokerageId: associatedBrokerageId.value,
                    agentId: filteredAgentId.value,
                    active: isActive.value,
                    sortBy: sortColumn.value,
                    cityId: selectedCity.value,
                    stateId: selectedState.value,
                  );
                },
                onAllFiltersChanged: (allFilters) async {
                  // Store current filters
                  currentFilters.value = allFilters;
                  String? relatedBrokerageValue;
                  String? agentNameValue;
                  String? statusId;
                  DateTime? joiningDate;
                  currentpage.value = 0;
                  if (allFilters.containsKey(joinDateColumnHeader)) {
                    joiningDate = allFilters[joinDateColumnHeader] as DateTime?;
                    selectedDate.value = joiningDate;
                  } else {
                    selectedDate.value = null;
                  }
                  // Extract dropdown filters
                  if (allFilters.containsKey(agentRelatedBrokerage)) {
                    relatedBrokerageValue = allFilters[agentRelatedBrokerage]
                        ?.toString();
                    associatedBrokerageId.value = relatedBrokerageValue!;
                  } else {
                    associatedBrokerageId.value = "";
                  }
                  if (allFilters.containsKey(agentName)) {
                    agentNameValue = allFilters[agentName]?.toString();
                    filteredAgentId.value = agentNameValue!;
                  } else {
                    filteredAgentId.value = "";
                  }
                  //status check
                  if (allFilters.containsKey(agentStatus)) {
                    statusId = allFilters[agentStatus] as String?;
                    final selectedFilter = userStatusFilterOptions.value
                        .firstWhere(
                          (filter) => filter.id == statusId,
                          orElse: () => TableFilter(
                            id: '',
                            key: '',
                            value: '',
                          ), // Default empty filter
                        );
                    if (selectedFilter.value == UserStatus.active.value) {
                      isActive.value = true;
                    } else if (selectedFilter.value ==
                        UserStatus.inactive.value) {
                      isActive.value = false;
                    } else if (selectedFilter.value ==
                        UserStatus.pendingVerification.value) {
                      isActive.value = null;
                    } else {
                      isActive.value = null;
                    }
                  } else {
                    isActive.value = null;
                  }

                  // Handle state filter with previous state tracking
                  if (allFilters.containsKey(agentState)) {
                    String? stateId = allFilters[agentState] as String?;

                    if (stateId == null || stateId.isEmpty) {
                      // State was cleared - reset both state and city
                      selectedState.value = null;
                      selectedCity.value = null;
                      previousState.value = null;
                      citiesFilterOptions.value = [];
                    } else {
                      // State has a value - only reset city if state changed
                      if (previousState.value != stateId) {
                        selectedCity.value = null;
                        previousState.value = stateId;
                      }
                      selectedState.value = stateId;
                    }
                  } else {
                    // Reset state when not in filters
                    selectedState.value = null;
                    selectedCity.value = null;
                    previousState.value = null;
                    citiesFilterOptions.value = [];
                  }

                  if (allFilters.containsKey(agentCity)) {
                    String? cityId = allFilters[agentCity] as String?;
                    selectedCity.value = cityId;
                  } else {
                    selectedCity.value = null;
                  }
                  //call api to filter the data
                  if (context.mounted && user != null) {
                    await _fetchAgents(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      sortBy: sortByDefault,
                      associatedBrokerageId: associatedBrokerageId.value,
                      agentId: filteredAgentId.value,
                      page: 0,
                      searchString: searchString.value ?? "",
                      sortDirection: sortOrder.value,
                      active: isActive.value,
                      cityId: selectedCity.value,
                      stateId: selectedState.value,
                    );
                  }
                },
                filterTooltips: {agentCity: AppStrings.cityTooltipFilter},
                filterInitialSearchText: filterSearchTexts.value,
                onResetFilters: ({required bool isFilterOptionOnlyReload}) async {
                  if (isFilterOptionOnlyReload) {
                    filterSearchTexts.value = {};
                    await updateDropDownOptions(
                      filterCubit,
                      brokerageFilterOptions,
                      user,
                      agentFilterOptions,
                      userStatusFilterOptions,
                      statesFilterOptions,
                      citiesFilterOptions,
                    );
                  } else {
                    filterSearchTexts.value = {};
                    selectedDate.value = null; // DateTime? type
                    currentpage.value =
                        0; // int type (reset to first page, usually 0 or 1)
                    searchString.value = ''; // String? type (empty string)
                    sortColumn.value =
                        'created_at'; // String type (default sort column, or set to '')
                    sortOrder.value =
                        'ASC'; // String type (default sort order, or set to '')
                    associatedBrokerageId.value =
                        ''; // String type (empty string)
                    filteredAgentId.value = ''; // String type (empty string)
                    isActive.value = null;
                    selectedCity.value = null;
                    selectedState.value = null;
                    statesFilterOptions.value = [];
                    citiesFilterOptions.value = [];
                    previousState.value = null;
                    previousBrokerage.value = null;
                    // Fetch filter options again
                    await updateDropDownOptions(
                      filterCubit,
                      brokerageFilterOptions,
                      user,
                      agentFilterOptions,
                      userStatusFilterOptions,
                      statesFilterOptions,
                      citiesFilterOptions,
                    );
                    _fetchAgents(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      page: 0,
                      searchString: searchString.value,
                      sortBy: sortColumn.value,
                      sortDirection: sortOrder.value,
                      associatedBrokerageId: associatedBrokerageId.value,
                      agentId: filteredAgentId.value,
                      active: isActive.value,
                      cityId: selectedCity.value,
                      stateId: selectedState.value,
                    );
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> updateDropDownOptions(
    FilterCubit filterCubit,
    ValueNotifier<List<TableFilter>> brokerageFilterOptions,
    User? user,
    ValueNotifier<List<TableFilter>> agentFilterOptions,
    ValueNotifier<List<TableFilter>> userStatusFilterOptions,
    ValueNotifier<List<TableFilter>> statesFilterOptions,
    ValueNotifier<List<TableFilter>> citiesFilterOptions,
  ) async {
    await filterCubit.getBrokerageFilterOptions();
    FilterState state = filterCubit.state;
    if (state is FilterLoaded) {
      brokerageFilterOptions.value = state.filterOptions;
    }
    await filterCubit.getAgentFilterOptions(
      userId: user?.userId,
      selectedValueParam: null,
      includeSelf: false,
    );
    state = filterCubit.state;
    if (state is FilterLoaded) {
      agentFilterOptions.value = state.filterOptions;
    }
    await filterCubit.getUserStatusFilterOptions();
    final statusState = filterCubit.state;
    if (statusState is FilterLoaded) {
      userStatusFilterOptions.value = statusState.filterOptions;
    }
    await filterCubit.getStatesFilterOptions(selectedCountry: '');
    final statesState = filterCubit.state;
    if (statesState is FilterLoaded) {
      statesFilterOptions.value = statesState.filterOptions;
    }

    await filterCubit.getCitiesFilterOptions(
      selectedState: null,
      searchString: null,
    );
    final citiesState = filterCubit.state;
    if (citiesState is FilterLoaded) {
      citiesFilterOptions.value = citiesState.filterOptions;
    }
  }

  _fetchAgents(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required String sortBy,
    required String sortDirection,
    required String associatedBrokerageId,
    required String agentId,
    required bool? active,
    required int page,
    required String? searchString,
    required String? cityId,
    required String? stateId,
  }) {
    if (context.mounted) {
      final dateString = AppDateFormatter.formatToUtcIso8601(selectedDate);

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortBy,
        "sortDirection": sortDirection,
        "searchString": searchString,
        "joiningDate": dateString,
        "associatedBrokerageId": associatedBrokerageId,
        "agentId": agentId,
        "userId": user?.userId,
        "active": active,
        "cityId": cityId,
        "stateId": stateId,
      };
      context.read<AgentCubit>().getAgents(requestBody: payload);
    }
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    if (onNavigateToAgentNetworkAgent != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetworkAgent!(agent);
      } catch (e) {
        // If no matching broker found, show error message
        AppSnackBar.showSnackBar(
          context,
          'Broker not found: ${agent.fullName}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      AppSnackBar.showSnackBar(
        context,
        'Action clicked for ${agent.fullName}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Widget _buildMobileAgentCard(
    AgentModel agent,
    BuildContext context,
    String userRole,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SelectableText(
                agent.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.isActive
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.isActive ? active : inactive,
                  style: AppFonts.mediumTextStyle(12, color: AppTheme.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SelectableText(
            '$agentContact: ${PhoneUtils.formatPhoneNumber(agent.phone)}',
          ),
          SelectableText('$agentEmail: ${agent.email}'),
          SelectableText(
            '$agentJoinDate: ${AppDateFormatter.formatDateMMddyyyy(agent.joiningDate)}',
          ),
          SelectableText('$agentState: ${agent.state}'),
          SelectableText('$agentCity: ${agent.city}'),
          SelectableText('$agentLevel: ${agent.depthLevel}'),
          if (userRole == UserRole.admin.toString() ||
              userRole == UserRole.platformOwner.toString())
            SelectableText(
              '$agentRelatedBrokerage: ${agent.associatedBrokerage}',
            ),
          SelectableText('$agentRefferedBy: ${agent.recruiterName}'),
          SelectableText('$agentDirectRecruits: ${agent.totalDownlineAgents}'),
          SelectableText('$agentTotalSales: ${agent.totalSales}'),
          SelectableText(
            '$agentTotalRevenue: ${formatCurrencyDollar(agent.salesVolume)}',
            //'$agentTotalRevenue: $currencySymbol${agent.totalRevenue.toStringAsFixed(2)}',
          ),
          SelectableText(
            '$agentCommission: ${formatCurrencyDollar(agent.salesCommission)}',
          ),
          SelectableText(
            '$agentCommission: ${formatCurrencyDollar(agent.revenueShare)}',
          ),
          SelectableText('$agentAdditionalNotes: ${agent.additionalNotes}'),

          SelectableText(
            '$agentStatus: ${agent.isActive ? UserStatus.active.value : UserStatus.inactive.value}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // View Agent Details Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isMobile: true,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                ),
                const SizedBox(height: 8), // Spacing between buttons
                // View Commission Revenue Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () {
                      if (onNavigateToCommissionInfo != null) {
                        onNavigateToCommissionInfo!(
                          agent.userId,
                          agent.fullName,
                          isBrokerageList: false,
                        );
                      }
                    },
                    isMobile: true,
                    icon: "$iconAssetpath/link_icon.png",
                    padding: 2.0,
                    name: agent.fullName,
                    tooltipMessage: AppStrings.viewAgentHierarchy,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
