import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';

/// Enum representing the status of an upload task
enum UploadTaskStatus {
  queued,
  uploading,
  processing,
  completed,
  failed,
  cancelled,
}

/// Model representing a single file to be uploaded
class UploadFileInfo {
  final String id;
  final String type;
  final String title;
  final PlatformFile file;

  UploadFileInfo({
    required this.id,
    required this.type,
    required this.title,
    required this.file,
  });
}

/// Model representing an upload task
class UploadTask {
  final String taskId;
  final String userId;
  final String categoryType;
  final String representingId;
  final List<UploadFileInfo> files;
  final DateTime createdAt;

  UploadTaskStatus status;
  double progress;
  String? currentFileName;
  int currentFileIndex;
  String? errorMessage;
  String? salesId;
  CancelToken? cancelToken;
  String? uploadStatus;

  // Navigation tracking
  String? originatingRoute; // The route where upload was initiated
  bool userNavigatedAway; // Whether user navigated away from originating route

  UploadTask({
    required this.taskId,
    required this.userId,
    required this.categoryType,
    required this.representingId,
    required this.files,
    DateTime? createdAt,
    this.status = UploadTaskStatus.queued,
    this.progress = 0.0,
    this.currentFileName,
    this.currentFileIndex = 0,
    this.errorMessage,
    this.salesId,
    this.cancelToken,
    this.originatingRoute,
    this.userNavigatedAway = false,
    this.uploadStatus,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Get total number of files in this task
  int get totalFiles => files.length;

  /// Check if task is in progress
  bool get isInProgress =>
      status == UploadTaskStatus.uploading ||
      status == UploadTaskStatus.processing;

  /// Check if task is completed
  bool get isCompleted => status == UploadTaskStatus.completed;

  /// Check if task has failed
  bool get isFailed => status == UploadTaskStatus.failed;

  /// Check if task is cancelled
  bool get isCancelled => status == UploadTaskStatus.cancelled;

  /// Check if task can be cancelled
  bool get canBeCancelled =>
      status == UploadTaskStatus.queued ||
      status == UploadTaskStatus.uploading ||
      status == UploadTaskStatus.processing;

  /// Create a copy of the task with updated fields
  UploadTask copyWith({
    UploadTaskStatus? status,
    double? progress,
    String? currentFileName,
    int? currentFileIndex,
    String? errorMessage,
    String? salesId,
    CancelToken? cancelToken,
    String? originatingRoute,
    bool? userNavigatedAway,
    String? uploadStatus,
  }) {
    return UploadTask(
      taskId: taskId,
      userId: userId,
      categoryType: categoryType,
      representingId: representingId,
      files: files,
      createdAt: createdAt,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      currentFileName: currentFileName ?? this.currentFileName,
      currentFileIndex: currentFileIndex ?? this.currentFileIndex,
      errorMessage: errorMessage ?? this.errorMessage,
      salesId: salesId ?? this.salesId,
      cancelToken: cancelToken ?? this.cancelToken,
      originatingRoute: originatingRoute ?? this.originatingRoute,
      userNavigatedAway: userNavigatedAway ?? this.userNavigatedAway,
      uploadStatus: uploadStatus ?? this.uploadStatus,
    );
  }

  /// Get total size of all files in bytes
  int get totalSize {
    return files.fold(0, (sum, file) => sum + file.file.size);
  }

  /// Get formatted status message
  String getStatusMessage() {
    switch (status) {
      case UploadTaskStatus.queued:
        return 'Queued';
      case UploadTaskStatus.uploading:
        return 'Uploading ${currentFileIndex + 1}/$totalFiles';
      case UploadTaskStatus.processing:
        return 'Processing';
      case UploadTaskStatus.completed:
        return 'Completed';
      case UploadTaskStatus.failed:
        return 'Failed: ${errorMessage ?? "Unknown error"}';
      case UploadTaskStatus.cancelled:
        return 'Cancelled';
    }
  }
}

