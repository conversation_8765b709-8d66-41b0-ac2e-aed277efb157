import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/network/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/network/api_config.dart';
import '../../core/services/firebase_auth_service.dart';
import '../../domain/models/google_auth_result.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;
  static const String logoutUrl = APIConfig.logout;
  static const String createPasswordUrl = APIConfig.createPassword;
  static const String socialSignInUrl = APIConfig.socialSignIn;
  static const String forgotPasswordVerifyMail =
      APIConfig.forgotPasswordVerifyMail;
  static const String forgotPasswordValidateToken =
      APIConfig.forgotPasswordValidateToken;
  static const String forgotPasswordReset = APIConfig.forgotPasswordReset;
  static const String resetPasswordUrl = APIConfig.resetPassword;

  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      final response = await _dio.post(loginUrl, data: payload);

      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<LoginModel> signInWithGoogle(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      final response = await _dio.post(socialSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedSocialSignin);
    } catch (e) {
      rethrow; // let Cubit handle error
    }
  }

  @override
  Future<LoginModel> signInWithApple(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      // final payload = {'idToken': result.idToken};
      final response = await _dio.post(socialSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedSocialSignin);
    } catch (e) {
      rethrow; // let Cubit handle error
    }
  }

  Future<bool> logout() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(logoutUrl);

      if (response.statusCode == 200) {
        developer.log(
          'User logged out successfully',
          name: 'AuthRepositoryImpl',
        );
        return true;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  ///v1/auth/password

  @override
  Future<dynamic> createPassword(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(createPasswordUrl, data: payload);

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? '';
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future verifyEmail(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(forgotPasswordVerifyMail, data: payload);

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? '';
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future validateToken(String token) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get('$forgotPasswordValidateToken$token/validation');

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? '';
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, e.error.toString());
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future forgotPassword(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(forgotPasswordReset, data: payload);

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? forgotPasswordResetSuccess;
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future resetPassword(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.put(resetPasswordUrl, data: payload);

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? passwordResetSuccess;
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<Map<String, dynamic>> validatePasswordSetupLink(String userId) async {
    try {
      final dio = await DioClient.getDio();
      String url =
          '${APIConfig.passwordSetupLinkValidation}$userId/password-setup/validation';

      final response = await dio.get(url);

      if (response.statusCode == 200 || response.statusCode == 201) {
        Map<String, dynamic> responseJson = response.data;
        return responseJson;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, invalidPasswordSetupLink);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
