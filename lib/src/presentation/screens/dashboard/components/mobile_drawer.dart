import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/tab_config.dart';
import '../../../../core/enum/user_role.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/utils/helper.dart';
import '../../../../domain/models/user.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';

class MobileDrawer extends StatelessWidget {
  final User? user;
  final int selectedTabIndex;
  final int selectedTab;
  final Function(int) onTabSelected;
  final List<TabConfig> tabs;
  final VoidCallback? onAddNewPressed;
  final VoidCallback? handleProfileNavigation;
 
  const MobileDrawer({
    super.key,
    required this.user,
    required this.selectedTab,
    required this.selectedTabIndex,
    required this.onTabSelected,
    required this.tabs,
    this.onAddNewPressed,
    this.handleProfileNavigation,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Header section with logo and profile
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding,
                vertical: defaultPadding * 2,
              ),
              decoration: BoxDecoration(color: AppTheme.primaryBlueColor),
              child: SafeArea(
                child: Column(
                  children: [
                    // Logo
                    // Image.asset(
                    //   '$launcherAssetpath/logo.png',
                    //   scale: 160 / 55,
                    //   color: Colors.white,
                    // ),
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        mouseCursor: SystemMouseCursors.click,
                        hoverColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () {
                          // Close the drawer first
                          Navigator.pop(context);
                          // Reset to dashboard tab (index 0)
                          if (selectedTabIndex != 0) {
                            onTabSelected(0);
                          }
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Image.asset(
                            '$launcherAssetpath/logo.png',
                            scale: 160 / 55,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: defaultPadding * 2),

                    if (user != null)
                      // Profile info
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundImage: const AssetImage(
                              '$iconAssetpath/agent_round.png',
                            ),
                            radius: 16,
                          ),
                          const SizedBox(width: defaultPadding / 2),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  user!.name,
                                  style: AppFonts.semiBoldTextStyle(
                                    16,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  user?.roleName ?? '',
                                  style: AppFonts.regularTextStyle(
                                    12,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            if (user?.role != UserRole.platformOwner &&
                user?.role != UserRole.admin)
              _buildUserOrgLogo(),

            // Navigation items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: defaultPadding),
                children: [
                  // Dynamic tabs from the tabs list
                  ...tabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    final title = tab.title;
                    final icon = tab.icon;

                    // Skip hidden tabs
                    if (tab.hidden) {
                      return const SizedBox.shrink();
                    }

                    // Skip reports tab for non-platform owners
                    if (title == reportsTab &&
                        user?.role != UserRole.platformOwner) {
                      return const SizedBox.shrink();
                    }

                    return _buildDrawerNavItem(
                      context,
                      title,
                      icon,
                      isSelected: selectedTabIndex == index,
                      onTap: () {
                        onTabSelected(index);
                        Navigator.pop(context); // Close drawer
                      },
                    );
                  }),

                  const Divider(height: defaultPadding * 2),
                  user?.role == UserRole.brokerage
                      ? Column(
                          children: [
                            _buildAddNewBtn(
                              context,
                              user?.role,
                              addNewAgent,
                              () {
                                Navigator.pop(context);
                                if (onAddNewPressed != null) {
                                  onAddNewPressed!();
                                }
                              },
                            ),
                            // _buildAddNewBtn(
                            //   context,
                            //   user?.role,
                            //   addNewStaff,
                            //   () {
                            //     Navigator.pop(context);
                            //     if (onAddNewPressed != null) {
                            //       onAddNewPressed!();
                            //     }
                            //   },
                            // ),
                          ],
                        )
                      : const SizedBox.shrink(),

                  // Action buttons section
                  user?.role != UserRole.platformOwner
                      ? user?.role == UserRole.admin
                            ? _buildAddNewBtn(
                                context,
                                user?.role,
                                addNewBrokerage,
                                () {
                                  Navigator.pop(context);
                                  if (onAddNewPressed != null) {
                                    onAddNewPressed!();
                                  }
                                },
                              )
                            : user?.role == UserRole.agent
                            ? _buildAddNewBtn(
                                context,
                                user?.role,
                                addNewAgent,
                                () {
                                  Navigator.pop(context);
                                  if (onAddNewPressed != null) {
                                    onAddNewPressed!();
                                  }
                                },
                              )
                            : const SizedBox.shrink()
                      : const SizedBox.shrink(),

                  const SizedBox(height: defaultPadding),
                  //TODO implement this in future
                  // Settings and notifications
                  // _buildDrawerActionItem(
                  //   context,
                  //   notifications,
                  //   Icons.notifications_outlined,
                  //   onTap: () {
                  //     Navigator.pop(context);
                  //     // Handle notifications
                  //   },
                  // ),
                  // _buildDrawerActionItem(
                  //   context,
                  //   settings,
                  //   Icons.settings_outlined,
                  //   onTap: () {
                  //     Navigator.pop(context);
                  //     // Handle settings
                  //   },
                  // ),
                ],
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(defaultPadding),
              child: Text(
                dashboardHead,
                style: AppFonts.regularTextStyle(12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddNewBtn(
    BuildContext context,
    UserRole? role,
    String label,
    VoidCallback? onPressed,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: ElevatedButton.icon(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryBlueColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding,
            vertical: defaultPadding,
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        onPressed: onPressed,
        icon: const Icon(Icons.add),
        label: Text(label),
      ),
    );
  }

  Widget _buildDrawerNavItem(
    BuildContext context,
    String title,
    IconData icon, {
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: ListTile(
          leading: Icon(
            icon,
            color: isSelected ? AppTheme.primaryBlueColor : Colors.grey[600],
          ),
          title: Text(
            title,
            style: isSelected
                ? AppFonts.semiBoldTextStyle(
                    14,
                    color: AppTheme.primaryBlueColor,
                  )
                : AppFonts.regularTextStyle(14, color: Colors.black87),
          ),
          selected: isSelected,
          selectedTileColor: AppTheme.primaryBlueColor.withValues(alpha: 0.1),
          onTap:
              onTap, // Use the passed onTap callback instead of the wrong one
        ),
      ),
    );
  }

  Widget _buildDrawerActionItem(
    BuildContext context,
    String title,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey[600]),
      title: Text(
        title,
        style: AppFonts.regularTextStyle(14, color: Colors.black87),
      ),
      onTap: onTap,
    );
  }

  Widget _buildUserOrgLogo() {
    return FutureBuilder<bool>(
      future: isValidImageUrl(user?.logo ?? ''),
      builder: (context, snapshot) {
        bool isValid = snapshot.data ?? false;
        if (!isValid) {
          return const SizedBox.shrink();
        }
        return isValid
            ? CachedNetworkImage(
                imageUrl: user?.logo ?? '',
                scale: (272 / 56),
              )
            : Image.asset('$imageAssetpath/org_logo.png', scale: (272 / 56));
      },
    );
  }
}
