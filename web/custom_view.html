<!doctype html>
<html>
  <body>
    <h3>Embedded HTML View</h3>
    <script>
      window.addEventListener("message", function (event) {
        if (event.data?.type === "FROM_FLUTTER") {
          const token = event.data.token;
          console.log("Token received in HTML:", token);

          // Send response back
          window.parent.postMessage(
            { type: "HTML_RESPONSE", message: "Token processed", token },
            "*"
          );
        }
      });
    </script>
  </body>
</html>
