import '../config/app_strings.dart';
import 'regex.dart';

class InputValidators {
  InputValidators._();

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) return emailIsRequired;
    final emailRegex = RegExUtils.emailRegex;
    if (!emailRegex.hasMatch(value)) return invalidEmail;
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return phoneNumberIsRequired;
    }

    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');
    // If user included US country code '1' (e.g. "1XXXXXXXXXX"), strip it for 10-digit validation
    String normalized = cleanedNumber;
    if (normalized.length == 11 && normalized.startsWith('1')) {
      normalized = normalized.substring(1);
    }
    if (normalized.length != 10) {
      return invalidPhone;
    }

    if (normalized.startsWith('0')) {
      return "Phone number cannot start with 0"; // Specific error message
    }

    if (normalized.length != 10) {
      return phoneNumberExceed;
    }

    return null;
  }

  static String? validateCompanyPhone(String? value) {
    if (value == null || value.isEmpty) {
      return phoneNumberIsRequired;
    }

    // Remove all non-digits (ignores '-', spaces, etc.)
    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');

    // Must be exactly 10 digits
    if (cleanedNumber.length != 10) {
      return invalidPhone;
    }
    if (cleanedNumber.startsWith('0')) {
      return "Phone number cannot start with 0"; // Specific error message
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) return passwordIsRequired;
    if (value.length < 8) return passwordMustBeAtLeast6Characters;
    return null;
  }

  static String? validateConfirmPassword(String? value, String password) {
    if (value != password) return passwordsDoNotMatch;
    return null;
  }

  static String? validateRequiredField(String? value) {
    if (value == null || value.isEmpty) return thisFieldIsRequired;
    return null;
  }

  static String? validateZipCode(String? value) {
    if (value == null || value.trim().isEmpty) return postalCodeIsRequired;
    final zipPattern = RegExUtils.zipCodeRegex;
    if (!zipPattern.hasMatch(value.trim())) {
      return 'Invalid zip code format. \neg: 72546-4567 or 72546';
    }
    return null;
  }

  static String? validateReferralCode(
    String? value, {
    String fieldLabel = 'Referral Code',
    int limit = 8, // max limit
  }) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmed = value.trim();

    // Normalize: count only alphanumeric characters (exclude hyphen)
    final onlyAlnum = trimmed.replaceAll('-', '');

    // Check max length (alphanumeric only)
    if (onlyAlnum.length > limit) {
      return '$fieldLabel cannot exceed $limit characters';
    }

    // Require exact format: XXXX-XXXX (4 + hyphen + 4)
    final regExp = RegExp(r'^[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$');
    if (!regExp.hasMatch(trimmed)) {
      return '$fieldLabel must follow the format XXXX-XXXX';
    }

    return null; // valid
  }

  static String? validateTextLengthRange(
    String? value, {
    String fieldLabel = 'Name',
    int limit = 50,
  }) {
    if (value == null || value.trim().isEmpty) return '$fieldLabel is required';
    final trimmed = value.trim();
    if (trimmed.length < 1 || trimmed.length > limit) {
      return '$fieldLabel \nlength must be 1-$limit';
    }
    return null;
  }

  static String? validateCompanyName(
    String? value, {
    String fieldLabel = 'Company Name',
    int limit = 100,
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldLabel is required';
    }

    final trimmed = value.trim();

    // Length check
    if (trimmed.length < 1 || trimmed.length > limit) {
      return '$fieldLabel length must be 1-$limit characters';
    }

    // Allow only alphabets, spaces, periods, and hyphens
    final allowedPattern = RegExp(r'^[A-Za-z.\-\s]+$');

    if (!allowedPattern.hasMatch(trimmed)) {
      return '$fieldLabel can only contain alphabets, spaces, periods (.), and hyphens (-)';
    }

    return null;
  }

  static String? validateCommissionSplit(String? value) {
    if (value == null || value.trim().isEmpty) {
      return invalidCommissionSplitFormat;
    }

    final text = value.trim();

    // Try parsing to number
    final numValue = double.tryParse(text);
    if (numValue == null) {
      return invalidCommissionSplitFormat;
    }

    // Check range
    if (numValue < 0 || numValue > 100) {
      return invalidCommissionSplitFormat;
    }

    // Check decimal precision (max 2 digits)
    if (text.contains('.')) {
      final decimals = text.split('.')[1];
      if (decimals.length > 2) {
        return invalidCommissionSplitDecimalPlaces;
      }
    }

    return null;
  }

  static String? validateCurrency(
    String? value, {
    bool allowDecimal = true, // toggle decimal check
    String? fieldName,
    bool? allowZeroValue = true,
  }) {
    if (value == null || value.trim().isEmpty) {
      return (fieldName == null || fieldName.isEmpty)
          ? thisFieldIsRequired
          : fieldName + requiredFieldLabel;
    }
    final text = value.trim();

    // Try parsing to number
    final numValue = double.tryParse(text);
    if (numValue == null) {
      return invalidCurrencyFormat;
    }
    if (allowZeroValue == false && numValue == 0) {
      return (fieldName == null || fieldName.isEmpty)
          ? "Value cannot be zero"
          : "$fieldName cannot be zero";
    }
    if (!allowDecimal) {
      // Reject if contains decimal
      if (text.contains('.')) {
        return decimalsNotAllowed;
      }
    } else {
      // If decimal is allowed, max 2 digits after decimal
      if (text.contains('.')) {
        final decimals = text.split('.')[1];
        if (decimals.length > 2) {
          return maximum2DecimalPlacesAllowed;
        }
      }
    }

    return null;
  }

  static String? validateName(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (value.trim().isEmpty) {
      return '$fieldName cannot be only whitespace';
    }

    // Check if starts with space
    if (value.startsWith(' ')) {
      return '$fieldName cannot start with a space';
    }

    // Check for consecutive spaces (more than one space in a row)
    if (value.contains(RegExp(r'\s{2,}'))) {
      return '$fieldName cannot contain consecutive spaces';
    }

    // if (value.length >= 50) {
    //   return '$fieldName must not exceed 50 characters';
    // }

    // Regex pattern: allows letters (Latin & Unicode), hyphen, dot, apostrophe, and spaces
    final nameRegex = RegExp(r"^[\p{L}\s.'\-]+$", unicode: true);

    if (!nameRegex.hasMatch(value)) {
      return '$fieldName can only contain letters, spaces, hyphen (-), dot (.), and apostrophe (\')';
    }

    return null;
  }
}
