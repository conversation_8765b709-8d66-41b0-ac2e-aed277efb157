import '../../core/utils/helper.dart';

class CommissionRevenueInfoModel {
  double revenueShare;
  String transactionId;
  double grossCommission;
  double personalCommission;
  double commissionPercentage;

  CommissionRevenueInfoModel({
    required this.revenueShare,
    required this.transactionId,
    required this.grossCommission,
    required this.personalCommission,
    required this.commissionPercentage,
  });

  factory CommissionRevenueInfoModel.fromJson(Map<String, dynamic> json) {
    // Safely parse numeric values that may be int or double or string

    return CommissionRevenueInfoModel(
      revenueShare: toDouble(json['revenueShare']),
      transactionId: json['transactionId']?.toString() ?? '',
      grossCommission: toDouble(json['grossCommission']),
      personalCommission: toDouble(json['personalCommission']),
      commissionPercentage: toDouble(json['commissionPercentage']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'revenueShare': revenueShare,
      'transactionId': transactionId,
      'grossCommission': grossCommission,
      'personalCommission': personalCommission,
      'commissionPercentage': commissionPercentage,
    };
  }

  CommissionRevenueInfoModel copyWith({
    double? revenueShare,
    String? transactionId,
    double? grossCommission,
    double? personalCommission,
    double? commissionPercentage,
  }) {
    return CommissionRevenueInfoModel(
      revenueShare: revenueShare ?? this.revenueShare,
      transactionId: transactionId ?? this.transactionId,
      grossCommission: grossCommission ?? this.grossCommission,
      personalCommission: personalCommission ?? this.personalCommission,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
    );
  }

  static List<CommissionRevenueInfoModel> listFromJson(List<dynamic>? list) {
    if (list == null) return [];
    return list
        .map(
          (e) => CommissionRevenueInfoModel.fromJson(e as Map<String, dynamic>),
        )
        .toList();
  }
}
