import 'package:flutter/material.dart';
import '/src/core/config/constants.dart';

import '../theme/app_theme.dart';

enum SnackBarType { success, error, warning, info }

class AppSnackBar {
  const AppSnackBar._();

  static Future<void> showSnackBar(
    BuildContext context,
    String message,
    SnackBarType type, {
    Duration? timeout = const Duration(seconds: 4),
    bool showCloseButton = false,
    bool isTimerNeeded = true,
  }) async {
    if (!context.mounted) return;

    final messenger = ScaffoldMessenger.of(context);
    // Remove any existing snackbars (including queued) so new one shows cleanly.
    messenger.clearSnackBars();

    // Decide whether to show the close button:
    // - If caller explicitly requested (showCloseButton == true) -> show it
    // - Otherwise, show by default only for error and warning
    // - If timeoutFlag is false, close button should be shown (user must dismiss)
    final shouldShowClose = (showCloseButton || !isTimerNeeded);

    // Calculate effective duration:
    // - If timeoutFlag is false: use very long duration (user must dismiss manually)
    // - If timeoutFlag is true: use provided timeout or default 2 seconds
    final Duration effectiveDuration = isTimerNeeded
        ? (timeout ?? const Duration(seconds: 4))
        : const Duration(days: 1);
    final snackBar = SnackBar(
      content: Row(
        children: [
          Expanded(child: Text(message)),
          if (shouldShowClose)...[
            SizedBox(width: defaultPadding),
            IconButton(
              tooltip: 'Close',
              icon: const Icon(Icons.close, color: Colors.white, size: 20),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              onPressed: () {
                // User explicitly closes -> remove current and clear any queued snacks
                messenger.clearSnackBars();
              },
            ),]
        ],
      ),
      backgroundColor: _getBackgroundColor(type),
      duration: effectiveDuration,
      behavior: SnackBarBehavior.fixed,
      dismissDirection: DismissDirection.down, // Allow swipe down to dismiss
    );

    // Show the snackbar
    messenger.showSnackBar(snackBar);
    return;
  }

  static Color _getBackgroundColor(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return AppTheme.successColor;
      case SnackBarType.error:
        return AppTheme.errorColor;
      case SnackBarType.warning:
        return AppTheme.warningColor;
      case SnackBarType.info:
        return Colors.blue;
    }
  }
}
