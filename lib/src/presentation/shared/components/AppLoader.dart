import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

/// Common Loader Indicator for the entire application
class AppLoader {
  static bool _isShowing = false;

  /// Show the loader
  static void show(BuildContext context, {String? message}) {
    if (_isShowing) return; // Prevent multiple dialogs
    _isShowing = true;

    final screenWidth = MediaQuery.of(context).size.width;
    final loaderWidth = screenWidth * 0.4 > 250 ? 250.0 : screenWidth * 0.4;

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: const Color.fromARGB(83, 0, 0, 0),
      builder: (_) => WillPopScope(
        onWillPop: () async => false, // Prevent back button dismiss
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            width: loaderWidth,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SpinKitFadingCircle(color: Colors.blue, size: 50.0),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Hide the loader
  static void hide(BuildContext context) {
    if (_isShowing) {
      _isShowing = false;
      Navigator.of(context, rootNavigator: true).pop();
    }
  }
}
