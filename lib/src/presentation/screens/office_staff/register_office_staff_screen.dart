import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../core/network/api_consts.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/format_phone_number.dart';
import '../../../core/utils/success_snack_bar.dart';
import '../../../core/utils/whitespace_formatter.dart';
import '../../../domain/models/role.dart';
import '../../cubit/office_staff/office_staff_cubit.dart';
import '../../shared/components/customDialogues/alert_dialogue.dart';
import '../../shared/components/phone_prefix_formatter.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '../../../core/config/app_strings.dart';
import '../../shared/components/app_textfield.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class RegisterOfficeStaffScreen extends HookWidget {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController additionalInfoController =
      TextEditingController();
  final TextEditingController referalCodeController = TextEditingController();
  // Controllers for invite field
  final TextEditingController firstNameControllerInvite =
      TextEditingController();
  final TextEditingController lastNameControllerInvite =
      TextEditingController();
  final TextEditingController phoneControllerInvite = TextEditingController();
  final TextEditingController emailControllerInvite = TextEditingController();

  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  final ValueNotifier<PlatformFile?> officeStaffLicenseFile = ValueNotifier(
    null,
  );
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);

  final ValueNotifier<List<Role>> roleList = ValueNotifier<List<Role>>([]);

  final selectedRole = ValueNotifier<Role?>(null);

  final nameInputFormatter = FilteringTextInputFormatter.allow(
    RegExp(r"[\p{L}\s.'\-]", unicode: true),
  );

  final ValueNotifier<bool> showAdditionalInfoHint = ValueNotifier(false);
  final ValueNotifier<bool> showFirstNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showLastNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showEmailHint = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    final officeStaffCubit = context.read<OfficeStaffCubit>();

    useEffect(() {
      _setupAdditionalInfoListener();
      return null;
    }, []);

    useEffect(() {
      // Async work for roles
      Future.microtask(() async {
        await officeStaffCubit.getRoleListNames();
        final state = officeStaffCubit.state;
        if (state is RoleLoaded) {
          roleList.value = state.filterOptions;
        }
      });
      return null;
    }, []);
    return _formWidget(context, size, isMobile);
  }

  void _setupAdditionalInfoListener() {
    additionalInfoController.addListener(() {
      showAdditionalInfoHint.value =
          additionalInfoController.text.length >=
          APIConsts.additionalInfoCharacterLimit;
    });
    firstNameController.addListener(() {
      showFirstNameHint.value =
          firstNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    lastNameController.addListener(() {
      showLastNameHint.value =
          lastNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    emailController.addListener(() {
      showEmailHint.value =
          emailController.text.length >= APIConsts.emailCharacterLimit;
    });
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return BlocConsumer<OfficeStaffCubit, OfficeStaffState>(
          listener: (context, state) {
            if (state is RoleLoaded) {
              // Update role list when roles are loaded
              roleList.value = state.filterOptions;
            }
          },
          builder: (context, state) {
            // Load roles when in staff registration mode
            if (roleList.value.isEmpty && state is! OfficeStaffLoading) {
              Future.microtask(() {
                context.read<OfficeStaffCubit>().getRoleListNames();
              });
            }

            return Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('../assets/images/register_bg.png'),
                  fit: BoxFit.cover,
                ),
              ),
              child: Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: isMobile ? double.infinity : 600,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _formHeader(context),
                          _formContent(isMobile, context),
                        ],
                      ),
                      if (state is OfficeStaffLoading)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppTheme.roundIconColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 1.5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding * 2,
          vertical: defaultPadding * 1.5,
        ),
        child: Center(
          child: Text(
            AppStrings.registerOfficeStaff,
            style: AppFonts.semiBoldTextStyle(26, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppStrings.staffInformation,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),

            SizedBox(height: defaultPadding * 1.5),

            _buildFormFields(isMobile),

            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required int characterLimit,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: showHint,
      builder: (context, show, _) {
        if (!show) return const SizedBox.shrink();
        return Padding(
          padding: const EdgeInsets.only(top: 4, left: 14),
          child: Text(
            'Max $characterLimit characters allowed',
            style: AppFonts.regularTextStyle(12, color: AppTheme.warningColor),
          ),
        );
      },
    );
  }

  Widget _buildEmailCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required TextEditingController controller,
    required int characterLimit,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: showHint,
      builder: (context, show, _) {
        if (!show) return const SizedBox.shrink();

        // Check if there's a validation error
        final emailText = controller.text;
        final hasValidationError =
            InputValidators.validateEmail(emailText) != null;

        // Don't show warning if validation error exists
        if (hasValidationError) return const SizedBox.shrink();

        return Padding(
          padding: const EdgeInsets.only(top: 4, left: 14),
          child: Text(
            'Max $characterLimit characters allowed',
            style: AppFonts.regularTextStyle(12, color: AppTheme.warningColor),
          ),
        );
      },
    );
  }

  Widget _buildFormFields(bool isMobile) {
    bool isRegisterTab = selectedIndex.value == 0;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormLabel(AppStrings.firstName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? firstNameController : firstNameControllerInvite,
            AppStrings.enterFirstName,
            AppStrings.firstName,
            isMandatory: true,
            validator: (value) =>
                InputValidators.validateName(value, AppStrings.firstName),
            inputFormatters: [
              nameInputFormatter,
              LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
            ],
          ),
          _buildCharacterLimitHint(
            showHint: showFirstNameHint,
            characterLimit: APIConsts.nameCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.lastName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? lastNameController : lastNameControllerInvite,
            AppStrings.enterLastName,
            AppStrings.lastName,
            isMandatory: true,
            validator: (value) =>
                InputValidators.validateName(value, AppStrings.lastName),
            inputFormatters: [
              nameInputFormatter,
              LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
            ],
          ),
          _buildCharacterLimitHint(
            showHint: showLastNameHint,
            characterLimit: APIConsts.nameCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          _buildEmailCharacterLimitHint(
            showHint: showEmailHint,
            controller: emailController,
            characterLimit: APIConsts.emailCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),
          if (isRegisterTab) ..._buildRegisterFields(true),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.firstName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      firstNameController,
                      AppStrings.enterFirstName,
                      AppStrings.firstName,
                      isMandatory: true,
                      validator: (value) => InputValidators.validateName(
                        value,
                        AppStrings.firstName,
                      ),
                      inputFormatters: [
                        nameInputFormatter,
                        LengthLimitingTextInputFormatter(
                          APIConsts.nameCharacterLimit,
                        ),
                      ],
                    ),
                    _buildCharacterLimitHint(
                      showHint: showFirstNameHint,
                      characterLimit: APIConsts.nameCharacterLimit,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.lastName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      lastNameController,
                      AppStrings.enterLastName,
                      AppStrings.lastName,
                      isMandatory: true,
                      validator: (value) => InputValidators.validateName(
                        value,
                        AppStrings.lastName,
                      ),
                      inputFormatters: [
                        nameInputFormatter,
                        LengthLimitingTextInputFormatter(
                          APIConsts.nameCharacterLimit,
                        ),
                      ],
                    ),
                    _buildCharacterLimitHint(
                      showHint: showLastNameHint,
                      characterLimit: APIConsts.nameCharacterLimit,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          _buildEmailCharacterLimitHint(
            showHint: showEmailHint,
            controller: emailController,
            characterLimit: APIConsts.emailCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(false),
        ],
      );
    }
  }

  List<Widget> _buildRegisterFields(bool isMobile) {
    if (isMobile) {
      return [
        _buildFormLabel(AppStrings.city),
        const SizedBox(height: 8),
        _buildTextFormField(
          cityController,
          AppStrings.enterCity,
          AppStrings.city,
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.city,
            limit: 100,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.stateProvince),
        const SizedBox(height: 8),
        _buildTextFormField(
          stateController,
          AppStrings.enterState,
          AppStrings.stateProvince,
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.stateProvince,
            limit: 100,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.postalZipCode),
        const SizedBox(height: 8),
        _buildTextFormField(
          postalCodeController,
          AppStrings.postalCodeEg,
          AppStrings.postalZipCode,
          validator: (value) => InputValidators.validateZipCode(value),
          isMandatory: true,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9-]')),
          ],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.country),
        const SizedBox(height: 8),
        _buildTextFormField(
          countryController,
          AppStrings.enterCountry,
          AppStrings.country,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            // Regex: only letters, spaces, hyphen, apostrophe
            final regex = RegExp(r"^[a-zA-Z\s'-]+$");
            if (!regex.hasMatch(value.trim())) {
              return AppStrings.enterValidCountryName;
            }
            // Extra length validation
            return InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.country,
              limit: 56,
            );
          },
          isMandatory: true,
          inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r'[0-9]'))],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentRole, isMandatory: true),
        const SizedBox(height: 8),
        _buildDropDown(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadStaffId, isMandatory: false),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(
          additionalInfoController,
          '',
          '',
          maxLines: 4,
          inputFormatters: [
            LengthLimitingTextInputFormatter(
              APIConsts.additionalInfoCharacterLimit,
            ),
          ],
        ),
        _buildCharacterLimitHint(
          showHint: showAdditionalInfoHint,
          characterLimit: APIConsts.additionalInfoCharacterLimit,
        ),
      ];
    } else {
      return [
        _textFormFieldRowWidget(
          AppStrings.city,
          cityController,
          AppStrings.stateProvince,
          stateController,
          validatorValueLeftController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.city,
                limit: 100,
              ),
          validatorValueRightController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.stateProvince,
                limit: 100,
              ),
        ),
        const SizedBox(height: defaultPadding),

        _textFormFieldRowWidget(
          AppStrings.postalZipCode,
          postalCodeController,
          AppStrings.country,
          countryController,
          validatorValueLeftController: InputValidators.validateZipCode,
          validatorValueRightController: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            // Regex: only letters, spaces, hyphen, apostrophe
            final regex = RegExp(r"^[a-zA-Z\s'-]+$");
            if (!regex.hasMatch(value.trim())) {
              return AppStrings.enterValidCountryName;
            }
            // Extra length validation
            return InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.country,
              limit: 56,
            );
          },
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentRole, isMandatory: true),
        const SizedBox(height: 8),
        _buildDropDown(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadStaffId, isMandatory: false),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(
          additionalInfoController,
          '',
          '',
          maxLines: 4,
          inputFormatters: [
            LengthLimitingTextInputFormatter(
              APIConsts.additionalInfoCharacterLimit,
            ),
          ],
        ),
        _buildCharacterLimitHint(
          showHint: showAdditionalInfoHint,
          characterLimit: APIConsts.additionalInfoCharacterLimit,
        ),
      ];
    }
  }

  Widget _buildDropDown() {
    return ValueListenableBuilder<List<Role>>(
      valueListenable: roleList,
      builder: (context, roles, child) {
        return DropdownButtonFormField<Role>(
          // Changed type to Role
          decoration: InputDecoration(
            hintText: AppStrings.selectRole,
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide(color: Colors.blue, width: 1.5),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide(color: Colors.red, width: 1.5),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30),
              borderSide: BorderSide(color: Colors.red, width: 1.5),
            ),
          ),
          icon: Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
          items: roles.map((role) {
            return DropdownMenuItem<Role>(
              // Changed type to Role
              value: role, // Pass the entire role object
              child: Text(
                role.value,
                style: TextStyle(
                  color: AppTheme.primaryTextColor,
                  fontSize: 14,
                ),
              ),
            );
          }).toList(),
          onChanged: (Role? roleData) {
            if (roleData != null) {
              selectedRole.value = roleData;
            }
          },
          validator: (value) {
            if (value == null) {
              return '${AppStrings.agentRole} is required'; // More consistent with other field validations
            }
            return null;
          },
        );
      },
    );
  }

  Column _buildUploadColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          officeStaffLicenseFile,
          APIConsts.allowedFileExtensions,
        ),
        _fileUploadTxt(),
      ],
    );
  }

  ValueListenableBuilder<bool> _fileUploadTxt() {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        if (hasError) {
          return Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 16.0),
            child: Text(
              AppStrings.pleaseUploadLicence,
              style: AppFonts.regularTextStyle(12, color: Colors.red),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController, {
    String? Function(String?)? validatorValueLeftController,
    String? Function(String?)? validatorValueRightController,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                leftController,
                (leftController == cityController)
                    ? AppStrings.enterCity
                    : (leftController == postalCodeController)
                    ? postalCodeEg
                    : '',
                // (leftController == postalCodeController) ? postalCodeEg : '',
                leftLabel,
                validator: validatorValueLeftController,
                isMandatory: true,
                inputFormatters: (leftController == postalCodeController)
                    ? [FilteringTextInputFormatter.allow(RegExp(r'[0-9-]'))]
                    : [],
              ),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                rightController,
                (rightController == stateController)
                    ? AppStrings.enterState
                    : (rightController == countryController)
                    ? AppStrings.enterCountry
                    : '',
                rightLabel,
                validator: validatorValueRightController,
                isMandatory: true,
                inputFormatters: (rightController == countryController)
                    ? [FilteringTextInputFormatter.deny(RegExp(r'[0-9]'))]
                    : [],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (isSmallMobile) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.register,
              backgroundColor: AppTheme.roundIconColor,
              foregroundColor: Colors.white,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () => _submitForm(context),
            ),
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.clear,
              backgroundColor: AppTheme.scaffoldBgColor,
              foregroundColor: AppTheme.primaryTextColor,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2.5,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () => _showClearDataAlert(context),
            ),
          ),
        ],
      );
    } else {
      // Keep horizontal layout for regular mobile, tablet, and desktop
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppButton(
            label: AppStrings.clear,
            backgroundColor: AppTheme.scaffoldBgColor,
            foregroundColor: AppTheme.primaryTextColor,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2.5,
              vertical: defaultPadding / 2,
            ),
            onPressed: () => _showClearDataAlert(context),
          ),
          const SizedBox(width: defaultPadding),
          AppButton(
            label: AppStrings.register,
            backgroundColor: AppTheme.roundIconColor,
            foregroundColor: Colors.white,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2,
              vertical: defaultPadding / 2,
            ),
            onPressed: () => _submitForm(context),
          ),
        ],
      );
    }
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText,
    String fieldLabel, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    String? prefixText,
  }) {
    // Create base formatters list
    List<TextInputFormatter> finalFormatters = [];

    if (controller != phoneController ||
        controller != emailController ||
        controller != postalCodeController) {
      finalFormatters.add(WhitespaceFormatter());
    }

    // Add any additional formatters passed as parameter
    if (inputFormatters != null) {
      finalFormatters.addAll(inputFormatters);
    }
    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      // validator: validator,
      validator: (value) {
        if (isMandatory) {
          if (value == null || value.isEmpty) {
            // Use fieldLabel instead of hintText
            return '$fieldLabel is required';
          } else if (value.trim().isEmpty) {
            return AppStrings.whiteSpaceValidation;
          }
        }
        if (validator != null) {
          return validator(value);
        }
        return null;
      },
      keyboardType: keyboardType,
      inputFormatters: finalFormatters,
      isMobile: false,
      prefixText: prefixText,
    );
  }

  Widget _emailTextFormField(TextEditingController controller) {
    return _buildTextFormField(
      controller,
      AppStrings.enterEmail,
      AppStrings.email,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
      inputFormatters: [
        LengthLimitingTextInputFormatter(APIConsts.emailCharacterLimit),
      ],
    );
  }

  Widget _phoneNumTextFormField(TextEditingController controller) {
    final String phonePrefix = '+1 ';
    // Prefill the controller with prefix if main phone and empty
    if (phoneController.text.isEmpty) {
      phoneController.text = phonePrefix;
      phoneController.selection = TextSelection.fromPosition(
        TextPosition(offset: phoneController.text.length),
      );
    }
    return _buildTextFormField(
      controller,
      AppStrings.enterPhone,
      AppStrings.phone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        PhonePrefixFormatter(phonePrefix, maxDigits: APIConsts.phoneCharacterLimit,
        ),
        LengthLimitingTextInputFormatter(
          phonePrefix.length + APIConsts.phoneCharacterLimit,
        ),
      ],
      validator: (value) {
        // strip prefix before validating
        final stripped = value?.replaceFirst(phonePrefix, '') ?? '';
        return InputValidators.validatePhone(stripped);
      },
      prefixText: null,
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: fileNotifier,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);

            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (file != null) {
              borderColor = Colors.green.shade200;
            } else if (hasError) {
              borderColor = Colors.red;
            }

            return buildDottedBorderContainerWithRadius(
              borderRadius: 25.0,
              borderColor: borderColor,
              child: Container(
                width: double.infinity,
                height: isSmallMobile ? 100 : 120,
                padding: EdgeInsets.all(
                  isSmallMobile ? defaultPadding / 2 : defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: file != null
                      ? Colors.green.shade50
                      : hasError
                      ? Colors.red.shade50
                      : AppTheme.docUploadBgColor,
                  borderRadius: BorderRadius.circular(25),
                  border: file != null
                      ? Border.all(color: Colors.green.shade200)
                      : hasError
                      ? Border.all(color: Colors.red.shade200)
                      : null,
                ),
                child: file != null
                    ? Stack(
                        children: [
                          Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: isSmallMobile ? 16 : 20,
                                ),
                                SizedBox(width: isSmallMobile ? 8 : 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        file.name,
                                        style: AppFonts.mediumTextStyle(
                                          isSmallMobile ? 12 : 14,
                                          color: Colors.green.shade700,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${(file.size / 1024).toStringAsFixed(1)} ${AppStrings.fileSizeKB}',
                                        style: AppFonts.regularTextStyle(
                                          isSmallMobile ? 10 : 12,
                                          color: Colors.green.shade600,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: () {
                                fileNotifier.value = null;
                                // Reset validation error when file is removed
                                showFileUploadError.value = false;
                              },
                              child: Icon(
                                Icons.close,
                                color: Colors.red,
                                size: isSmallMobile ? 16 : 20,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _showFilePickerOptions(
                              context,
                              fileNotifier,
                              allowedExtensions,
                            ),
                            icon: Image.asset(
                              '$iconAssetpath/upload.png',
                              height: isSmallMobile ? 14 : 16,
                              width: isSmallMobile ? 14 : 16,
                            ),
                            label: Text(
                              AppStrings.upload,
                              style: AppFonts.mediumTextStyle(
                                isSmallMobile ? 12 : 14,
                                color: AppTheme.black,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryTextColor,
                              elevation: 0,
                              padding: EdgeInsets.symmetric(
                                horizontal: isSmallMobile ? 8 : 12,
                                vertical: isSmallMobile ? 4 : 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: AppTheme.borderColor),
                              ),
                            ),
                          ),
                          SizedBox(height: isSmallMobile ? 4 : 8),
                          Text(
                            hintText,
                            textAlign: TextAlign.center,
                            style: AppFonts.mediumTextStyle(
                              isSmallMobile ? 10 : 12,
                              color: AppTheme.black,
                            ),
                          ),
                          Text(
                            formatText,
                            textAlign: TextAlign.center,
                            style: AppFonts.regularTextStyle(
                              isSmallMobile ? 9 : 12,
                              color: AppTheme.ternaryTextColor,
                            ),
                          ),
                        ],
                      ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToCaptureImage}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Extract extension
        final fileExtension = file.extension?.toLowerCase() ?? '';

        // ✅ Validate extension (on all platforms)
        if (!allowedExtensions.contains(fileExtension)) {
          AppSnackBar.showSnackBar(
            context,
            '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(", ")}',
            SnackBarType.error,
            showCloseButton: false,
            isTimerNeeded: true,
          );
          return;
        }

        // ✅ Validate file size (max 25 MB, same as your first function)
        if (file.size > maxFileSizeInBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileSizeExceeded,
            SnackBarType.error,
          );
          return;
        }

        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = file.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            AppSnackBar.showSnackBar(
              context,
              '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
              SnackBarType.error,
              showCloseButton: false,
              isTimerNeeded: true,
            );
            return;
          }
        }

        // Validate file based on platform
        if (kIsWeb) {
          if (file.bytes != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (file.path != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('${AppStrings.errorPickingFile}: $e');
      // Show user-friendly error message
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToOpenFilePicker}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Future<void> _submitForm(BuildContext context) async {
    final user = context.read<UserCubit>().state;
    final officeStaffCubit = context.read<OfficeStaffCubit>();

    //  Validate form
    if (!_formKey.currentState!.validate()) {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.pleaseFillRequiredFields,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return;
    }
    if (selectedRole.value == null) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.agentRole} is required',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return;
    }

    // Check license file
    // if (this.officeStaffCubit.value == null) {
    //   showFileUploadError.value = true;
    //   scaffoldMessenger.showSnackBar(
    //     const SnackBar(content: Text(AppStrings.pleaseUploadLicence)),
    //   );
    //   return;
    // }
    showFileUploadError.value = false;

    try {
      // Build payload
      final payload = _buildStaffPayload(user);

      // Register Staff
      await officeStaffCubit.registerOfficeStaff(payload);
      final state = officeStaffCubit.state;

      if (state is OfficeStaffCreated) {
        await _handleFileUpload(
          officeStaffCubit: officeStaffCubit,
          file: this.officeStaffLicenseFile.value,
          userId: state.userId,
          context: context,
        );
      } else if (state is OfficeStaffError) {
        AppSnackBar.showSnackBar(
          context,
          '${AppStrings.failedToCreateStaff}: ${state.message}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
        // Reset cubit state to initial to prevent loading state persistence
        officeStaffCubit.resetToInitial();
      }
    } catch (e) {
      // Handle any unexpected errors
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.unexpectedError}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      // Reset cubit state to initial to prevent loading state persistence
      officeStaffCubit.resetToInitial();
    }
  }

  Map<String, dynamic> _buildStaffPayload(UserState user) {
    final phoneNumberCorrected = PhoneUtils.normalizePhoneForApi(
      phoneController.text.trim(),
    );
    return {
      "recruiterId": user.user?.userId.trim(),
      "firstName": firstNameController.text.trim(),
      "lastName": lastNameController.text.trim(),
      "phone": phoneNumberCorrected,
      "email": emailController.text.trim(),
      "roleId": selectedRole.value?.id ?? "",
      "city": cityController.text.trim(),
      "state": stateController.text.trim(),
      "postalCode": postalCodeController.text.trim(),
      "country": countryController.text.trim(),
      "additionalInfo": additionalInfoController.text.trim(),
    };
  }

  Future<void> _handleFileUpload({
    required OfficeStaffCubit officeStaffCubit,
    required PlatformFile? file,
    required String? userId,
    required BuildContext context,
  }) async {
    if (file == null) {
      await updateOfficeStaffStatus(
        officeStaffCubit: officeStaffCubit,
        userId: userId,
        context: context,
      );
      return;
    }

    final isValidFile = kIsWeb ? file.bytes != null : file.path != null;
    if (!isValidFile) {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.invalidFile,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      // Reset cubit state to initial to prevent loading state persistence
      officeStaffCubit.resetToInitial();
      return;
    }

    try {
      final uploadFilePayload = {
        "userId": userId,
        "categoryType": APIConsts.agentCategoryType,
        "documentType": APIConsts.staffDocType,
        "file": file,
      };

      await officeStaffCubit.uploadOfficeStaffFile(uploadFilePayload);
      final uploadState = officeStaffCubit.state;

      if (uploadState is OfficeStaffFileUploaded) {
        await updateOfficeStaffStatus(
          officeStaffCubit: officeStaffCubit,
          userId: userId,
          context: context,
        );
      } else if (uploadState is OfficeStaffError) {
        AppSnackBar.showSnackBar(
          context,
          '${AppStrings.fileUploadFailed}: ${uploadState.message}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
        // Reset cubit state to initial to prevent loading state persistence
        officeStaffCubit.resetToInitial();
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.fileUploadFailed}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      // Reset cubit state to initial to prevent loading state persistence
      officeStaffCubit.resetToInitial();
    }
  }

  Future<void> updateOfficeStaffStatus({
    required OfficeStaffCubit officeStaffCubit,
    required String? userId,
    required BuildContext context,
  }) async {
    try {
      final statusPayload = {"id": userId, "uploadStatus": true};
      await officeStaffCubit.getOfficeStaffStatus(statusPayload);
      final statusState = officeStaffCubit.state;

      if (statusState is OfficeStaffStatusUpdated) {
        await SuccessSnackBar.showSnackBar(
          context,
          AppStrings.staffCreatedSuccessfully,
          SnackBarType.success,
        );

        _clearForm();
        // Reset cubit state to initial to prevent loading state persistence
        officeStaffCubit.resetToInitial();
      } else if (statusState is OfficeStaffStatusUpdateFailed) {
        AppSnackBar.showSnackBar(
          context,
          ' ${statusState.message}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
        _clearForm();
        // Reset cubit state to initial to prevent loading state persistence
        officeStaffCubit.resetToInitial();
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        ' ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      _clearForm();
      // Reset cubit state to initial to prevent loading state persistence
      officeStaffCubit.resetToInitial();
    }
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return showAlertDialogue(
          context,
          title: AppStrings.clearData,
          content: AppStrings.clearDataConfirmation,
          primaryColor: AppTheme.primaryBlueColor,
          positiveButtonText: AppStrings.ok,
          negativeButtonText: AppStrings.cancel,
          onPositivePressed: () {
            Navigator.of(context).pop(true);
            _clearForm();
            // Any other logic you want
          },
        );
      },
    );
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    additionalInfoController.clear();
    referalCodeController.clear();
    firstNameControllerInvite.clear();
    lastNameControllerInvite.clear();
    phoneControllerInvite.clear();
    emailControllerInvite.clear();
    officeStaffLicenseFile.value = null;
    selectedRole.value = null;
    showFileUploadError.value = false;
    showAdditionalInfoHint.value = false;
    showFirstNameHint.value = false;
    showLastNameHint.value = false;
    showEmailHint.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_formKey.currentState != null) {
        _formKey.currentState!.reset();
      }
    });
  }
}
