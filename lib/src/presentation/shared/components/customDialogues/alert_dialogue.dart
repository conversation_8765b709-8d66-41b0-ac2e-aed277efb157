import 'package:flutter/material.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/app_snack_bar.dart';
import '../elevated_button.dart';

AlertDialog showAlertDialogue(
  BuildContext context, {
  required String title,
  required String content,
  required String positiveButtonText,
  required String negativeButtonText,
  double? width,
  double titlePaddingRatio = 1,
  Color? primaryColor,
  SnackBarType alertType = SnackBarType.info,
  TextAlign? contentTextAlign,
  VoidCallback? onPositivePressed,
  VoidCallback? onNegativePressed,
}) {
  final Color effectivePrimaryColor = alertType == SnackBarType.error
      ? AppTheme.alertError
      : primaryColor ?? AppTheme.roundIconColor;

  return AlertDialog(
    backgroundColor: effectivePrimaryColor,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    clipBehavior: Clip.antiAlias,
    titlePadding: EdgeInsets.zero,
    contentPadding: EdgeInsets.zero,
    actionsPadding: EdgeInsets.zero,
    title: Container(
      width: width ?? 350,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding * titlePaddingRatio,
      ),
      decoration: BoxDecoration(
        color: effectivePrimaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Text(
        title,
        style: AppFonts.semiBoldTextStyle(
          18,
          color: alertType == SnackBarType.error
              ? AppTheme.alertErrorTitle
              : AppTheme.white,
        ),
      ),
    ),
    content: Container(
      width: MediaQuery.of(context).size.width * 0.080,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(defaultPadding * 1.25),
            child: Text(
              content,
              textAlign: contentTextAlign ?? TextAlign.center,
              style: AppFonts.regularTextStyle(14, color: Colors.black),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              bottom: defaultPadding * 1.5,
              top: 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (negativeButtonText.isNotEmpty) ...[
                  AppButton(
                    label: negativeButtonText,
                    backgroundColor: AppTheme.scaffoldBgColor,
                    foregroundColor: AppTheme.primaryTextColor,
                    borderRadius: 25,
                    useMinSize: true,
                    minWidth: 100,
                    minHeight: 45,
                    useMinHeightSize: true,
                    onPressed: () {
                      if (onNegativePressed != null) {
                        onNegativePressed();
                      } else {
                        Navigator.of(context).pop(false);
                      }
                    },
                  ),
                  const SizedBox(width: 12),
                ],
                if (positiveButtonText.isNotEmpty) ...[
                  AppButton(
                    label: positiveButtonText,
                    backgroundColor: alertType == SnackBarType.error
                        ? AppTheme.alertError
                        : AppTheme.roundIconColor,
                    foregroundColor: alertType == SnackBarType.error
                        ? AppTheme.alertErrorTitle
                        : AppTheme.white,
                    borderRadius: 25,
                    minWidth: 100,
                    minHeight: 45,
                    useMinSize: true,
                    useMinHeightSize: true,
                    onPressed: () async {
                      if (onPositivePressed != null) {
                        onPositivePressed();
                      } else {
                        Navigator.of(context).pop(true);
                      }
                    },
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
