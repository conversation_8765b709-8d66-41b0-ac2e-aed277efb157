import '../../core/utils/helper.dart';

class CommissionRevenueInfoModel {
  final double revenueShare;
  final String transactionId;
  final double grossCommission;
  final double personalCommission;
  final double commissionPercentage;
  final String notes;

  CommissionRevenueInfoModel({
    required this.revenueShare,
    required this.transactionId,
    required this.grossCommission,
    required this.personalCommission,
    required this.commissionPercentage,
    required this.notes,
  });

  factory CommissionRevenueInfoModel.fromJson(Map<String, dynamic> json) {
    return CommissionRevenueInfoModel(
      revenueShare: toDouble(json['revenueShare']),
      transactionId: json['transactionId']?.toString() ?? '',
      grossCommission: toDouble(json['grossCommission']),
      personalCommission: toDouble(json['personalCommission']),
      commissionPercentage: toDouble(json['commissionPercentage']),
      notes: json['notes']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'revenueShare': revenueShare,
      'transactionId': transactionId,
      'grossCommission': grossCommission,
      'personalCommission': personalCommission,
      'commissionPercentage': commissionPercentage,
      'notes': notes,
    };
  }

  CommissionRevenueInfoModel copyWith({
    double? revenueShare,
    String? transactionId,
    double? grossCommission,
    double? personalCommission,
    double? commissionPercentage,
    String? notes,
  }) {
    return CommissionRevenueInfoModel(
      revenueShare: revenueShare ?? this.revenueShare,
      transactionId: transactionId ?? this.transactionId,
      grossCommission: grossCommission ?? this.grossCommission,
      personalCommission: personalCommission ?? this.personalCommission,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
      notes: notes ?? this.notes,
    );
  }

  static List<CommissionRevenueInfoModel> listFromJson(List<dynamic>? list) {
    if (list == null) return [];
    return list
        .map(
          (e) => CommissionRevenueInfoModel.fromJson(e as Map<String, dynamic>),
        )
        .toList();
  }
}

class CommissionRevenueInfoResponse {
  final List<CommissionRevenueInfoModel> content;
  final Pageable pageable;
  final int totalElements;
  final int totalPages;
  final bool last;
  final int size;
  final int number;
  final Sort sort;
  final int numberOfElements;
  final bool first;
  final bool empty;

  CommissionRevenueInfoResponse({
    required this.content,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  factory CommissionRevenueInfoResponse.fromJson(Map<String, dynamic> json) {
    return CommissionRevenueInfoResponse(
      content:
          (json['content'] as List<dynamic>?)
              ?.map(
                (item) => CommissionRevenueInfoModel.fromJson(
                  item as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
      pageable: Pageable.fromJson(
        json['pageable'] as Map<String, dynamic>? ?? {},
      ),
      totalElements: toInt(json['totalElements']),
      totalPages: toInt(json['totalPages']),
      last: json['last'] as bool? ?? false,
      size: toInt(json['size']),
      number: toInt(json['number']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      numberOfElements: toInt(json['numberOfElements']),
      first: json['first'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}

class Pageable {
  final int pageNumber;
  final int pageSize;
  final Sort sort;
  final int offset;
  final bool paged;
  final bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) {
    return Pageable(
      pageNumber: toInt(json['pageNumber']),
      pageSize: toInt(json['pageSize']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      offset: toInt(json['offset']),
      paged: json['paged'] as bool? ?? false,
      unpaged: json['unpaged'] as bool? ?? false,
    );
  }
}

class Sort {
  final bool sorted;
  final bool unsorted;
  final bool empty;

  Sort({required this.sorted, required this.unsorted, required this.empty});

  factory Sort.fromJson(Map<String, dynamic> json) {
    return Sort(
      sorted: json['sorted'] as bool? ?? false,
      unsorted: json['unsorted'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}
