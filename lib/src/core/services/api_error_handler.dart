import 'package:dio/dio.dart';
import '../config/app_strings.dart';
import '../services/exceptions.dart';

class ApiErrorHandler {
  static Exception handleResponseError(int? statusCode, dynamic data) {
    final message = data?['message'];

    switch (statusCode) {
      case 400:
        return ApiException(
          message: message ?? badRequestError,
          statusCode: 400,
        );
      case 401:
        return InvalidCredentialsException(
          message: message ?? unauthorizedError,
          statusCode: 401,
        );
      case 403:
        return ApiException(
          message: message ?? forbiddenError,
          statusCode: 403,
        );
      case 404:
        return ApiException(message: message ?? notFoundError, statusCode: 404);
      case 408:
        return ApiException(
          message: message ?? requestTimeoutError,
          statusCode: 408,
        );
      case 409:
        return ApiException(message: message, statusCode: 409);
      case 422:
        return ApiException(
          message: "$validationFailedPrefix$message",
          statusCode: 422,
        );
      case 429:
        return ApiException(
          message: message ?? tooManyRequestsError,
          statusCode: 429,
        );
      case 500:
        return ApiException(
          message: message ?? internalServerErrorMessage,
          statusCode: 500,
        );
      case 502:
        return ApiException(
          message: message ?? connectionResetByPeer,
          statusCode: 502,
        );
      case 413:
        return ApiException(
          message: message ?? requestEntityTooLarge,
          statusCode: 413,
        );
      case 503:
        return ApiException(
          message: message ?? serviceUnavailableError,
          statusCode: 503,
        );
      case 504:
        return ApiException(
          message: message ?? gatewayTimeoutError,
          statusCode: 504,
        );
      default:
        return ApiException(
          message: message ?? unknownError,
          statusCode: statusCode ?? -1,
        );
    }
  }

  static Exception handleDioException(DioException e, String defaultError) {
    final statusCode = e.response?.statusCode;
    final finalMessage = (() {
      final data = e.response?.data;

      if (data is Map &&
          data['message'] is String &&
          data['message'].toString().trim().isNotEmpty) {
        return data['message'];
      }
      if (data is String && data.trim().isNotEmpty) {
        return data.trim();
      }
      if (e.error is String && e.error.toString().trim().isNotEmpty) {
        return e.error.toString();
      }
      return defaultError;
    })();

    return handleResponseError(statusCode, {'message': finalMessage});
  }
}
