import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/utils/phone_number_formatter.dart';
import '/src/core/utils/success_snack_bar.dart';
import '../../../core/utils/combined_dropdown_state.dart';
import '../../../core/utils/format_phone_number.dart' show PhoneUtils;
import '../../../core/utils/helper.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../cubit/filter/filter_cubit.dart';
import '../../../core/services/firebase_auth_service.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../shared/components/customDialogues/alert_dialogue.dart';
import '../../shared/components/phone_prefix_formatter.dart';
import '/src/core/utils/app_snack_bar.dart';
import '../../cubit/agent/agent_cubit.dart';
import '/src/core/navigation/web_router.dart';
import '/src/core/utils/regex.dart';
import 'dart:io';
import '../../../core/network/api_consts.dart';
import '../../../core/utils/whitespace_formatter.dart';
import '../../../domain/models/RegisteredAgent.dart';
import '/src/presentation/cubit/broker/broker_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '../../../core/config/app_strings.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/custom_dropdown_button2.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

enum SigninType { google, apple, none }

class AgentRegistrationScreen extends HookWidget {
  final String? inviteId;
  final bool isSignUp;
  final String? idToken;
  final SigninType signinType;

  AgentRegistrationScreen({
    super.key,
    this.inviteId,
    this.isSignUp = false,
    this.idToken,
    this.signinType = SigninType.none,
  });

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController agentLicenseIdController =
      TextEditingController();
  final TextEditingController additionalInfoController =
      TextEditingController();
  final TextEditingController referralCodeController = TextEditingController();

  // Controllers for invite field
  final TextEditingController firstNameControllerInvite =
      TextEditingController();
  final TextEditingController lastNameControllerInvite =
      TextEditingController();
  final TextEditingController phoneControllerInvite = TextEditingController();
  final TextEditingController emailControllerInvite = TextEditingController();
  final GlobalKey _cityFieldKey = GlobalKey();
  final GlobalKey _stateFieldKey = GlobalKey();
  final GlobalKey _countryFieldKey = GlobalKey();

  final ValueNotifier<List<TableFilter>> countryOptions = ValueNotifier([]);
  final ValueNotifier<List<TableFilter>> stateOptions = ValueNotifier([]);
  final ValueNotifier<List<TableFilter>> cityOptions = ValueNotifier([]);

  final ValueNotifier<TableFilter?> selectedCountry = ValueNotifier(null);
  final ValueNotifier<TableFilter?> selectedState = ValueNotifier(null);
  final ValueNotifier<TableFilter?> selectedCity = ValueNotifier(null);

  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  final ValueNotifier<PlatformFile?> agentLicenseFile = ValueNotifier(null);
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);
  final ValueNotifier<bool> hasUploadedFiles = ValueNotifier(false);
  String recruiterIdForSignup = '';

  ValueListenable<CombinedDropdownState> _countryDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));
  ValueListenable<CombinedDropdownState> _stateDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));
  ValueListenable<CombinedDropdownState> _cityDropdownState =
      ValueNotifier<CombinedDropdownState>(CombinedDropdownState([], null));
  final nameInputFormatter = FilteringTextInputFormatter.allow(
    RegExp(r"[\p{L}\s.'\-]", unicode: true),
  );

  final ValueNotifier<bool> showAdditionalInfoHint = ValueNotifier(false);
  final ValueNotifier<bool> showFirstNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showLastNameHint = ValueNotifier(false);
  final ValueNotifier<bool> showEmailHint = ValueNotifier(false);
  final ValueNotifier<bool> showAgentLicenseIdHint = ValueNotifier(false);

  void _initializeCombinedListenables() {
    _countryDropdownState = _createCombinedListenable(
      countryOptions,
      selectedCountry,
    );
    _stateDropdownState = _createCombinedListenable(
      stateOptions,
      selectedState,
    );
    _cityDropdownState = _createCombinedListenable(cityOptions, selectedCity);
  }

  ValueListenable<CombinedDropdownState> _createCombinedListenable(
    ValueListenable<List<TableFilter>> optionsNotifier,
    ValueListenable<TableFilter?> selectedNotifier,
  ) {
    final combined = ValueNotifier<CombinedDropdownState>(
      CombinedDropdownState(optionsNotifier.value, selectedNotifier.value),
    );

    void updateCombined() {
      combined.value = CombinedDropdownState(
        optionsNotifier.value,
        selectedNotifier.value,
      );
    }

    optionsNotifier.addListener(updateCombined);
    selectedNotifier.addListener(updateCombined);

    return combined;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    // form should be reset/cleared when reopened,
    // regardless of whether the form was previously filled and not submitted.
    // if want to persist the data remove the useEffect
    useEffect(() {
      _initializeCombinedListenables();
      _setupAdditionalInfoListener();
      _clearForm(context);
      _loadCountries(context);
      return null;
    }, []);

    useEffect(() {
      if (isSignUp && inviteId != null) {
        final agentCubit = context.read<AgentCubit>();
        Future.microtask(() async {
          if (inviteId != null) {
            await agentCubit.validateInviteLink(inviteId!);
          }
          AgentState agentState = agentCubit.state;
          if (agentState is InviteLinkValidated) {
            await agentCubit.getRegisteredAgentInfo(inviteId!);
            agentState = agentCubit.state;
            if (agentState is AgentRegisteredInfoError) {
              AppSnackBar.showSnackBar(
                context,
                agentState.message,
                SnackBarType.error,
              );
            }
          }
        });
      }
      return null;
    });

    return _formWidget(context, size, isMobile);
  }

  void _setupAdditionalInfoListener() {
    additionalInfoController.addListener(() {
      showAdditionalInfoHint.value =
          additionalInfoController.text.length >=
          APIConsts.additionalInfoCharacterLimit;
    });
    firstNameController.addListener(() {
      showFirstNameHint.value =
          firstNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    lastNameController.addListener(() {
      showLastNameHint.value =
          lastNameController.text.length >= APIConsts.nameCharacterLimit;
    });

    emailController.addListener(() {
      showEmailHint.value =
          emailController.text.length >= APIConsts.emailCharacterLimit;
    });

    agentLicenseIdController.addListener(() {
      showAgentLicenseIdHint.value =
          agentLicenseIdController.text.length >=
          APIConsts.agentLicenseIdCharacterLimit;
    });
  }

  _fillFormWithRegisteredAgentInfo(
    RegisteredAgent? agent,
    BuildContext context,
  ) async {
    firstNameController.text = agent?.firstName ?? '';
    lastNameController.text = agent?.lastName ?? '';
    emailController.text = agent?.email ?? '';
    phoneController.text = PhoneNumberFormatter.formatPhoneNumberFromApi(agent?.phone ?? '');
    cityController.text = agent?.city ?? '';
    stateController.text = agent?.state ?? '';
    postalCodeController.text = agent?.zipCode ?? '';
    countryController.text = agent?.country ?? '';
    agentLicenseIdController.text = agent?.licenseNumber ?? '';
    additionalInfoController.text = agent?.additionalInfo ?? '';
    referralCodeController.text = agent?.inviteCode ?? '';
    final uploadedFile = agent?.uploadedDocuments?.firstOrNull;
    if (uploadedFile != null) {
      agentLicenseFile.value = PlatformFile(
        name: uploadedFile.fileName ?? 'unknown',
        path: null, // server returns only metadata (no local path)
        size: uploadedFile.fileSize ?? 0,
      );
    }
    hasUploadedFiles.value = uploadedFile != null;
    recruiterIdForSignup = agent?.recruiterId ?? '';

    final countryId = agent?.country;
    if (countryId != null) {
      await _loadStates(context, countryId);
    }

    final stateId = agent?.state;
    if (stateId != null) {
      await _loadCities(context, stateId);
    }

    if (agent?.country != null) {
      final country = countryOptions.value.firstWhere(
        (element) => element.id == agent?.country,
        orElse: () => TableFilter(id: '', key: '', value: ''),
      );
      if (country.id.isNotEmpty) {
        selectedCountry.value = country;
      }
    }
    if (agent?.state != null) {
      final state = stateOptions.value.firstWhere(
        (element) => element.id == agent?.state,
        orElse: () => TableFilter(id: '', key: '', value: ''),
      );
      if (state.id.isNotEmpty) {
        selectedState.value = state;
      }
    }

    if (agent?.city != null) {
      final city = cityOptions.value.firstWhere(
        (element) => element.id == agent?.city,
        orElse: () => TableFilter(
          id: agent?.city ?? '',
          key: '',
          value: agent?.cityName ?? '',
        ),
      );
      if (city.id.isNotEmpty) {
        selectedCity.value = city;
      }
    }
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return BlocConsumer<AgentCubit, AgentState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is InviteLinkValidationFailed) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                child: Text(state.message, style: AppFonts.mediumTextStyle(16)),
              );
            } else if (state is AgentRegisteredInfoLoaded) {
              _fillFormWithRegisteredAgentInfo(state.registeredAgent, context);
              _initializeCombinedListenables();
            }
            return Container(
              decoration: BoxDecoration(color: AppTheme.scaffoldBgColor),
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: isMobile ? double.infinity : 600,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _formHeader(context),
                          _formContent(isMobile, context),
                        ],
                      ),
                    ),

                    state is AgentLoading
                        ? Positioned.fill(
                            child: Container(
                              color: Colors.transparent,
                              child: Center(child: CircularProgressIndicator()),
                            ),
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 1.5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: defaultPadding * 1.2),
          Text(
            isSignUp ? AppStrings.signupAgent : AppStrings.inviteRegisterAgent,
            style: AppFonts.semiBoldTextStyle(22, color: Colors.white),
          ),
          const SizedBox(height: defaultPadding * 1.2),
        ],
      ),
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppStrings.agentInformation,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 1.5),

            _buildFormFields(isMobile, context),

            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required int characterLimit,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: showHint,
      builder: (context, show, _) {
        if (!show) return const SizedBox.shrink();
        return Padding(
          padding: const EdgeInsets.only(top: 4, left: 14),
          child: Text(
            'Max $characterLimit characters allowed',
            style: AppFonts.regularTextStyle(12, color: AppTheme.warningColor),
          ),
        );
      },
    );
  }

  Widget _buildEmailCharacterLimitHint({
    required ValueNotifier<bool> showHint,
    required TextEditingController controller,
    required int characterLimit,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: showHint,
      builder: (context, show, _) {
        if (!show) return const SizedBox.shrink();

        // Check if there's a validation error
        final emailText = controller.text;
        final hasValidationError =
            InputValidators.validateEmail(emailText) != null;

        // Don't show warning if validation error exists
        if (hasValidationError) return const SizedBox.shrink();

        return Padding(
          padding: const EdgeInsets.only(top: 4, left: 14),
          child: Text(
            'Max $characterLimit characters allowed',
            style: AppFonts.regularTextStyle(12, color: AppTheme.warningColor),
          ),
        );
      },
    );
  }

  Widget _buildFormFields(bool isMobile, BuildContext context) {
    bool isRegisterTab = selectedIndex.value == 0;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormLabel(AppStrings.firstName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? firstNameController : firstNameControllerInvite,
            AppStrings.enterFirstName,
            AppStrings.firstName,
            isMandatory: true,
            validator: (value) =>
                InputValidators.validateName(value, AppStrings.firstName),
            inputFormatters: [
              nameInputFormatter,
              LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
            ],
          ),
          _buildCharacterLimitHint(
            showHint: showFirstNameHint,
            characterLimit: APIConsts.nameCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.lastName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? lastNameController : lastNameControllerInvite,
            AppStrings.enterLastName,
            AppStrings.lastName,
            isMandatory: true,
            validator: (value) =>
                InputValidators.validateName(value, AppStrings.lastName),
            inputFormatters: [
              nameInputFormatter,
              LengthLimitingTextInputFormatter(APIConsts.nameCharacterLimit),
            ],
          ),
          _buildCharacterLimitHint(
            showHint: showLastNameHint,
            characterLimit: APIConsts.nameCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          _buildEmailCharacterLimitHint(
            showHint: showEmailHint,
            controller: emailController,
            characterLimit: APIConsts.emailCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),
          if (isRegisterTab) ..._buildRegisterFields(true, context),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.firstName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      firstNameController,
                      AppStrings.enterFirstName,
                      AppStrings.firstName,
                      isMandatory: true,
                      validator: (value) => InputValidators.validateName(
                        value,
                        AppStrings.firstName,
                      ),
                      inputFormatters: [
                        nameInputFormatter,
                        LengthLimitingTextInputFormatter(
                          APIConsts.nameCharacterLimit,
                        ),
                      ],
                    ),
                    _buildCharacterLimitHint(
                      showHint: showFirstNameHint,
                      characterLimit: APIConsts.nameCharacterLimit,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.lastName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      lastNameController,
                      AppStrings.enterLastName,
                      AppStrings.lastName,
                      isMandatory: true,
                      validator: (value) => InputValidators.validateName(
                        value,
                        AppStrings.lastName,
                      ),
                      inputFormatters: [
                        nameInputFormatter,
                        LengthLimitingTextInputFormatter(
                          APIConsts.nameCharacterLimit,
                        ),
                      ],
                    ),
                    _buildCharacterLimitHint(
                      showHint: showLastNameHint,
                      characterLimit: APIConsts.nameCharacterLimit,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          _buildEmailCharacterLimitHint(
            showHint: showEmailHint,
            controller: emailController,
            characterLimit: APIConsts.emailCharacterLimit,
          ),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(false, context),
        ],
      );
    }
  }

  _countryListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _countryDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'country_empty'),
          hint: AppStrings.enterCountry,
          items: state.options,
          selectedValue: state.selected?.id,
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.country,
                countryController,
                selected,
              );
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            return null;
          },
          enableSearch: true,
          isRequired: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
        );
      },
    );
  }

  _stateListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _stateDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'state_empty'),
          hint: AppStrings.enterState,
          items: state.options,
          selectedValue: state.selected?.id,
          onBeforeOpen: () {
            if (selectedCountry.value == null) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.pleaseSelectCountryFirst,
                SnackBarType.warning,
              );
              return;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.stateProvince,
                stateController,
                selected,
              );
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.stateProvince} is required";
            }
            return null;
          },
          enableSearch: true,
          isRequired: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
        );
      },
    );
  }

  _cityListenableBuilder() {
    return ValueListenableBuilder<CombinedDropdownState>(
      valueListenable: _cityDropdownState,
      builder: (context, state, _) {
        return CustomDropdownButton2(
          // key: ValueKey(state.selected?.id ?? 'city_empty'),
          hint: AppStrings.enterCity,
          items: state.options,
          selectedItem: state.selected,
          selectedValue: state.selected?.id,
          onBeforeOpen: () {
            if (selectedState.value == null) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.pleaseSelectStateFirst,
                SnackBarType.warning,
              );
              return;
            }
          },
          onChanged: (value) async {
            if (value != null) {
              final selected = state.options.firstWhere(
                (item) => item.id == value,
              );
              await _onLocationSelected(
                context,
                AppStrings.city,
                cityController,
                selected,
              );
            }
          },
          validator: (value) {
            // final shouldValidateCity = inviteId != null
            //     ? selectedCity.value == null
            //     : value == null || value.trim().isEmpty;

            return selectedCity.value == null
                ? "${AppStrings.city} is required"
                : null;
          },

          enableSearch: true,
          isRequired: true,
          useApiSearch: true,
          hintTextColor: AppTheme.textFieldHint,
          itemTextColor: AppTheme.primaryTextColor,
          onSearchChanged: (searchValue) async {
            if (selectedState.value?.id != null) {
              // Only call API if search text has 2 or more characters
              await _loadCities(
                context,
                selectedState.value!.id!,
                searchText: searchValue,
              );
            }
          },
        );
      },
    );
  }

  Widget _buildLabelWithInfo(
    String label, {
    bool isMandatory = false,
    String? tooltipMessage,
  }) {
    return Row(
      children: [
        _buildFormLabel(label, isMandatory: isMandatory),
        if (tooltipMessage != null) ...[
          const SizedBox(width: 4),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: Tooltip(
              richMessage: WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: Container(
                  constraints: const BoxConstraints(
                    maxWidth: 250, // Control max width for wrapping
                  ),
                  child: Text(
                    tooltipMessage,
                    softWrap: true,
                    maxLines: 4,
                    style: AppFonts.regularTextStyle(13, color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              preferBelow: false,
              verticalOffset: 10,
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),
        ],
      ],
    );
  }

  List<Widget> _buildRegisterFields(bool isMobile, BuildContext context) {
    return isMobile ? _buildMobileLayout() : _buildDesktopLayout();
  }

  List<Widget> _buildMobileLayout() {
    return [
      _buildFormLabel(AppStrings.country, isMandatory: true),
      const SizedBox(height: 8),
      _countryListenableBuilder(),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
      const SizedBox(height: 8),
      _stateListenableBuilder(),
      const SizedBox(height: defaultPadding),

      _buildLabelWithInfo(
        AppStrings.city,
        isMandatory: true,
        tooltipMessage: AppStrings.cityTooltipRegForm,
      ),
      const SizedBox(height: 8),
      _cityListenableBuilder(),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
      const SizedBox(height: 8),
      _buildTextFormField(
        postalCodeController,
        AppStrings.postalCodeEg,
        AppStrings.postalZipCode,
        validator: (value) => InputValidators.validateZipCode(value),
        isMandatory: true,
        inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9-]'))],
      ),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
      const SizedBox(height: 8),
      _buildTextFormField(
        agentLicenseIdController,
        AppStrings.enterAgentLicenseId,
        AppStrings.agentLicenseId,
        isMandatory: true,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\-]')),
          LengthLimitingTextInputFormatter(
            APIConsts.agentLicenseIdCharacterLimit,
          ),
        ],
      ),
      _buildCharacterLimitHint(
        showHint: showAgentLicenseIdHint,
        characterLimit: 100,
      ),
      const SizedBox(height: defaultPadding),

      if (isSignUp) ...[
        _buildFormLabel(AppStrings.referralCode, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          referralCodeController,
          AppStrings.enterReferralCode,
          AppStrings.referralCode,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),
      ],

      _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: false),
      const SizedBox(height: 8),
      _buildUploadColumn(),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.additionalInformation),
      const SizedBox(height: 8),
      _buildTextFormField(
        additionalInfoController,
        '',
        '',
        maxLines: 4,
        inputFormatters: [
          LengthLimitingTextInputFormatter(
            APIConsts.additionalInfoCharacterLimit,
          ),
        ],
      ),
      _buildCharacterLimitHint(
        showHint: showAdditionalInfoHint,
        characterLimit: APIConsts.additionalInfoCharacterLimit,
      ),
    ];
  }

  List<Widget> _buildDesktopLayout() {
    return [
      // Country and State row
      _buildCountryStateRowDesktop(),
      const SizedBox(height: defaultPadding),

      // City and Postal Code row
      _buildCityPostalCodeRowDesktop(),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
      const SizedBox(height: 8),
      _buildTextFormField(
        agentLicenseIdController,
        AppStrings.enterAgentLicenseId,
        AppStrings.agentLicenseId,
        isMandatory: true,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\-]')),
          LengthLimitingTextInputFormatter(
            APIConsts.agentLicenseIdCharacterLimit,
          ),
        ],
      ),
      _buildCharacterLimitHint(
        showHint: showAgentLicenseIdHint,
        characterLimit: 100,
      ),
      const SizedBox(height: defaultPadding),

      if (isSignUp) ...[
        _buildFormLabel(AppStrings.referralCode, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          referralCodeController,
          AppStrings.enterReferralCode,
          AppStrings.referralCode,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),
      ],

      _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: false),
      const SizedBox(height: 8),
      _buildUploadColumn(),
      const SizedBox(height: defaultPadding),

      _buildFormLabel(AppStrings.additionalInformation),
      const SizedBox(height: 8),
      _buildTextFormField(
        additionalInfoController,
        '',
        '',
        maxLines: 4,
        inputFormatters: [
          LengthLimitingTextInputFormatter(
            APIConsts.additionalInfoCharacterLimit,
          ),
        ],
      ),
      _buildCharacterLimitHint(
        showHint: showAdditionalInfoHint,
        characterLimit: APIConsts.additionalInfoCharacterLimit,
      ),
    ];
  }

  Row _buildCountryStateRowDesktop() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.country, isMandatory: true),
              const SizedBox(height: 8),
              _countryListenableBuilder(),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
              const SizedBox(height: 8),
              _stateListenableBuilder(),
            ],
          ),
        ),
      ],
    );
  }

  Row _buildCityPostalCodeRowDesktop() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLabelWithInfo(
                AppStrings.city,
                isMandatory: true,
                tooltipMessage: AppStrings.cityTooltipRegForm,
              ),
              const SizedBox(height: 8),
              _cityListenableBuilder(),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                postalCodeController,
                AppStrings.postalCodeEg,
                AppStrings.postalZipCode,
                validator: (value) => InputValidators.validateZipCode(value),
                isMandatory: true,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9-]')),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Column _buildUploadColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          agentLicenseFile,
          APIConsts.allowedFileExtensions,
        ),
        _fileUploadTxt(),
      ],
    );
  }

  ValueListenableBuilder<bool> _fileUploadTxt() {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        if (hasError) {
          return Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 16.0),
            child: Text(
              AppStrings.pleaseUploadLicence,
              style: AppFonts.regularTextStyle(12, color: Colors.red),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> _loadCountries(BuildContext context) async {
    final filterCubit = context.read<FilterCubit>();
    await filterCubit.getCountiresFilterOptions();

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      countryOptions.value = state.filterOptions;
    }
  }

  Future<void> _loadStates(BuildContext context, String? countryId) async {
    final filterCubit = context.read<FilterCubit>();

    // Clear dependent fields only if country is different
    final isDifferentCountry = selectedCountry.value?.id != countryId;
    if (isDifferentCountry) {
      stateController.clear();
      cityController.clear();
      stateOptions.value = [];
      cityOptions.value = [];
      selectedState.value = null;
      selectedCity.value = null;
    }

    if (countryId != null) {
      await filterCubit.getStatesFilterOptions(selectedCountry: countryId);
    } else {
      // Get all states without country filter
      await filterCubit.getStatesFilterOptions();
    }

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      stateOptions.value = state.filterOptions;
    }
  }

  Future<void> _loadCities(
    BuildContext context,
    String stateId, {
    String? searchText,
  }) async {
    if (!context.mounted) {
      return;
    }
    final filterCubit = context.read<FilterCubit>();

    // Clear dependent field only if state is different
    final isDifferentState = selectedState.value?.id != stateId;
    if (isDifferentState && searchText == null) {
      cityController.clear();
      cityOptions.value = [];
      selectedCity.value = null;
    }

    await filterCubit.getCitiesFilterOptions(
      selectedState: stateId,
      searchString: searchText,
    );

    if (filterCubit.state is FilterLoaded) {
      final state = filterCubit.state as FilterLoaded;
      cityOptions.value = state.filterOptions;
    }
  }

  Future<void> _onLocationSelected(
    BuildContext context,
    String fieldLabel,
    TextEditingController controller,
    TableFilter selected,
  ) async {
    controller.text = selected.value;

    if (fieldLabel == AppStrings.country) {
      final oldCountryId = selectedCountry.value?.id;
      selectedCountry.value = selected;

      // Load states for the selected country
      await _loadStates(context, selected.id);

      // Check if current state belongs to this country
      if (selectedState.value != null) {
        final stateExists = stateOptions.value.any(
          (state) => state.id == selectedState.value?.id,
        );

        if (!stateExists) {
          // Current state doesn't belong to this country, clear state and city
          stateController.clear();
          selectedState.value = null;
          cityController.clear();
          cityOptions.value = [];
          selectedCity.value = null;
        } else if (selectedCity.value != null) {
          // State exists in this country, now check if city exists in the state
          // Reload cities to ensure we have the correct list
          await _loadCities(context, selectedState.value!.id!);
          final cityExists = cityOptions.value.any(
            (city) => city.id == selectedCity.value?.id,
          );

          if (!cityExists) {
            // City doesn't belong to this state, clear only city
            cityController.clear();
            selectedCity.value = null;
          }
        }
      }
    } else if (fieldLabel == AppStrings.stateProvince) {
      final oldStateId = selectedState.value?.id;
      selectedState.value = selected;

      // Load cities for the selected state
      await _loadCities(context, selected.id!);

      // If state changed, always clear city
      if (oldStateId != selected.id) {
        cityController.clear();
        selectedCity.value = null;
      } else {
        // State is same, check if current city belongs to this state
        if (selectedCity.value != null) {
          final cityExists = cityOptions.value.any(
            (city) => city.id == selectedCity.value?.id,
          );
          if (!cityExists) {
            cityController.clear();
            selectedCity.value = null;
          }
        }
      }
    } else if (fieldLabel == AppStrings.city) {
      selectedCity.value = selected;
    }
  }

  Widget _actionButtons(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (isSmallMobile) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: isSignUp
                  ? AppStrings.signupButton
                  : AppStrings.inviteRegisterAgent,
              backgroundColor: AppTheme.roundIconColor,
              foregroundColor: Colors.white,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () async => isSignUp
                  ? await _submitSignupForm(context)
                  : await _submitForm(context),
            ),
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.clear,
              backgroundColor: AppTheme.scaffoldBgColor,
              foregroundColor: AppTheme.primaryTextColor,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2.5,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () => _showClearDataAlert(context),
            ),
          ),
        ],
      );
    } else {
      // Keep horizontal layout for regular mobile, tablet, and desktop
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppButton(
            label: AppStrings.clear,
            backgroundColor: AppTheme.scaffoldBgColor,
            foregroundColor: AppTheme.primaryTextColor,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2.5,
              vertical: defaultPadding / 2,
            ),
            onPressed: () => _showClearDataAlert(context),
          ),
          const SizedBox(width: defaultPadding),
          AppButton(
            label: isSignUp
                ? AppStrings.signupButton
                : AppStrings.inviteRegisterAgent,
            backgroundColor: AppTheme.roundIconColor,
            foregroundColor: Colors.white,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2,
              vertical: defaultPadding / 2,
            ),
            onPressed: () async => isSignUp
                ? await _submitSignupForm(context)
                : await _submitForm(context),
          ),
        ],
      );
    }
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText,
    String fieldLabel, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    bool enable = true,
    String? prefixText,
  }) {
    // Create base formatters list
    List<TextInputFormatter> finalFormatters = [];
    if (controller != phoneController ||
        controller != emailController ||
        controller != postalCodeController) {
      finalFormatters.add(WhitespaceFormatter());
    }

    // Add any additional formatters passed as parameter
    if (inputFormatters != null) {
      finalFormatters.addAll(inputFormatters);
    }

    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      // validator: validator,
      validator: (value) {
        if (isMandatory) {
          if (value == null || value.isEmpty) {
            // Use fieldLabel instead of hintText
            return '$fieldLabel is required';
          } else if (value.trim().isEmpty) {
            return AppStrings.whiteSpaceValidation;
          }
        }
        if (validator != null) {
          return validator(value);
        }
        return null;
      },
      keyboardType: keyboardType,
      inputFormatters: finalFormatters,
      isMobile: false,
      enable: enable,
      prefixText: prefixText,
    );
  }

  Widget _emailTextFormField(TextEditingController controller) {
    bool isEditable = (inviteId == null);

    return _buildTextFormField(
      controller,
      AppStrings.enterEmail,
      AppStrings.email,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
      enable: isEditable,
      inputFormatters: [
        LengthLimitingTextInputFormatter(APIConsts.emailCharacterLimit),
      ],
    );
  }

  Widget _phoneNumTextFormField(TextEditingController controller) {
    final bool showPrefix = controller == phoneController;
    final String phonePrefix = '+1 ';
    // Prefill the controller with prefix if main phone and empty
    if (showPrefix && controller.text.isEmpty) {
      controller.text = phonePrefix;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length),
      );
    }
    return _buildTextFormField(
      controller,
      AppStrings.enterPhone,
      AppStrings.phone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        // keep digits-only but allow prefix at start by using a custom formatter if needed
        PhonePrefixFormatter(phonePrefix, maxDigits: APIConsts.phoneCharacterLimit),
        LengthLimitingTextInputFormatter(
          phonePrefix.length + APIConsts.phoneCharacterLimit,
        ),
      ],
      validator: (value) {
        // strip prefix before validating
        final stripped = value?.replaceFirst(phonePrefix, '') ?? '';
        return InputValidators.validatePhone(stripped);
      },
      prefixText: null,
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    // if (isSignUp && hasUploadedFiles.value) {
    //   return const SizedBox.shrink();
    // }

    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: fileNotifier,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);

            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (file != null) {
              borderColor = Colors.green.shade200;
            } else if (hasError) {
              borderColor = Colors.red;
            }

            return buildDottedBorderContainerWithRadius(
              borderRadius: 25.0,
              borderColor: borderColor,
              child: Container(
                width: double.infinity,
                height: isSmallMobile ? 100 : 120,
                padding: EdgeInsets.all(
                  isSmallMobile ? defaultPadding / 2 : defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: file != null
                      ? Colors.green.shade50
                      : hasError
                      ? Colors.red.shade50
                      : AppTheme.docUploadBgColor,
                  borderRadius: BorderRadius.circular(25),
                  border: file != null
                      ? Border.all(color: Colors.green.shade200)
                      : hasError
                      ? Border.all(color: Colors.red.shade200)
                      : null,
                ),
                child: file != null
                    ? _buildFileUploadedView(isSmallMobile, file, fileNotifier)
                    : _buildUploadFileView(
                        context,
                        fileNotifier,
                        allowedExtensions,
                        isSmallMobile,
                        hintText,
                        formatText,
                      ),
              ),
            );
          },
        );
      },
    );
  }

  Stack _buildFileUploadedView(
    bool isSmallMobile,
    PlatformFile file,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) {
    return Stack(
      children: [
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: isSmallMobile ? 16 : 20,
              ),
              SizedBox(width: isSmallMobile ? 8 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      file.name,
                      style: AppFonts.mediumTextStyle(
                        isSmallMobile ? 12 : 14,
                        color: Colors.green.shade700,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      formatFileSize(file.size),
                      style: AppFonts.regularTextStyle(
                        isSmallMobile ? 10 : 12,
                        color: Colors.green.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              fileNotifier.value = null;
              // Reset validation error when file is removed
              showFileUploadError.value = false;
              hasUploadedFiles.value = false;
            },
            child: Icon(
              Icons.close,
              color: Colors.red,
              size: isSmallMobile ? 16 : 20,
            ),
          ),
        ),
      ],
    );
  }

  Column _buildUploadFileView(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
    bool isSmallMobile,
    String hintText,
    String formatText,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: () =>
              _showFilePickerOptions(context, fileNotifier, allowedExtensions),
          icon: Image.asset(
            '$iconAssetpath/upload.png',
            height: isSmallMobile ? 14 : 16,
            width: isSmallMobile ? 14 : 16,
          ),
          label: Text(
            AppStrings.upload,
            style: AppFonts.mediumTextStyle(
              isSmallMobile ? 12 : 14,
              color: AppTheme.black,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: AppTheme.primaryTextColor,
            elevation: 0,
            padding: EdgeInsets.symmetric(
              horizontal: isSmallMobile ? 8 : 12,
              vertical: isSmallMobile ? 4 : 8,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: AppTheme.borderColor),
            ),
          ),
        ),
        SizedBox(height: isSmallMobile ? 4 : 8),
        Text(
          hintText,
          textAlign: TextAlign.center,
          style: AppFonts.mediumTextStyle(
            isSmallMobile ? 10 : 12,
            color: AppTheme.black,
          ),
        ),
        Text(
          formatText,
          textAlign: TextAlign.center,
          style: AppFonts.regularTextStyle(
            isSmallMobile ? 9 : 12,
            color: AppTheme.ternaryTextColor,
          ),
        ),
      ],
    );
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToCaptureImage}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          debugPrint('Custom file type failed, trying FileType.any: $e');
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      debugPrint('File picker result: ${result?.files.length ?? 0} files');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final bool isEmptyFile = kIsWeb
            ? (file.bytes == null || file.bytes!.isEmpty)
            : (file.size == 0);
        if (isEmptyFile) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.selectedFileIsEmpty,
            SnackBarType.error,
            showCloseButton: false,
            isTimerNeeded: true,
          );
          return;
        }
        // Strict extension validation
        final extension = file.extension?.toLowerCase();
        if (extension == null || !allowedExtensions.contains(extension)) {
          AppSnackBar.showSnackBar(
            context,
            '${AppStrings.invalidFileType}${allowedExtensions.join(", ")}',
            SnackBarType.error,
            showCloseButton: false,
            isTimerNeeded: true,
          );
          return;
        }

        // File size validation
        if (file.size > maxFileSizeInBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileSizeExceeded,
            SnackBarType.error,
            showCloseButton: false,
          );
          return;
        }
        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = file.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            AppSnackBar.showSnackBar(
              context,
              '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
              SnackBarType.error,
              showCloseButton: false,
              isTimerNeeded: true,
            );
            return;
          }
        }
        // PDF integrity check: detect corrupted PDFs by validating header/footer
        try {
          if (file.extension?.toLowerCase() == 'pdf') {
            final isCorrupted = await isPdfCorrupted(file);
            if (isCorrupted) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.selectedPdfAppearsToBeCorruptedOrInvalid,
                SnackBarType.error,
                showCloseButton: false,
                isTimerNeeded: true,
              );
              return;
            }
          }
        } catch (e, st) {
          debugPrint('PDF validation error (non-fatal): $e\n$st');
          AppSnackBar.showSnackBar(
            context,
            AppStrings.couldNotFullyValidatePdf,
            SnackBarType.warning,
            showCloseButton: false,
            isTimerNeeded: true,
          );
        }
        // Validate file based on platform
        if (kIsWeb) {
          if (file.bytes != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (file.path != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('$errorPickingFile: $e');
      //TODO: snackbar using commin shared snackbar
      AppSnackBar.showSnackBar(
        context,
        '$failedToOpenFilePicker: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  /// Signup after clinking on link or
  /// normal signup from login screens
  _submitSignupForm(BuildContext context) async {
    if (!context.mounted) return;
    if (!_formKey.currentState!.validate()) {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.pleaseFillRequiredFields,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return;
    }
    final agentCubit = context.read<AgentCubit>();
    final user = context.read<UserCubit>().state;

    final payload = _buildAgentPayload(user);

    await agentCubit.signupAgent(payload);
    final state = agentCubit.state;

    if (state is AgentSignupSuccess) {
      bool isSuccess = false;

      if (agentLicenseFile.value == null ||
          agentLicenseFile.value!.bytes == null ||
          agentLicenseFile.value!.bytes!.isEmpty) {
        // No file to upload, just update status
        bool isStatusUpdated = await _statusUpdateApi(
          state.userId,
          agentCubit,
          context,
        );

        if (isStatusUpdated) {
          isSuccess = true;
          await SuccessSnackBar.showSnackBar(
            context,
            isSignUp
                ? agentRegisteredSuccessfully
                : AppStrings.agentCreatedSuccessfully,
            SnackBarType.success,
          );
        } else {
          await AppSnackBar.showSnackBar(
            context,
            agentRegisterStatusFailure,
            SnackBarType.error,
          );
        }
      } else {
        // File exists, upload it
        final uploadResult = await _handleFileUpload(
          context,
          agentCubit: agentCubit,
          file: agentLicenseFile.value,
          userId: state.userId,
        );

        if (uploadResult != null && uploadResult) {
          isSuccess = true;
        }
      }

      // Only navigate and clear form if everything succeeded
      if (isSuccess) {
        await _handleCreatePasswordNavigation(context, state.userId);
      }
    } else if (state is AgentSignupError) {
      AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  _handleCreatePasswordNavigation(BuildContext context, String? userId) async {
    if (idToken != null) {
      if (signinType == SigninType.google) {
        _handleGoogleSignIn(context);
      } else if (signinType == SigninType.apple) {
        _handleAppleSignIn(context);
      }
    } else {
      context.go(
        AppRoutes.createPassword.path,
        extra: {'email': emailController.text.trim(), 'userId': userId},
      );
    }
  }

  Future<void> _handleGoogleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();
    await authCubit.signInWithGoogle(idToken);
    final state = authCubit.state;
    if (state is AuthSuccess) {
      await _fetchUserInfo(context);
    } else if (state is AuthGoogleError) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  Future<void> _handleAppleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();
    await authCubit.signInWithApple(idToken);
    final state = authCubit.state;
    if (state is AuthSuccess) {
      await _fetchUserInfo(context);
    } else if (state is AuthAppleError) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  _fetchUserInfo(BuildContext context) async {
    final userCubit = context.read<UserCubit>();
    await userCubit.getUserProfile();
    final state = userCubit.state;
    if (state is UserLoaded) {
      // User loaded successfully, navigate to main layout
      debugPrint('User loaded successfully');
      if (context.mounted) {
        context.go(AppRoutes.mainLayout.path);
      }
    } else if (state is UserError) {
      debugPrint('Error loading user: ${state.message}');
      if (context.mounted) {
        AppSnackBar.showSnackBar(
          context,
          '${state.message} while fetching user info',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    }
  }

  /// Agent registration/invitation
  Future<void> _submitForm(BuildContext context) async {
    if (selectedIndex.value == 1) {
      //invite tab
      AppSnackBar.showSnackBar(
        context,
        'Under development',
        SnackBarType.warning,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return;
    }
    final user = context.read<UserCubit>().state;
    final agentCubit = context.read<AgentCubit>();

    // 1️⃣ Validate form
    if (!_formKey.currentState!.validate()) {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.pleaseFillRequiredFields,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return;
    }
    // TODO: Remove after regitration edit feature implemented at api side
    // 2️⃣ Check license file: not mandatory
    // if (agentLicenseFile.value == null) {
    //   showFileUploadError.value = true;
    //   scaffoldMessenger.showSnackBar(
    //     const SnackBar(content: Text(AppStrings.pleaseUploadLicence)),
    //   );
    //   return;
    // }
    // showFileUploadError.value = false;

    // 3️⃣ Build payload
    final payload = _buildAgentPayload(user);
    // 4️⃣ Register agent
    await agentCubit.resgiterAgent(payload);
    final state = agentCubit.state;

    if (state is AgentCreated) {
      await _handleFileUpload(
        context,
        agentCubit: agentCubit,
        file: agentLicenseFile.value,
        userId: state
            .userId, // status update api must use invite userId(from response) during invite
      );
    } else if (state is AgentLoaded) {
      SuccessSnackBar.showSnackBar(
        context,
        AppStrings.agentCreatedSuccessfully,
        SnackBarType.success,
      );
    } else if (state is AgentError) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToCreateAgent}: ${state.message}',
        SnackBarType.error,
      );
    }
  }

  Map<String, dynamic> _buildAgentPayload(UserState user) {
    final phoneNumberCorrected = PhoneNumberFormatter.formatPhoneNumberFromApi(
      phoneController.text.trim(),
    );
    Map<String, dynamic> payload = {
      "recruiterId": isSignUp ? recruiterIdForSignup : user.user?.userId.trim(),
      "firstName": firstNameController.text.trim(),
      "lastName": lastNameController.text.trim(),
      "email": emailController.text.trim(),
      "phone": phoneNumberCorrected,
      "cityId": selectedCity.value?.id != null
          ? int.tryParse(selectedCity.value!.id)
          : null,
      "stateId": selectedState.value?.id != null
          ? int.tryParse(selectedState.value!.id)
          : null,
      "postalCode": postalCodeController.text.trim(),
      "countryId": selectedCountry.value?.id != null
          ? int.tryParse(selectedCountry.value!.id)
          : null,
      "agentLicenseId": agentLicenseIdController.text.trim(),
      "additionalInfo": additionalInfoController.text.trim(),
    };

    if (isSignUp) {
      payload.addAll({
        "inviteCode": referralCodeController.text.trim(),
        "inviteId": inviteId,
      });
      payload.remove("recruiterId");
    }
    return payload;
  }

  Future<bool?> _handleFileUpload(
    BuildContext context, {
    required AgentCubit agentCubit,
    required PlatformFile? file,
    required String? userId,
  }) async {
    if (file == null) {
      bool isStatusUpdated = await _statusUpdateApi(
        userId,
        agentCubit,
        context,
      );
      if (isStatusUpdated) {
        await SuccessSnackBar.showSnackBar(
          context,
          isSignUp
              ? agentRegisteredSuccessfully
              : AppStrings.agentCreatedSuccessfully,
          SnackBarType.success,
        );
        if (!isSignUp) {
          _clearForm(context);
        }
        return true;
      } else {
        await AppSnackBar.showSnackBar(
          context,
          agentRegisterStatusFailure,
          SnackBarType.error,
        );
      }

      return false;
    }

    final isValidFile = kIsWeb ? file.bytes != null : file.path != null;
    if (!isValidFile) {
      AppSnackBar.showSnackBar(
        context,
        AppStrings.invalidFile,
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
      return false;
    }

    final uploadFilePayload = {
      "userId": isSignUp ? userId : '',
      "invitedUserId": isSignUp ? '' : userId,
      "categoryType": APIConsts.agentCategoryType,
      "documentType": APIConsts.agentDocType,
      "file": file,
    };

    await agentCubit.uploadAgentFile(uploadFilePayload);
    final uploadState = agentCubit.state;

    if (uploadState is AgentFileUploaded) {
      bool isStatusUpdated = await _statusUpdateApi(
        userId,
        agentCubit,
        context,
      );
      if (isStatusUpdated) {
        await SuccessSnackBar.showSnackBar(
          context,
          isSignUp
              ? agentRegisteredSuccessfully
              : AppStrings.agentCreatedSuccessfully,
          SnackBarType.success,
        );

        if (!isSignUp) {
          _clearForm(context);
        }
        return true;
      } else {
        await AppSnackBar.showSnackBar(
          context,
          agentRegisterStatusFailure,
          SnackBarType.error,
        );
      }
      return isStatusUpdated;
    } else if (uploadState is AgentError) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.fileUploadFailed}: ${uploadState.message}',
        SnackBarType.error,
      );
      return false;
    }
    return false;
  }

  /// Invoke status update api when:
  /// Agent invited/registered without choosing files
  /// Agent invited/registered by selecting files(after successfull file upload)
  Future<bool> _statusUpdateApi(
    String? userId,
    AgentCubit agentCubit,
    BuildContext context,
  ) async {
    if (userId != null) {
      await agentCubit.handleAgentStatusUpdate(
        userId,
        true,
        isSignUp,
        isSignUp ? referralCodeController.text.trim() : null,
      );
      final uploadState = agentCubit.state;
      if (uploadState is AgentInviteStatusUpdated) {
        if (!isSignUp) {
          _clearForm(context);
        }
        return true;
      } else if (uploadState is AgentInviteStatusUpdateFailed) {
        AppSnackBar.showSnackBar(
          context,
          uploadState.message,
          SnackBarType.error,
        );
      }
    } else {
      await AppSnackBar.showSnackBar(
        context,
        failedToUploadAgentInviteStatusUserIDEmpty,
        SnackBarType.error,
      );
    }
    return false;
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return showAlertDialogue(
          context,
          title: AppStrings.clearData,
          content: AppStrings.clearDataConfirmation,
          primaryColor: AppTheme.primaryBlueColor,
          positiveButtonText: AppStrings.ok,
          negativeButtonText: AppStrings.cancel,
          onPositivePressed: () {
            Navigator.of(context).pop(true);
            _clearForm(context);
            // Any other logic you want
          },
        );
      },
    );
  }

  _clearForm(BuildContext context) {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    agentLicenseIdController.clear();
    additionalInfoController.clear();
    agentLicenseFile.value = null;
    referralCodeController.clear();
    selectedCountry.value = null;
    selectedState.value = null;
    selectedCity.value = null;
    stateOptions.value = [];
    cityOptions.value = [];
    showAdditionalInfoHint.value = false;
    showFirstNameHint.value = false;
    showLastNameHint.value = false;
    showEmailHint.value = false;
    showAgentLicenseIdHint.value = false;
    if (isSignUp && inviteId != null) {
      final agentCubit = context.read<AgentCubit>();
      final agentState = agentCubit.state;
      if (agentState is AgentRegisteredInfoLoaded) {
        _fillFormWithRegisteredAgentInfo(agentState.registeredAgent, context);
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_formKey.currentState != null) {
        _formKey.currentState!.reset();
      }
    });
  }
}
