# Force Logout Testing Guide

## Overview
This guide will help you test the force logout functionality that ensures users are always prompted to login on every app launch, even when running on the same port.

## Test Scenarios

### Test 1: Fresh App Launch (Primary Test)
**Purpose:** Verify that the app always shows login screen on startup

**Steps:**
1. Open the app at `http://localhost:8081`
2. **Expected Result:** Login screen should appear immediately
3. **Verify:** No automatic redirection to dashboard
4. **Verify:** Loading indicator appears briefly, then login screen

**What to Look For:**
- ✅ Login screen displays
- ✅ No dashboard content visible
- ✅ Username and password fields are empty
- ✅ No error messages

### Test 2: Login and Restart Cycle
**Purpose:** Verify force logout works after successful login

**Steps:**
1. Login with valid credentials
2. Navigate to dashboard (should work normally)
3. **Refresh the browser page (F5 or Ctrl+R)**
4. **Expected Result:** Should return to login screen (not dashboard)

**What to Look For:**
- ✅ After refresh, login screen appears
- ✅ User is not automatically logged in
- ✅ Must enter credentials again

### Test 3: Manual Logout Functionality
**Purpose:** Verify logout button works correctly

**Steps:**
1. Login with valid credentials
2. Navigate to dashboard
3. Click on profile dropdown in header (top right)
4. Click "Logout" option
5. **Expected Result:** Redirected to login screen

**What to Look For:**
- ✅ Logout button is visible in profile dropdown
- ✅ Clicking logout redirects to login screen
- ✅ Cannot navigate back to dashboard without logging in

### Test 4: Session Persistence Check
**Purpose:** Verify that session data is properly cleared

**Steps:**
1. Login with valid credentials
2. Open browser Developer Tools (F12)
3. Go to Application/Storage tab
4. Check Local Storage, Session Storage, and Cookies
5. **Refresh the page**
6. Check storage again after refresh

**What to Look For:**
- ✅ Auth tokens are cleared after refresh
- ✅ User data is not persisted
- ✅ No authentication-related data remains

### Test 5: Multiple Tab Test
**Purpose:** Verify behavior across multiple browser tabs

**Steps:**
1. Open app in first tab and login
2. Open app in second tab (`http://localhost:8081`)
3. **Expected Result:** Second tab should show login screen
4. Login in second tab
5. Refresh first tab
6. **Expected Result:** First tab should show login screen

**What to Look For:**
- ✅ Each tab requires independent login
- ✅ Login in one tab doesn't affect others
- ✅ Refresh always shows login screen

## Browser Developer Tools Verification

### Check Network Requests
1. Open Developer Tools (F12)
2. Go to Network tab
3. Refresh the page
4. **Look for:** No automatic API calls to fetch user data
5. **Look for:** Session clearing operations

### Check Console Output
1. Open Developer Tools (F12)
2. Go to Console tab
3. Refresh the page
4. **Look for:** No authentication errors
5. **Look for:** Proper state management logs

### Check Application Storage
1. Open Developer Tools (F12)
2. Go to Application tab (Chrome) or Storage tab (Firefox)
3. Check the following sections:
   - **Local Storage:** Should be cleared of auth tokens
   - **Session Storage:** Should be cleared
   - **Cookies:** Auth-related cookies should be cleared

## Expected Behavior Summary

| Action | Expected Result |
|--------|----------------|
| Fresh app launch | Login screen appears |
| Page refresh after login | Login screen appears (force logout) |
| Browser back button | Cannot bypass login |
| New tab/window | Login screen appears |
| Manual logout | Redirected to login screen |
| Direct dashboard URL | Redirected to login screen |

## Troubleshooting

### If Dashboard Appears on Refresh
**Problem:** Dashboard shows instead of login screen after refresh

**Check:**
1. Verify AuthWrapper implementation is correct
2. Check browser console for errors
3. Ensure force logout logic is executing
4. Check if session data is being cleared

### If Login Fails
**Problem:** Cannot login after implementing force logout

**Check:**
1. Verify login API endpoints are working
2. Check network requests in developer tools
3. Ensure AuthCubit login method is functioning
4. Check for any authentication errors

### If App Shows Loading Forever
**Problem:** App stuck on loading screen

**Check:**
1. Verify useEffect in AuthWrapper is completing
2. Check for any async operation errors
3. Ensure isLoggedIn state is being set to false
4. Check browser console for JavaScript errors

## Success Criteria

The implementation is working correctly if:

✅ **Fresh Launch:** App always shows login screen on startup
✅ **Refresh Behavior:** Page refresh always returns to login screen
✅ **Session Clearing:** All authentication data is cleared on startup
✅ **Logout Function:** Manual logout works correctly
✅ **No Auto-Login:** No automatic redirection to dashboard
✅ **Consistent Behavior:** Same behavior across browser tabs/windows

## Development Benefits

This implementation provides:

1. **Consistent Testing Environment:** Every app launch starts fresh
2. **Security:** No persistent sessions that could be exploited
3. **User Control:** Users must actively authenticate each session
4. **Development Friendly:** Easy to test different user scenarios
5. **Clean State:** No leftover authentication data between sessions

## Notes for Developers

- The force logout happens in `AuthWrapper` component
- Session data is cleared using `SessionManager.clearSession()`
- User data is cleared using `UserCubit.clearUser()`
- Authentication state is reset using `AuthCubit.forceLogoutOnStartup()`
- The implementation is safe and handles errors gracefully

## Next Steps

After verifying the tests above:

1. **Confirm all tests pass** ✅
2. **Test with different user accounts** ✅
3. **Test on different browsers** (Chrome, Firefox, Safari)
4. **Test on different devices** (Desktop, Mobile)
5. **Deploy to staging environment** for further testing

This force logout implementation ensures a secure and consistent authentication experience for all users.
