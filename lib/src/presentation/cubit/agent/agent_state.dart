part of 'agent_cubit.dart';

@immutable
sealed class AgentState {}

final class <PERSON>Initial extends AgentState {}

final class AgentLoading extends AgentState {}

final class AgentLoaded extends AgentState {
  final List<AgentModel> agents;
  final int totalCount;
  final int totalPages;
  final int currentPage;
  final bool hasMore;

  AgentLoaded({
    required this.agents,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    this.hasMore = false,
  });
}

final class AgentError extends AgentState {
  final String message;
  final int? statusCode;

  AgentError({required this.message, this.statusCode});
}

final class AgentCreated extends AgentState {
  final String? userId;
  final Map<String, dynamic>? responseData;

  AgentCreated({this.userId, this.responseData});
}

final class AgentFileUploaded extends AgentState {}

final class AgentInviteStatusUpdated extends AgentState {}

final class AgentInviteStatusUpdateFailed extends AgentState {
  final String message;

  AgentInviteStatusUpdateFailed({required this.message});
}

final class AgentRegisteredInfoLoaded extends AgentState {
  final RegisteredAgent? registeredAgent;

  AgentRegisteredInfoLoaded({required this.registeredAgent});
}

final class AgentRegisteredInfoError extends AgentState {
  final String message;
  final int? statusCode;

  AgentRegisteredInfoError({required this.message, this.statusCode});
}

final class AgentSignupSuccess extends AgentState {
  final String? userId;
  AgentSignupSuccess({this.userId});
}

final class AgentSignupError extends AgentState {
  final String error;
  AgentSignupError({required this.error});
}

final class InviteLinkValidated extends AgentState {
  final String message;
  InviteLinkValidated({required this.message});
}

final class InviteLinkValidationFailed extends AgentState {
  final String message;
  InviteLinkValidationFailed({required this.message});
}
