import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../domain/models/upload_task_model.dart';
import '../../domain/repository/sales_details_repository.dart';
import '../../presentation/cubit/upload/upload_cubit.dart';
import '../network/api_consts.dart';
import 'exceptions.dart';

/// Singleton service for managing background file uploads
/// This service persists across navigation and handles upload queue
class UploadService {
  static final UploadService _instance = UploadService._internal();
  factory UploadService() => _instance;
  UploadService._internal();

  final _uuid = const Uuid();
  UploadCubit? _uploadCubit;
  SalesDetailsRepository? _salesDetailsRepository;

  /// Initialize the service with required dependencies
  void initialize({
    required UploadCubit uploadCubit,
    required SalesDetailsRepository salesDetailsRepository,
  }) {
    _uploadCubit = uploadCubit;
    _salesDetailsRepository = salesDetailsRepository;
  }

  /// Queue a new upload task
  Future<String> queueUpload({
    required String userId,
    required String categoryType,
    required String representingId,
    required List<UploadFileInfo> files,
    String? originatingRoute,
    double? progress,
  }) async {
    if (_uploadCubit == null || _salesDetailsRepository == null) {
      throw Exception('UploadService not initialized');
    }

    // Create a new upload task
    final taskId = _uuid.v4();
    final task = UploadTask(
      taskId: taskId,
      userId: userId,
      categoryType: categoryType,
      representingId: representingId,
      files: files,
      cancelToken: CancelToken(),
      originatingRoute: originatingRoute,
      userNavigatedAway: false,
      progress: progress ?? 0.0,
    );

    // Add task to cubit
    _uploadCubit!.addTask(task);

    // Start upload immediately
    _startUpload(task);

    return taskId;
  }

  /// Start uploading a task
  Future<void> _startUpload(UploadTask task) async {
    try {
      // Mark task as uploading
      _uploadCubit!.markTaskAsUploading(task.taskId);

      // Prepare the first file (closing document)
      final closingDoc = task.files.firstWhere(
        (file) => file.id == 'closing_document',
        orElse: () => task.files.first,
      );

      // Update current file info
      _uploadCubit!.updateTaskProgress(
        taskId: task.taskId,
        progress: 0.0,
        currentFileName: closingDoc.file.name,
        currentFileIndex: 0,
      );

      // Upload closing document first
      final requestBody = {
        "userId": task.userId,
        "categoryType": task.categoryType,
        "documentType": APIConsts.salesDocType,
        "file": closingDoc.file,
        "representingId": task.representingId,
      };

      final response = await _salesDetailsRepository!
          .uploadSalesClosingDocumentFile(
            requestBody,
            cancelToken: task.cancelToken,
          );

      final salesId = response['data']['fileId'] as String?;

      if (salesId == null) {
        throw Exception('Failed to get sales ID from response');
      }

      // Update task with sales ID
      final updatedTask = task.copyWith(salesId: salesId);
      _uploadCubit!.updateTask(updatedTask);

      // Upload remaining files
      int uploadedCount = 1;
      final remainingFiles = task.files
          .where((file) => file.id != 'closing_document')
          .toList();

      for (final file in remainingFiles) {
        // Check if task was cancelled
        final currentTask = _uploadCubit!.getTask(task.taskId);
        if (currentTask?.isCancelled == true) {
          debugPrint('Upload cancelled for task: ${task.taskId}');
          return;
        }

        // Update progress
        _uploadCubit!.updateTaskProgress(
          taskId: task.taskId,
          progress: uploadedCount / task.totalFiles,
          currentFileName: file.file.name,
          currentFileIndex: uploadedCount,
        );

        // Upload file
        final payload = {
          "userId": task.userId,
          "categoryType": task.categoryType,
          "documentType": file.type,
          "file": file.file,
          "id": salesId,
          "representingId": task.representingId,
        };

        await _salesDetailsRepository!.uploadSalesClosingDocumentFile(
          payload,
          cancelToken: task.cancelToken,
        );

        uploadedCount++;
      }

      // Mark as processing
      _uploadCubit!.markTaskAsProcessing(task.taskId);

      // Update sales upload status
      final success = await handleSaleDocStatusUpdate(salesId);
      if (success) {
        // Simulate processing delay
        await Future.delayed(const Duration(seconds: 1));

        // Mark as completed
        _uploadCubit!.markTaskAsCompleted(task.taskId, salesId);

        debugPrint('Upload completed for task: ${task.taskId}');
      } else {
        _uploadCubit!.markTaskAsFailed(task.taskId, 'Upload failed');
        debugPrint('Upload completed for task: ${task.taskId}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.cancel) {
        debugPrint('Upload cancelled via DioException: ${e.message}');
        _uploadCubit!.markTaskAsCancelled(task.taskId);
      } else {
        debugPrint(
          'Upload failed for task: ${task.taskId}, error: ${e.message}',
        );
        _uploadCubit!.markTaskAsFailed(
          task.taskId,
          e.response?.data?['message'] ?? e.message ?? 'Upload failed',
        );
      }
    } catch (e) {
      debugPrint('Upload failed for task: ${task.taskId}, error: $e');
      _uploadCubit!.markTaskAsFailed(
        task.taskId,
        e.toString().isEmpty
            ? 'An unexpected error occurred: ${e.toString()}'
            : e.toString(),
      );
    }
  }

  /// Cancel an upload task
  void cancelUpload(String taskId) {
    final task = _uploadCubit?.getTask(taskId);
    if (task != null && task.canBeCancelled) {
      // Cancel the Dio request
      if (task.cancelToken != null && !task.cancelToken!.isCancelled) {
        task.cancelToken!.cancel('Upload cancelled by user');
      }
      // Mark as cancelled
      _uploadCubit!.markTaskAsCancelled(taskId);
    }
  }

  /// Retry a failed upload
  Future<void> retryUpload(String taskId) async {
    final task = _uploadCubit?.getTask(taskId);
    if (task != null && task.isFailed) {
      // Create a new task with the same data but new cancel token
      final newTask = task.copyWith(
        status: UploadTaskStatus.queued,
        progress: 0.0,
        currentFileIndex: 0,
        errorMessage: null,
        cancelToken: CancelToken(),
      );
      _uploadCubit!.updateTask(newTask);
      await _startUpload(newTask);
    }
  }

  /// Remove a completed or failed task
  void removeTask(String taskId) {
    final task = _uploadCubit?.getTask(taskId);
    if (task != null &&
        (task.isCompleted || task.isFailed || task.isCancelled)) {
      _uploadCubit!.removeTask(taskId);
    }
  }

  /// Clear all completed tasks
  void clearCompletedTasks() {
    _uploadCubit?.clearCompletedTasks();
  }

  /// Mark that user has navigated away from the originating route
  void markUserNavigatedAway(String taskId, String currentRoute) {
    final task = _uploadCubit?.getTask(taskId);
    if (task != null && task.originatingRoute != null) {
      // Check if current route is different from originating route
      if (currentRoute != task.originatingRoute) {
        final updatedTask = task.copyWith(userNavigatedAway: true);
        _uploadCubit!.updateTask(updatedTask);
      }
    }
  }

  /// Check if user has navigated away from upload screen
  bool hasUserNavigatedAway(String taskId) {
    final task = _uploadCubit?.getTask(taskId);
    return task?.userNavigatedAway ?? false;
  }

  Future<bool> handleSaleDocStatusUpdate(String saleId) async {
    if (_salesDetailsRepository == null) return false;
    try {
      final success = await _salesDetailsRepository!.handleSaleDocStatusUpdate(
        saleId,
      );
      return success;
    } on ApiException catch (e) {
      debugPrint(e.message);
      // AppSnackBar.showSnackBar(
      //   context,
      //   e.message,
      //   SnackBarType.error,
      //   showCloseButton: true,
      //   isTimerNeeded: false,
      // );
      // emit(SalesDetailsError(message: e.message, statusCode: e.statusCode));
      return false;
    } catch (e) {
      debugPrint(e.toString());
      // emit(SalesDetailsError(message: e.toString()));
      return false;
    }
  }
}
