class APIConsts {
  const APIConsts._();

  static const String API_VERSION_V1 = 'v1';
  static const String API_PREFIX = '/api';

  static const String agentDocType = "AGENT_LICENSE";
  static const String staffDocType = "STAFF_ONBOARDING_DOCUMENT";
  static const String agentCategoryType = "USER";
  static const String salesCategoryType = "SALES";
  static const String salesDocType = "CLOSING_DOCUMENT";
  static const String salesDocTypeApproved = 'APPROVED_CLOSING_DOCUMENT';
  static const List<String> allowedFileExtensions = [
    'pdf',
    'jpg',
    'png',
    'jpeg',
  ];
  static const List<String> imageExtensionsAllowed = [
    'jpg',
    'png',
    'jpeg',
    'webp',
  ];
  static const List<String> pdfExtensionsAllowed = ['pdf'];

  static const List<String> leadSourceOptions = [
    "COLD_CALLING",
    "DIRECT_MAIL",
    "EMAIL_MARKETING",
    "FACEBOOK_ADS",
    "GOOGLE_ADS",
    "MLS",
    "NETWORKING",
    "OPEN_HOUSE",
    "OTHER",
    "PAST_CLIENT",
    "REFERRAL",
    "REALTOR_COM",
    "SOCIAL_MEDIA",
    "SPHERE_OF_INFLUENCE",
    "WEBSITE",
    "YARD_SIGN",
    "ZILLOW",
  ];

  static const nameCharacterLimit = 50;
  static const phoneCharacterLimit = 12;
  static const emailCharacterLimit = 254;
  static const additionalInfoCharacterLimit = 255;
  static const agentLicenseIdCharacterLimit = 100;
  static const companyNameCharacterLimit = 100;
}
