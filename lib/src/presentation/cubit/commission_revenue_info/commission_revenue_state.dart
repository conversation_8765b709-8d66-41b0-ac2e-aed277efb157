part of 'commission_revenue_cubit.dart';

@immutable
sealed class CommissionRevenueState {
  const CommissionRevenueState();
}

final class CommissionRevenueInitial extends CommissionRevenueState {
  const CommissionRevenueInitial();
}

final class CommissionRevenueLoading extends CommissionRevenueState {
  final List<CommissionRevenueInfoModel> items;
  const CommissionRevenueLoading([this.items = const []]);
}

final class CommissionRevenueLoaded extends CommissionRevenueState {
  final List<CommissionRevenueInfoModel> items;
  const CommissionRevenueLoaded(this.items);
}

final class CommissionRevenueError extends CommissionRevenueState {
  final String message;
  const CommissionRevenueError(this.message);
}
