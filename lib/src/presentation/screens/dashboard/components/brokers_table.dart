import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/utils/app_snack_bar.dart';
import '../../../../core/utils/column_mapping_utils.dart';
import '../../../../core/utils/format_currency_dollar.dart';
import '../../../../core/utils/format_phone_number.dart';
import '/src/presentation/cubit/broker/broker_cubit.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../domain/models/broker_api.dart';
import '../../../../domain/models/user.dart';
import '../../../cubit/user/user_cubit.dart';
import '../../../shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import 'commission_revenue_info.dart';

class BrokersTable extends HookWidget {
  final bool showEditOptions;
  final Function(Brokers)? onNavigateToAgentNetwork;
  final Function(String userId, String userName)? onNavigateToCommissionInfo;

  const BrokersTable({
    super.key,
    this.onNavigateToAgentNetwork,
    this.showEditOptions = false,
    required this.onNavigateToCommissionInfo,
  });

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getBrokerBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = Responsive.isDesktop(context);
    final sortedBrokers = useState<List<Brokers>>([]);
    final totalBrokers = useState(0);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final ValueNotifier<String?> searchString = useState('');
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final ValueNotifier<String?> sortBy = useState("created_at");
    final ValueNotifier<String?> sortOrder = useState("ASC");
    final currentFilters = useState<Map<String, dynamic>>({});

    final user = context.watch<UserCubit>().state.user;
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
      brokerListTotalRevenueColumnHeader,
      brokerListCommissionColumnHeader,
      brokerListrevenueShareColumnHeader,
    ];

    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);

    void handleSort(String columnName, bool ascending) async {
      String backendColumnName;
      if (columnName.isEmpty) {
        sortBy.value = "created_at";
        sortOrder.value = "ASC";
        backendColumnName = "created_at";
      } else {
        // Map the display column name to the backend field name
        backendColumnName = _getBackendColumnName(columnName);
        sortBy.value = backendColumnName;
        sortOrder.value = ascending ? 'ASC' : 'DESC';
      }

      if (context.mounted) {
        await _fetchBrokers(
          context,
          user,
          selectedDate: selectedDate.value,
          page: currentpage.value,
          searchString: searchString.value,
          sortColumn: backendColumnName,
          sortOrder: sortOrder.value,
        );
      }
    }

    useEffect(() {
      Future.microtask(() async {
        if (context.mounted) {
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
            sortColumn: sortBy.value,
            sortOrder: sortOrder.value,
          );
        }
      });

      return null;
    }, [user]);

    return LayoutBuilder(
      builder: (context, constraints) {
        return BlocConsumer<BrokerCubit, BrokerState>(
          listener: (context, state) {
            // TODO: implement listener
            if (state is BrokerLoaded) {
              pageCount.value = state.brokerApi?.totalPages ?? 0;
              totalBrokers.value = state.brokerApi?.totalElements ?? 0;
              sortedBrokers.value = state.brokerApi?.brokers ?? [];
            }
          },
          builder: (context, state) {
            // if (state is BrokerLoaded) {
            //   WidgetsBinding.instance.addPostFrameCallback((_) {
            //     pageCount.value = state.brokerApi?.totalPages ?? 0;
            //     totalBrokers.value = state.brokerApi?.totalElements ?? 0;
            //     sortedBrokers.value = state.brokerApi?.brokers ?? [];
            //   });
            // }

            List<Brokers> currentBrokerageData = [];
            if (state is BrokerLoaded) {
              currentBrokerageData = state.brokerApi?.brokers ?? [];
            } else {
              // Use stored values from useState
              currentBrokerageData = sortedBrokers.value;
            }
            // if (user != null &&
            //     state is BrokerLoading &&
            //     sortedBrokers.value.isEmpty) {
            //   return const Center(child: CircularProgressIndicator());
            // }
            return ValueListenableBuilder(
              valueListenable: sortedBrokers,
              builder: (context, value, child) {
                return CustomDataTableWidget<Brokers>(
                  data: sortedBrokers.value,
                  title: brokersTab,
                  titleIcon: "$iconAssetpath/user.png",
                  searchHint: searchHint,
                  searchFn: (broker) =>
                      broker.fullName +
                      broker.phone +
                      broker.email +
                      AppDateFormatter.formatDateMMddyyyy(broker.joiningDate) +
                      broker.totalDownlineAgents.toString() +
                      broker.totalSales.toString(),
                  dateFilterColumns: const [
                    brokerListJoinDateColumnHeader, // Join date should use calendar picker
                  ],
                  dateFilterBannerTexts: const {
                    brokerListJoinDateColumnHeader: 'Users joined on or after',
                  },
                  filterColumnNames: [brokerListJoinDateColumnHeader],
                  filterValueExtractors: {
                    brokerListJoinDateColumnHeader: (broker) =>
                        AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
                  },
                  columnNames: formattedHeaders,
                  showCrossButtonForFilter: const {
                    brokerListJoinDateColumnHeader: true,
                  },
                  cellBuilders: [
                    (broker) => broker.fullName,
                    (broker) => PhoneUtils.formatPhoneNumber(broker.phone),
                    (broker) => broker.email,
                    (broker) =>
                        AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
                    (broker) => broker.totalDownlineAgents.toString(),
                    (broker) => '${broker.totalSales}',
                    (broker) => formatCurrencyDollar(broker.salesVolume),
                    (broker) => formatCurrencyDollar(broker.salesCommission),
                    (broker) => formatCurrencyDollar(broker.revenueShare),
                  ],
                  iconCellBuilders: [
                    (broker) => TableCellData(
                      text: broker.fullName,
                      leftIconAsset: "$iconAssetpath/agent_round.png",
                      iconSize: 30,
                    ),
                    null, // contact - no icon
                    null, // email - no icon
                    null, // joinDate - no icon
                    null, // agents - no icon
                    null, // totalSales - no icon
                    null, // totalrevenue - no icon
                    null, // commission - no icon
                    null, // revenue share
                  ],
                  useIconBuilders: [
                    true, // name - use icon
                    false, // contact - use text
                    false, // email - use text
                    false, // joinDate - use text
                    false, // agents - use text
                    false, // totalSales - use text
                    false, // totalrevenue - use text
                    false, // commission - use text
                    false, // revenue share
                  ],
                  actionBuilders: [
                    (context, broker) => ActionButtonEye(
                      onPressed: () => _onBrokerAction(context, broker),
                      isCompact: true,
                      isMobile: false,
                      name: broker.fullName,
                    ),
                    (context, broker) => ActionButtonEye(
                      onPressed: () {
                        onNavigateToCommissionInfo != null
                            ? onNavigateToCommissionInfo!(
                                broker.userId,
                                broker.fullName,
                              )
                            : (_) {};
                      },
                      isCompact: true,
                      isMobile: false,
                      icon:
                          "$iconAssetpath/link_icon.png", // optional custom icon asset
                      padding: 2.0,
                      name: broker.fullName,
                    ),
                    //TODO: implement later
                    /* if (showEditOptions) ...[
                      (context, broker) => ActionButtonEye(
                        onPressed: () =>
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Under development!')),
                            ),
                        isCompact: true,
                        isMobile: false,
                        padding: 8,
                        icon: '$iconAssetpath/table_edit.png',
                      ),
                      (context, broker) => ActionButtonEye(
                        onPressed: () =>
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Under development!')),
                            ),
                        isCompact: true,
                        isMobile: false,
                        padding: 8,
                        icon: '$iconAssetpath/delete.png',
                      ),
                    ],*/
                  ],
                  mobileCardBuilder: (context, broker) =>
                      _buildMobileBrokerCard(broker, context),
                  onSort: handleSort,
                  emptyStateMessage: _getEmptyMessage(
                    state,
                    currentBrokerageData,
                  ),
                  useMinHeight: false, // Disable fixed min height
                  pageCount: pageCount.value,
                  totalElements: totalBrokers.value,
                  isLoading: state is BrokerLoading,
                  useExpandedView: true,
                  currentPageIndex: currentpage.value,
                  onAllFiltersChanged: (allFilters) async {
                    currentFilters.value = allFilters;
                    currentpage.value = 0;
                    DateTime? joinDateValue;
                    // Extract dropdown filters
                    if (allFilters.containsKey(
                      brokerListJoinDateColumnHeader,
                    )) {
                      joinDateValue =
                          allFilters[brokerListJoinDateColumnHeader];
                      selectedDate.value = joinDateValue;
                    } else {
                      selectedDate.value = null;
                    }

                    await _fetchBrokers(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      page: 0,
                      searchString: searchString.value,
                      sortColumn: sortBy.value,
                      sortOrder: sortOrder.value,
                    );
                  },
                  //TODO: Remove later
                  //   onDateFilterChanged: (value) async {
                  //   selectedDate.value = value;
                  //   await _fetchBrokers(
                  //     context,
                  //     user,
                  //     selectedDate: value,
                  //     page: currentpage.value,
                  //     searchString: searchString.value,
                  //     sortColumn: sortBy.value,
                  //     sortOrder: sortOrder.value,
                  //   );
                  // },
                  handleTableSearch: (value) async {
                    // Cancel any pending search
                    searchDebouncer.value?.cancel();

                    // Store the search value immediately for UI feedback
                    searchString.value = value;

                    // Don't search if value hasn't changed
                    if (lastSearchValue.value == value) {
                      return;
                    }

                    lastSearchValue.value = value;

                    // Debounce: wait 300ms before making the API call
                    searchDebouncer.value = Timer(
                      const Duration(milliseconds: 300),
                      () async {
                        currentpage.value = 0;
                        await _fetchBrokers(
                          context,
                          user,
                          selectedDate: selectedDate.value,
                          page: 0,
                          searchString: value,
                          sortColumn: sortBy.value,
                          sortOrder: sortOrder.value,
                        );
                      },
                    );
                  },
                  handlePagination: (page) async {
                    currentpage.value = page;
                    await _fetchBrokers(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      searchString: searchString.value,
                      page: page,
                      sortColumn: sortBy.value,
                      sortOrder: sortOrder.value,
                    );
                  },
                  onResetFilters: ({required bool isFilterOptionOnlyReload}) async {
                    if (!isFilterOptionOnlyReload) {
                      selectedDate.value = null; // DateTime? type
                      searchString.value = ''; // String? type (empty string)
                      currentpage.value =
                          1; // int type (reset to first page, usually 0 or 1)
                      sortBy.value =
                          'created_at'; // String? type (default sort column, or set to '')
                      sortOrder.value = 'ASC';
                      await _fetchBrokers(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        searchString: searchString.value,
                        page: currentpage.value,
                        sortColumn: sortBy.value,
                        sortOrder: sortOrder.value,
                      );
                    }
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  String _getEmptyMessage(BrokerState state, List<Brokers> currentData) {
    if (state is BrokerLoading && currentData.isEmpty) {
      return '';
    }

    if (state is BrokerLoaded && currentData.isEmpty) {
      return noDataAvailable;
    }

    return '';
  }

  Future<void> _fetchBrokers(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required int page,
    required String? searchString,
    required String? sortColumn,
    required String? sortOrder,
  }) async {
    if (context.mounted && (user?.userId != null || user?.userId != '')) {
      // String? formattedDate = selectedDate != null
      //     ? AppDateFormatter.formatDateyyyyMMdd(selectedDate)
      //     : null;

      final dateString = AppDateFormatter.formatToUtcIso8601(selectedDate);

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortColumn,
        "sortDirection": sortOrder,
        "searchString": searchString,
        "joiningDate": dateString,
        "userId": user?.userId,
        "associatedBrokerageId": null,
        "agentId": null,
        "active": null,
      };
      await context.read<BrokerCubit>().fetchBrokers(payload);
    }
  }

  void _onBrokerAction(BuildContext context, Brokers broker) {
    if (onNavigateToAgentNetwork != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetwork!(broker);
      } catch (e) {
        // If no matching broker found, show error message
        AppSnackBar.showSnackBar(
          context,
          'Broker not found: ${broker.fullName}',
          SnackBarType.error,
          showCloseButton: false,
          isTimerNeeded: true,
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      AppSnackBar.showSnackBar(
        context,
        'Action clicked for ${broker.fullName}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Widget _buildMobileBrokerCard(Brokers broker, BuildContext context) {
    String joinDate = AppDateFormatter.formatDateMMddyyyy(broker.joiningDate);
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SelectableText(
                broker.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SelectableText(
            '$brokerListContactColumnHeader: ${PhoneUtils.formatPhoneNumber(broker.phone)}',
          ),
          SelectableText('$brokerListEmailColumnHeader: ${broker.email}'),
          SelectableText('$brokerListJoinDateColumnHeader: $joinDate'),
          SelectableText(
            '$brokerListAgentsColumnHeader: ${broker.totalDownlineAgents}',
          ),
          SelectableText(
            '$brokerListTotalSalesColumnHeader: ${broker.totalSales}',
          ),
          SelectableText(
            '$brokerListTotalRevenueColumnHeader: ${formatCurrencyDollar(broker.salesVolume)}',
          ),
          SelectableText(
            '$brokerListrevenueShareColumnHeader: ${formatCurrencyDollar(broker.revenueShare)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // View Agent Details Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () => _onBrokerAction(context, broker),
                    isMobile: true,
                    name: broker.fullName,
                  ),
                ),
                const SizedBox(height: 8), // Spacing between buttons
                // View Commission Revenue Button
                SizedBox(
                  width: double.infinity,
                  child: ActionButtonEye(
                    onPressed: () {
                      if (onNavigateToCommissionInfo != null) {
                        onNavigateToCommissionInfo!(
                          broker.userId,
                          broker.fullName,
                        );
                      }
                    },
                    isMobile: true,
                    icon: "$iconAssetpath/link_icon.png",
                    padding: 2.0,
                    name: broker.fullName,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
