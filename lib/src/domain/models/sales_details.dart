// To parse this JSON data, do
//
//     final salesClosingDocDetailsApi = salesClosingDocDetailsApiFromJson(jsonString);

import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../../core/enum/sale_doc_type.dart';

SalesClosingDocDetailsApi salesClosingDocDetailsApiFromJson(String str) =>
    SalesClosingDocDetailsApi.fromJson(json.decode(str));

String salesClosingDocDetailsApiToJson(SalesClosingDocDetailsApi data) =>
    json.encode(data.toJson());

class SalesClosingDocDetailsApi {
  SaleData? saleData;
  List<FileDetails>? fileDetails;

  SalesClosingDocDetailsApi({this.saleData, this.fileDetails});

  SalesClosingDocDetailsApi copyWith({
    SaleData? saleData,
    List<FileDetails>? fileDetails,
  }) => SalesClosingDocDetailsApi(
    saleData: saleData ?? this.saleData,
    fileDetails: fileDetails ?? this.fileDetails,
  );

  factory SalesClosingDocDetailsApi.fromJson(Map<String, dynamic> json) =>
      SalesClosingDocDetailsApi(
        saleData: json["saleData"] == null
            ? null
            : SaleData.fromJson(json["saleData"]),
        fileDetails: json["fileDetails"] == null
            ? []
            : List<FileDetails>.from(
                json["fileDetails"]!.map((x) => FileDetails.fromJson(x)),
              ),
      );

  Map<String, dynamic> toJson() => {
    "saleData": saleData?.toJson(),
    "fileDetails": fileDetails == null
        ? []
        : List<dynamic>.from(fileDetails!.map((x) => x.toJson())),
  };
}

SalesDocumentsType? _documentsTypeFromString(String? value) {
  if (value == null) return null;
  final normalized = value.split('.').last.trim().toUpperCase();
  try {
    return SalesDocumentsType.values.firstWhere(
      (v) => v.toString().split('.').last.toUpperCase() == normalized,
    );
  } catch (_) {
    return null;
  }
}

String? _documentsTypeToString(SalesDocumentsType? type) {
  return type == null ? null : type.toString().split('.').last;
}

class FileDetails {
  String? fileName;
  String? fileUrl;
  SalesDocumentsType? fileType;
  String? fileSize;
  String? title;
  String? fileCategory;

  FileDetails({
    this.fileName,
    this.title,
    this.fileUrl,
    this.fileType,
    this.fileSize,
    this.fileCategory,
  });

  FileDetails copyWith({
    String? fileName,
    String? title,
    String? fileUrl,
    SalesDocumentsType? fileType,
    String? fileSize,
    String? fileCategory,
  }) => FileDetails(
    fileName: fileName ?? this.fileName,
    title: title ?? this.title,
    fileUrl: fileUrl ?? this.fileUrl,
    fileType: fileType ?? this.fileType,
    fileSize: fileSize ?? this.fileSize,
    fileCategory: fileCategory ?? this.fileCategory,
  );

  factory FileDetails.fromJson(Map<String, dynamic> json) => FileDetails(
    fileName: json["fileName"],
    fileUrl: json["fileUrl"],
    fileType: _documentsTypeFromString(json["fileType"]),
    fileSize: json["fileSize"],
    fileCategory: json["fileCategory"],
    title: _documentsTypeFromString(json["fileType"])?.displayName ?? '',
  );

  Map<String, dynamic> toJson() => {
    "fileName": fileName,
    "fileUrl": fileUrl,
    "fileType": _documentsTypeToString(fileType),
    "fileSize": fileSize,
    "fileCategory": fileCategory,
  };
}

class SaleData {
  String? salesId;
  String? agentId;
  String? representing;
  double? salesVolume;
  String? propertyType;
  String? propertyAddress;
  String? propertyDescription;
  String? transactionName;
  String? transactionFileNumber;
  double? depositReceived;
  DateTime? dateDeposited;
  double? grossCommission;
  double? commissionSplit;
  DateTime? dateReleased;
  DateTime? listingDate;
  DateTime? expirationDate;
  DateTime? closingDate;
  String? lenderInfo;
  String? legalDescription;
  String? escrowNumber;
  String? escrowCompany;
  DateTime? dateReceived;
  double? amountReleased;
  String? transactionType;
  String? representingFirstName;
  String? representingLastName;
  String? representingEmail;
  String? representingCompanyPhoneNumber;
  String? representingAddress;
  String? leadSource;
  String? reviewedStatus;

  SaleData({
    this.salesId,
    this.agentId,
    this.representing,
    this.salesVolume,
    this.propertyType,
    this.propertyAddress,
    this.propertyDescription,
    this.transactionName,
    this.transactionFileNumber,
    this.depositReceived,
    this.dateDeposited,
    this.grossCommission,
    this.commissionSplit,
    this.dateReleased,
    this.listingDate,
    this.expirationDate,
    this.closingDate,
    this.lenderInfo,
    this.legalDescription,
    this.escrowNumber,
    this.escrowCompany,
    this.dateReceived,
    this.amountReleased,
    this.transactionType,
    this.representingFirstName,
    this.representingLastName,
    this.representingEmail,
    this.representingCompanyPhoneNumber,
    this.representingAddress,
    this.leadSource,
    this.reviewedStatus,
  });

  SaleData copyWith({
    String? salesId,
    String? agentId,
    String? representing,
    double? salesVolume,
    String? propertyType,
    String? propertyAddress,
    String? propertyDescription,
    String? transactionName,
    String? transactionFileNumber,
    double? depositReceived,
    DateTime? dateDeposited,
    double? grossCommission,
    double? commissionSplit,
    DateTime? dateReleased,
    DateTime? listingDate,
    DateTime? expirationDate,
    DateTime? closingDate,
    String? lenderInfo,
    String? legalDescription,
    String? escrowNumber,
    String? escrowCompany,
    DateTime? dateReceived,
    double? amountReleased,
    String? transactionType,
    String? representingFirstName,
    String? representingLastName,
    String? representingEmail,
    String? representingCompanyPhoneNumber,
    String? representingAddress,
    String? leadSource,
    String? reviewedStatus,
  }) => SaleData(
    salesId: salesId ?? this.salesId,
    agentId: agentId ?? this.agentId,
    representing: representing ?? this.representing,
    salesVolume: salesVolume ?? this.salesVolume,
    propertyType: propertyType ?? this.propertyType,
    propertyAddress: propertyAddress ?? this.propertyAddress,
    propertyDescription: propertyDescription ?? this.propertyDescription,
    transactionName: transactionName ?? this.transactionName,
    transactionFileNumber: transactionFileNumber ?? this.transactionFileNumber,
    depositReceived: depositReceived ?? this.depositReceived,
    dateDeposited: dateDeposited ?? this.dateDeposited,
    grossCommission: grossCommission ?? this.grossCommission,
    commissionSplit: commissionSplit ?? this.commissionSplit,
    dateReleased: dateReleased ?? this.dateReleased,
    listingDate: listingDate ?? this.listingDate,
    expirationDate: expirationDate ?? this.expirationDate,
    closingDate: closingDate ?? this.closingDate,
    lenderInfo: lenderInfo ?? this.lenderInfo,
    legalDescription: legalDescription ?? this.legalDescription,
    escrowNumber: escrowNumber ?? this.escrowNumber,
    escrowCompany: escrowCompany ?? this.escrowCompany,
    dateReceived: dateReceived ?? this.dateReceived,
    amountReleased: amountReleased ?? this.amountReleased,
    transactionType: transactionType ?? this.transactionType,
    representingFirstName: representingFirstName ?? this.representingFirstName,
    representingLastName: representingLastName ?? this.representingLastName,
    representingEmail: representingEmail ?? this.representingEmail,
    representingCompanyPhoneNumber:
        representingCompanyPhoneNumber ?? this.representingCompanyPhoneNumber,
    representingAddress: representingAddress ?? this.representingAddress,
    leadSource: leadSource ?? this.leadSource,
    reviewedStatus: reviewedStatus ?? this.reviewedStatus,
  );

  factory SaleData.fromJson(Map<String, dynamic> json) => SaleData(
    salesId: json["salesId"],
    agentId: json["agentId"],
    representing: json["representing"],
    salesVolume: (json["salesVolume"] ?? 0) * 1.0,
    propertyType: json["propertyType"],
    propertyAddress: json["propertyAddress"],
    propertyDescription: json["propertyDescription"],
    transactionName: json["transactionName"],
    transactionFileNumber: json["transactionFileNumber"],
    depositReceived: (json["depositReceived"] ?? 0) * 1.0,
    dateDeposited: json["dateDeposited"] == null
        ? null
        : DateTime.parse(json["dateDeposited"]),
    grossCommission: (json["grossCommission"] ?? 0) * 1.0,
    commissionSplit: (json["commissionSplit"] ?? 0) * 1.0,
    dateReleased: json["dateReleased"] == null
        ? null
        : DateTime.parse(json["dateReleased"]),
    listingDate: json["listingDate"] == null
        ? null
        : DateTime.parse(json["listingDate"]),
    expirationDate: json["expirationDate"] == null
        ? null
        : DateTime.parse(json["expirationDate"]),
    closingDate: json["closingDate"] == null
        ? null
        : DateTime.parse(json["closingDate"]),
    lenderInfo: json["lenderInfo"],
    legalDescription: json["legalDescription"],
    escrowNumber: json["escrowNumber"],
    escrowCompany: json["escrowCompany"],
    dateReceived: json["dateReceived"] == null
        ? null
        : DateTime.parse(json["dateReceived"]),
    amountReleased: (json["amountReleased"] ?? 0) * 1.0,
    transactionType: json["transactionType"],
    representingFirstName: json["representingFirstName"],
    representingLastName: json["representingLastName"],
    representingEmail: json["representingEmail"],
    representingCompanyPhoneNumber: json["representingCompanyPhoneNumber"],
    representingAddress: json["representingAddress"],
    leadSource: json["leadSource"],
    reviewedStatus: json["reviewedStatus"],
  );

  Map<String, dynamic> toJson() => {
    "salesId": salesId,
    "agentId": agentId,
    "representing": representing,
    "salesVolume": salesVolume,
    "propertyType": propertyType,
    "propertyAddress": propertyAddress,
    "propertyDescription": propertyDescription,
    "transactionName": transactionName,
    "transactionFileNumber": transactionFileNumber,
    "depositReceived": depositReceived,
    "dateDeposited":
        "${dateDeposited!.year.toString().padLeft(4, '0')}-${dateDeposited!.month.toString().padLeft(2, '0')}-${dateDeposited!.day.toString().padLeft(2, '0')}",
    "grossCommission": grossCommission,
    "commissionSplit": commissionSplit,
    "dateReleased":
        "${dateReleased!.year.toString().padLeft(4, '0')}-${dateReleased!.month.toString().padLeft(2, '0')}-${dateReleased!.day.toString().padLeft(2, '0')}",
    "listingDate":
        "${listingDate!.year.toString().padLeft(4, '0')}-${listingDate!.month.toString().padLeft(2, '0')}-${listingDate!.day.toString().padLeft(2, '0')}",
    "expirationDate":
        "${expirationDate!.year.toString().padLeft(4, '0')}-${expirationDate!.month.toString().padLeft(2, '0')}-${expirationDate!.day.toString().padLeft(2, '0')}",
    "closingDate":
        "${closingDate!.year.toString().padLeft(4, '0')}-${closingDate!.month.toString().padLeft(2, '0')}-${closingDate!.day.toString().padLeft(2, '0')}",
    "lenderInfo": lenderInfo,
    "legalDescription": legalDescription,
    "escrowNumber": escrowNumber,
    "escrowCompany": escrowCompany,
    "dateReceived":
        "${dateReceived!.year.toString().padLeft(4, '0')}-${dateReceived!.month.toString().padLeft(2, '0')}-${dateReceived!.day.toString().padLeft(2, '0')}",
    "amountReleased": amountReleased,
    "transactionType": transactionType,
    "representingFirstName": representingFirstName,
    "representingLastName": representingLastName,
    "representingEmail": representingEmail,
    "representingCompanyPhoneNumber": representingCompanyPhoneNumber,
    "representingAddress": representingAddress,
    "leadSource": leadSource,
    "reviewedStatus": reviewedStatus,
  };
}

class SalesDocumentInfo {
  final String id;
  final String type;
  final String title;
  final String description;
  final List<String> allowedExtensions;
  final ValueNotifier<PlatformFile?> file;
  final ValueNotifier<bool> isUploading;
  final ValueNotifier<double> uploadProgress;
  final ValueNotifier<String> uploadStatus;
  final bool isRequired;

  SalesDocumentInfo({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.allowedExtensions,
    this.isRequired = true,
    ValueNotifier<PlatformFile?>? file,
    ValueNotifier<bool>? isUploading,
    ValueNotifier<double>? uploadProgress,
    ValueNotifier<String>? uploadStatus,
  }) : file = file ?? ValueNotifier(null),
       isUploading = isUploading ?? ValueNotifier(false),
       uploadProgress = uploadProgress ?? ValueNotifier(0.0),
       uploadStatus = uploadStatus ?? ValueNotifier('');

  //copywith
  SalesDocumentInfo copyWith({
    String? id,
    String? type,
    String? title,
    String? description,
    List<String>? allowedExtensions,
    ValueNotifier<PlatformFile?>? file,
    ValueNotifier<bool>? isUploading,
    ValueNotifier<double>? uploadProgress,
    ValueNotifier<String>? uploadStatus,
    bool? isRequired,
  }) {
    return SalesDocumentInfo(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      allowedExtensions: allowedExtensions ?? this.allowedExtensions,
      file: file ?? this.file,
      isUploading: isUploading ?? this.isUploading,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      isRequired: isRequired ?? this.isRequired,
    );
  }

  void dispose() {
    file.dispose();
    isUploading.dispose();
    uploadProgress.dispose();
    uploadStatus.dispose();
  }
}
