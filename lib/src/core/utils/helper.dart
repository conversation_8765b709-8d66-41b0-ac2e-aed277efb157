import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../config/app_strings.dart' as AppStrings;

Future<bool> isValidImageUrl(String url) async {
  try {
    final response = await http.head(Uri.parse(url));
    if (response.statusCode == 200) {
      final contentType = response.headers['content-type'];
      return contentType != null && contentType.startsWith('image/');
    }
    return false;
  } catch (e) {
    return false;
  }
}

// Helper method for safe int conversion
int toInt(dynamic value) {
  if (value == null) return 0;
  if (value is int) return value;
  if (value is double) return value.toInt();
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

// Helper method for safe double conversion
double toDouble(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

// Helper method for safe DateTime parsing
DateTime parseDate(dynamic value) {
  if (value == null) return DateTime.now();
  if (value is String && value.isNotEmpty) {
    return DateTime.tryParse(value) ?? DateTime.now();
  }
  return DateTime.now();
}

String toString(dynamic v) => v?.toString() ?? '';

// Helper method for text length with ellipses.
String truncateWithTextEllipsis(String text, {required int maxLength}) {
  if (text.length <= maxLength) return text;
  return '${text.substring(0, maxLength)}...';
}

// Converts bytes to MB if >= 1 MB
// Converts bytes to KB if < 1 MB
// Rounds to 2 decimal places for readability

String formatFileSize(int bytes) {
  if (bytes < 0) bytes = 0;
  const kbDiv = 1024;
  const mbDiv = 1024 * 1024;

  if (bytes >= mbDiv) {
    final mb = bytes / mbDiv;
    // For readability: 2 decimals for small MB values, 1 decimal otherwise
    final mbStr = mb < 10 ? mb.toStringAsFixed(2) : mb.toStringAsFixed(1);
    return '$mbStr ${AppStrings.fileSizeMB}';
  } else {
    final kb = bytes / kbDiv;
    // Show 2 decimals for small KB values (e.g. 1 KB -> 1.00 KB), otherwise no decimals
    final kbStr = kb < 10 ? kb.toStringAsFixed(2) : kb.toStringAsFixed(0);
    return '$kbStr  ${AppStrings.fileSizeKB}';
  }
}

/// Return true if PDF seems corrupted/invalid.
/// Very lightweight check: verifies "%PDF" header and presence of "%%EOF" near file end.
Future<bool> isPdfCorrupted(PlatformFile file) async {
  try {
    // Use bytes on web
    if (kIsWeb) {
      final bytes = file.bytes;
      if (bytes == null || bytes.isEmpty) return true;
      // Check header
      final header = String.fromCharCodes(bytes.take(5));
      if (!header.startsWith('%PDF')) return true;
      // Check footer - look into last up to 2048 bytes for EOF marker
      final tailStart = bytes.length > 2048 ? bytes.length - 2048 : 0;
      final tail = String.fromCharCodes(bytes.skip(tailStart));
      if (!tail.contains('%%EOF')) return true;
      return false;
    } else {
      // On non-web platforms read first few bytes and last chunk
      if (file.path == null) return true;
      final f = File(file.path!);
      if (!await f.exists()) return true;
      final raf = await f.open();
      try {
        final len = await raf.length();
        if (len < 5) return true;
        // read header (first 5 bytes)
        await raf.setPosition(0);
        final headerBytes = await raf.read(5);
        final header = String.fromCharCodes(headerBytes);
        if (!header.startsWith('%PDF')) return true;
        // read tail up to last 2048 bytes
        final tailLen = len > 2048 ? 2048 : len;
        await raf.setPosition(len - tailLen);
        final tailBytes = await raf.read(tailLen);
        final tail = String.fromCharCodes(tailBytes);
        if (!tail.contains('%%EOF')) return true;
        return false;
      } finally {
        await raf.close();
      }
    }
  } catch (e) {
    debugPrint('PDF validation raised exception: $e');
    rethrow;
  }
}
