import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/models/upload_task_model.dart';
import 'upload_state.dart';

export 'upload_state.dart';

/// Cubit for managing global upload state
/// This cubit persists across navigation and manages all upload tasks
class UploadCubit extends Cubit<UploadState> {
  UploadCubit() : super(UploadInitial());

  /// List of all upload tasks
  final List<UploadTask> _tasks = [];

  /// Get all tasks
  List<UploadTask> get tasks => List.unmodifiable(_tasks);

  /// Get active tasks (queued, uploading, or processing)
  List<UploadTask> get activeTasks => _tasks
      .where(
        (task) =>
            task.status == UploadTaskStatus.queued ||
            task.status == UploadTaskStatus.uploading ||
            task.status == UploadTaskStatus.processing,
      )
      .toList();

  /// Get completed tasks
  List<UploadTask> get completedTasks => _tasks
      .where((task) => task.status == UploadTaskStatus.completed)
      .toList();

  /// Get failed tasks
  List<UploadTask> get failedTasks =>
      _tasks.where((task) => task.status == UploadTaskStatus.failed).toList();

  /// Check if there are any active uploads
  bool get hasActiveUploads => activeTasks.isNotEmpty;

  /// Add a new upload task
  void addTask(UploadTask task) {
    _tasks.add(task);
    emit(UploadTaskAdded(task: task, allTasks: List.from(_tasks)));
  }

  /// Update an existing task
  void updateTask(UploadTask updatedTask) {
    final index = _tasks.indexWhere(
      (task) => task.taskId == updatedTask.taskId,
    );
    if (index != -1) {
      _tasks[index] = updatedTask;
      emit(UploadTaskUpdated(task: updatedTask, allTasks: List.from(_tasks)));
    }
  }

  /// Remove a task
  void removeTask(String taskId) {
    _tasks.removeWhere((task) => task.taskId == taskId);
    emit(UploadTaskRemoved(taskId: taskId, allTasks: List.from(_tasks)));
  }

  /// Get a specific task by ID
  UploadTask? getTask(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.taskId == taskId);
    } catch (e) {
      return null;
    }
  }

  /// Clear all completed tasks
  void clearCompletedTasks() {
    _tasks.removeWhere((task) => task.status == UploadTaskStatus.completed);
    emit(UploadTasksCleared(allTasks: List.from(_tasks)));
  }

  /// Clear all tasks
  void clearAllTasks() {
    _tasks.clear();
    emit(UploadTasksCleared(allTasks: []));
  }

  /// Update task progress
  void updateTaskProgress({
    required String taskId,
    required double progress,
    String? currentFileName,
    int? currentFileIndex,
    String? uploadStatus,
  }) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        progress: progress,
        currentFileName: currentFileName,
        currentFileIndex: currentFileIndex,
        uploadStatus: uploadStatus,
      );
      updateTask(updatedTask);
    }
  }

  /// Mark task as uploading
  void markTaskAsUploading(String taskId) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        status: UploadTaskStatus.uploading,
        progress: 0.5,
      );
      updateTask(updatedTask);
    }
  }

  /// Mark task as processing
  void markTaskAsProcessing(String taskId) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        status: UploadTaskStatus.processing,
        progress: 0.8,
      );
      updateTask(updatedTask);
    }
  }

  /// Mark task as completed
  void markTaskAsCompleted(String taskId, String salesId) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        status: UploadTaskStatus.completed,
        progress: 1.0,
        salesId: salesId,
      );
      updateTask(updatedTask);
    }
  }

  /// Mark task as failed
  void markTaskAsFailed(String taskId, String errorMessage) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        status: UploadTaskStatus.failed,
        errorMessage: errorMessage,
      );
      updateTask(updatedTask);
    }
  }

  /// Mark task as cancelled
  void markTaskAsCancelled(String taskId) {
    final task = getTask(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(status: UploadTaskStatus.cancelled);
      updateTask(updatedTask);
    }
  }
}
