import 'package:flutter/services.dart';

class ReferralCodeFormatter extends TextInputFormatter {
  // Enforce up to 8 alphanumeric chars (4-4) and insert hyphen after 4 chars.
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Keep only alphanumeric characters
    final raw = newValue.text;
    String alnumOnly = raw.replaceAll(RegExp(r'[^A-Za-z0-9]'), '');

    // Limit to 8 alphanumeric characters
    if (alnumOnly.length > 8) {
      alnumOnly = alnumOnly.substring(0, 8);
    }

    // Build formatted text with hyphen after 4 chars
    final String formatted;
    if (alnumOnly.length > 4) {
      formatted = '${alnumOnly.substring(0, 4)}-${alnumOnly.substring(4)}';
    } else {
      formatted = alnumOnly;
    }

    // Compute caret position:
    // Count how many alnum chars are before the caret in the raw newValue
    final int rawCaret = newValue.selection.baseOffset.clamp(0, raw.length);
    int alnumBeforeCaret = 0;
    for (int i = 0; i < rawCaret; i++) {
      if (RegExp(r'[A-Za-z0-9]').hasMatch(raw[i])) alnumBeforeCaret++;
    }

    // Map alnumBeforeCaret to formatted index (add 1 if past first 4 chars because of hyphen)
    int newCaretOffset = alnumBeforeCaret;
    if (alnumBeforeCaret > 4) newCaretOffset += 1;

    newCaretOffset = newCaretOffset.clamp(0, formatted.length);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: newCaretOffset),
    );
  }
}
