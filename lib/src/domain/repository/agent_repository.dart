import '../models/RegisteredAgent.dart';
import '../models/agent_model.dart';
import '../models/filter_model.dart';
import '../models/role.dart';

abstract class AgentRepository {
  /// Get list of agents with POST request body
  Future<ApiAgentResponse> getAgents(Map<String, dynamic> requestBody);

  /// Get agent details by ID
  Future<AgentModel> getAgentById(String agentId);

  /// Create a new agent with POST request body
  Future<dynamic> registerAgent(Map<String, dynamic> requestBody);

  /// Upload agent file with POST request body
  Future<bool> uploadAgentFile(Map<String, dynamic> requestBody);

  /// update status after agent registration/invitation
  Future<bool> handleAgentStatusUpdate(
    Map<String, dynamic> requestBody,
    String userId,
    String? inviteCode,
  );

  Future<RegisteredAgent?> fetchRegisteredAgentInfo(String id);

  /// signup agent
  Future<dynamic> signupAgent(Map<String, dynamic> requestBody);

  // validate agent invite link
  Future<Map<String, dynamic>> validateInviteLink(String inviteId);
}
