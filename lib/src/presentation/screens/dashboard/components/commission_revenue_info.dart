import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/column_mapping_utils.dart';
import '../../../../core/utils/regex.dart';
import '../../../../domain/models/commission_revenue_info_model.dart';
import '../../../cubit/commission_revenue_info/commission_revenue_cubit.dart';
import '../../../cubit/user/user_cubit.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/format_currency_dollar.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class CommissionRevenuewInfoScreen extends HookWidget {
  final String? userId;
  final String? userName;
  final bool isBrokerageList;
  const CommissionRevenuewInfoScreen({
    super.key,
    this.userId,
    this.userName,
    required this.isBrokerageList,
  });

  @override
  Widget build(BuildContext context) {
    final sortColumn = useState<String>('transaction_id');
    final sortOrder = useState<String>('ASC');
    final pageCount = useState(0);
    final currentpage = useState(0);
    final ValueNotifier<String?> searchString = useState('');
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');
    final totalElements = useState(0);

    final currentUser = context.watch<UserCubit>().state.user;
    // Fetch commission data when widget loads
    useEffect(() {
      if (userId != null && userId!.isNotEmpty) {
        Future.microtask(() async {
          await fetchCommisionRevenue(
            context,
            page: 1,
            size: 10,
            sortBy: sortColumn.value,
            sortDirection: sortOrder.value,
            userId: userId!,
            searchString: searchString.value,
            currentUserId: currentUser?.userId,
          );
        });
      } else {
        debugPrint(
          'CommissionRevenuewInfoScreen - userId is null or empty: $userId',
        );
      }
      return null;
    }, [userId]);
    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);
    return BlocBuilder<CommissionRevenueCubit, CommissionRevenueState>(
      builder: (context, state) {
        List<CommissionRevenueInfoModel> commissionRevenuedata = [];
        if (state is CommissionRevenueLoaded) {
          commissionRevenuedata = state.commissionRevenue;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.totalPages;
            totalElements.value = state.totalCount ?? 0;
          });
        }
        if (state is CommissionRevenueError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error: ${state.message}',
                  style: AppFonts.regularTextStyle(14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    if (userId != null) {
                      await fetchCommisionRevenue(
                        context,
                        page: 1,
                        size: 10,
                        sortBy: sortColumn.value,
                        sortDirection: sortOrder.value,
                        userId: userId!,
                        searchString: searchString.value,
                        currentUserId: currentUser?.userId,
                      );
                    }
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        void handleSort(String columnName, bool ascending) async {
          // Check if columnName is empty (indicates reset to default sort)
          if (columnName.isEmpty) {
            sortColumn.value = 'transaction_id';
            sortOrder.value = 'ASC';
            if (context.mounted) {
              await fetchCommisionRevenue(
                context,
                page: 1,
                size: 10,
                sortBy: sortColumn.value,
                sortDirection: sortOrder.value,
                userId: userId!,
                searchString: searchString.value,
                currentUserId: currentUser?.userId,
              );
            }
            return;
          }
          String backendColumnName = _getBackendColumnName(columnName);
          sortColumn.value = backendColumnName;
          sortOrder.value = ascending ? 'ASC' : 'DESC';

          debugPrint(
            "handleSort: column=$columnName backend=$backendColumnName sortOrder=${sortOrder.value}",
          );

          if (context.mounted) {
            await fetchCommisionRevenue(
              context,
              page: 1,
              size: 10,
              sortBy: backendColumnName,
              sortDirection: sortOrder.value,
              userId: userId!,
              searchString: searchString.value,
              currentUserId: currentUser?.userId,
            );
          }
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(top: 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: CustomDataTableWidget<CommissionRevenueInfoModel>(
                    data: commissionRevenuedata,
                    title: getTitle(context),
                    titleIcon: "$iconAssetpath/dollar_icon.png",

                    //if no need to show the search and filter buttons, so need to pass false for the belw params
                    //for filters, if we not set specified filter columns then the filter button not visible, it should visible
                    //showFilterButton = true and spaecify the filter columns.
                    showFilterButton: false,
                    showSearchButton: true,
                    searchButtonHeight: 40.0,
                    searchButtonWidth: 250.0,
                    searchHint:
                        'Search by $columnCommissionRevenueInfoTransactionId',
                    columnNames: [
                      columnCommissionRevenueInfoTransactionId,
                      columnCommissionRevenueInfoGrossCommission,
                      if (!isBrokerageList) ...[
                        columnCommissionRevenueInfoPersonalCommision,
                      ],
                      columnCommissionRevenueInfoRevenueShare,
                      columnCommissionRevenueInfoCommissionPercentage,
                      columnCommissionRevenueNotes,
                    ],
                    showSortIconColumns: [
                      columnCommissionRevenueInfoTransactionId,
                      columnCommissionRevenueInfoGrossCommission,
                      if (!isBrokerageList) ...[
                        columnCommissionRevenueInfoPersonalCommision,
                      ],
                      columnCommissionRevenueInfoRevenueShare,
                      columnCommissionRevenueInfoCommissionPercentage,
                    ],
                    cellBuilders: [
                      (t) => t.transactionId,
                      (t) =>
                          (t.grossCommission == 0.0 ||
                              t.grossCommission.abs() < 0.01)
                          ? '-'
                          : formatCurrencyDollar(t.grossCommission),
                      if (!isBrokerageList) ...[
                        (t) =>
                            (t.personalCommission == 0.0 ||
                                t.personalCommission.abs() < 0.01)
                            ? '-'
                            : formatCurrencyDollar(t.personalCommission),
                      ],
                      (t) =>
                          (t.revenueShare == 0.0 || t.revenueShare.abs() < 0.01)
                          ? '-'
                          : formatCurrencyDollar(t.revenueShare),
                      (t) =>
                          (t.commissionPercentage == 0.0 ||
                              t.commissionPercentage.abs() < 0.01)
                          ? '-'
                          : '${t.commissionPercentage}%',
                      (t) => t.notes.isNotEmpty ? t.notes : '-',
                    ],
                    iconCellBuilders: [
                      null,
                      null,
                      if (!isBrokerageList) ...[null],
                      null,
                      null,
                      null,
                    ],
                    useIconBuilders: [
                      true,
                      false,
                      if (!isBrokerageList) ...[false],
                      false,
                      false,
                      false,
                    ],
                    useWidgetBuilders: [
                      false,
                      false,
                      if (!isBrokerageList) ...[false],
                      false,
                      false,
                      false,
                    ],
                    widgetCellBuilders: [
                      null,
                      null,
                      if (!isBrokerageList) ...[null],
                      null,
                      null,
                      null,
                    ],
                    actionBuilders: [],
                    onSort: handleSort,
                    mobileCardBuilder: (context, t) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.shade200,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '$columnCommissionRevenueInfoTransactionId : ${t.transactionId}',
                              style: AppFonts.boldTextStyle(
                                15,
                              ).copyWith(color: Colors.black87),
                            ),
                            const SizedBox(height: 12),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoGrossCommission :',
                              formatCurrencyDollar(t.grossCommission),
                            ),
                            if (!isBrokerageList) ...[
                              const SizedBox(height: 8),
                              _buildInfoRow(
                                '$columnCommissionRevenueInfoPersonalCommision :',
                                formatCurrencyDollar(t.personalCommission),
                              ),
                            ],
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoRevenueShare :',
                              formatCurrencyDollar(t.revenueShare),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueInfoCommissionPercentage :',
                              '${t.commissionPercentage}%',
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '$columnCommissionRevenueNotes:',
                              t.notes.isNotEmpty ? t.notes : '-',
                            ),
                          ],
                        ),
                      );
                    },

                    pageCount: pageCount.value,
                    isLoading: state is CommissionRevenueLoading,
                    totalElements: totalElements.value,
                    currentPageIndex: currentpage.value,
                    showPagination: true,
                    handlePagination: (page) async {
                      if (sortColumn.value.isNotEmpty &&
                          RegExUtils.startsWithUppercase.hasMatch(
                            sortColumn.value,
                          )) {
                        String backendColumnName = _getBackendColumnName(
                          sortColumn.value,
                        );
                        sortColumn.value = backendColumnName;
                      }
                      currentpage.value = page;
                      await fetchCommisionRevenue(
                        context,
                        page: page,
                        size: 10,
                        sortBy: sortColumn.value,
                        sortDirection: sortOrder.value,
                        userId: userId!,
                        searchString: searchString.value,
                        currentUserId: currentUser?.userId,
                      );
                    },
                    handleTableSearch: (value) async {
                      // Cancel any pending search
                      searchDebouncer.value?.cancel();

                      // Store the search value immediately for UI feedback
                      searchString.value = value;

                      // Don't search if value hasn't changed
                      if (lastSearchValue.value == value) {
                        return;
                      }

                      lastSearchValue.value = value;

                      // Debounce: wait 300ms before making the API call
                      searchDebouncer.value = Timer(
                        const Duration(milliseconds: 300),
                        () async {
                          currentpage.value = 0;
                          await fetchCommisionRevenue(
                            context,
                            page: 1,
                            size: 10,
                            sortBy: sortColumn.value,
                            sortDirection: sortOrder.value,
                            userId: userId!,
                            searchString: searchString.value,
                            currentUserId: currentUser?.userId,
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String getTitle(BuildContext context) {
    bool isMobile = Responsive.isMobile(context);
    String title = isMobile
        ? ((userName?.isNotEmpty ?? false)
              ? '$commissionRevenueInfoTitle of \n$userName'
              : commissionRevenueInfoTitle)
        : ((userName?.isNotEmpty ?? false)
              ? '$commissionRevenueInfoTitle of $userName'
              : commissionRevenueInfoTitle);
    return title;
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            label,
            style: AppFonts.regularTextStyle(
              14,
            ).copyWith(color: Colors.grey.shade700),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            value,
            style: AppFonts.regularTextStyle(
              14,
            ).copyWith(color: Colors.black87),
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }

  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getCommissionRevenueBackendColumnName(
      displayColumnName,
    );
  }

  fetchCommisionRevenue(
    BuildContext context, {
    required int page,
    required int size,
    required String sortBy,
    required String sortDirection,
    required String userId,
    required String? searchString,
    String? currentUserId,
  }) async {
    if (context.mounted) {
      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortBy,
        "sortDirection": sortDirection,
        "userId": userId,
        "loggedInUser": currentUserId,
        "searchString": searchString,
      };
      await context
          .read<CommissionRevenueCubit>()
          .fetchCommissionRevenueDetailsByUser(requestBody: payload);
    }
  }
}
