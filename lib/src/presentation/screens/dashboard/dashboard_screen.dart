import 'package:flutter/foundation.dart';
import 'dart:html' as html; // Only available on web

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../domain/models/agent_model.dart';
import '/src/domain/models/broker_api.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/config/constants.dart';
import '../../../domain/models/broker.dart';
import '../../../domain/models/user.dart';
import '../../cubit/user/user_cubit.dart';
import 'components/commission_revenue_info.dart';
import 'components/dashboard_content.dart';

import '../../../core/theme/app_theme.dart';

class DashboardScreen extends HookWidget {
  final Function(Brokers)? onNavigateToAgentNetwork;
  final Function(AgentModel)? onNavigateToAgentNetworkAgent;
  final Function(String userId, String userName, {bool isBrokerageList})?
  onNavigateToCommissionInfo;

  const DashboardScreen({
    super.key,
    this.onNavigateToAgentNetwork,
    this.onNavigateToAgentNetworkAgent,
    this.onNavigateToCommissionInfo,
  });

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserCubit>().state.user;
    useEffect(() {
      if (kIsWeb) {
        html.window.onBeforeUnload.listen((event) {
          event.preventDefault();
          //event.returnValue = '';
          // Required for Chrome to trigger the dialog
        });
      }
      return null;
    }, []);

    return Builder(
      builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _welcomeUser(user),
            const SizedBox(height: defaultPadding / 2),
            DashboardContent(
              onNavigateToAgentNetwork: onNavigateToAgentNetwork,
              onNavigateToAgentNetworkAgent: onNavigateToAgentNetworkAgent,
              onNavigateToCommissionInfo: onNavigateToCommissionInfo,
            ),
            const SizedBox(height: defaultPadding),
          ],
        );
      },
    );
  }

  Widget _welcomeUser(User? user) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.regularTextStyle(
            22,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.7),
          ),
          children: [
            TextSpan(
              text: user?.name ?? '',
              style: AppFonts.semiBoldTextStyle(
                22,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
