part of 'commission_revenue_cubit.dart';

@immutable
abstract class CommissionRevenueState {
  const CommissionRevenueState();
}

class CommissionRevenueInitial extends CommissionRevenueState {
  const CommissionRevenueInitial();
}

class CommissionRevenueLoading extends CommissionRevenueState {
  const CommissionRevenueLoading();
}

final class CommissionRevenueLoaded extends CommissionRevenueState {
  final List<CommissionRevenueInfoModel> commissionRevenue;
  final int totalCount;
  final int totalPages;
  final int currentPage;
  final bool hasMore;

  CommissionRevenueLoaded({
    required this.commissionRevenue,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    this.hasMore = false,
  });
}

class CommissionRevenueError extends CommissionRevenueState {
  final String message;
  final int? statusCode;

  const CommissionRevenueError({required this.message, this.statusCode});
}
