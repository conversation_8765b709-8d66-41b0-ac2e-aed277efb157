class PhoneNumberFormatter {
  PhoneNumberFormatter();
  static const String PHONE_PREFIX = '+1 ';

  /// Formats a phone number with +1 prefix and XXX-XXX-XXXX format
  /// 
  /// [phoneNumber] - The phone number to format (can be null or empty)
  /// [includePrefix] - Whether to include the +1 prefix (default: true)
  /// [returnOriginalIfInvalid] - For user input, return original if < 10 digits (default: false)
  static String formatPhoneNumber(
    String? phoneNumber, {
    bool includePrefix = true,
    bool returnOriginalIfInvalid = false,
  }) {
    // Handle null or empty input
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return includePrefix ? PHONE_PREFIX : '';
    }

    // Store original for potential return
    final original = phoneNumber;

    // Remove ALL non-digit characters
    String digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Remove leading '1' if we have 11 digits (country code)
    if (digitsOnly.startsWith('1') && digitsOnly.length == 11) {
      digitsOnly = digitsOnly.substring(1);
    }

    // If no digits remain, return prefix only or empty
    if (digitsOnly.isEmpty) {
      return includePrefix ? PHONE_PREFIX : '';
    }

    // If exactly 10 digits, format completely
    if (digitsOnly.length == 10) {
      final formatted = '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
      return includePrefix ? '$PHONE_PREFIX$formatted' : formatted;
    }

    // If more than 10 digits, take the last 10
    if (digitsOnly.length > 10) {
      digitsOnly = digitsOnly.substring(digitsOnly.length - 10);
      final formatted = '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
      return includePrefix ? '$PHONE_PREFIX$formatted' : formatted;
    }

    // Less than 10 digits
    // If this is user input (returnOriginalIfInvalid), return the original
    if (returnOriginalIfInvalid) {
      return original;
    }

    // Otherwise, format what we have (for API data)
    final prefix = includePrefix ? PHONE_PREFIX : '';
    
    if (digitsOnly.length <= 3) {
      return '$prefix$digitsOnly';
    } else if (digitsOnly.length <= 6) {
      return '$prefix${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3)}';
    } else {
      // 7-9 digits
      return '$prefix${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    }
  }

  /// Convenience method for formatting API responses
  /// Always includes prefix, formats partial numbers
  static String formatPhoneNumberFromApi(String? phoneNumber) {
    return formatPhoneNumber(
      phoneNumber,
      includePrefix: true,
      returnOriginalIfInvalid: false,
    );
  }

  /// Convenience method for formatting user input
  /// Always includes prefix, returns original if incomplete
  static String formatPhoneNumberForInput(String? phoneNumber) {
    return formatPhoneNumber(
      phoneNumber,
      includePrefix: true,
      returnOriginalIfInvalid: true,
    );
  }
}