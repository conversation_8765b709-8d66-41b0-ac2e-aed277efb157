import 'package:neorevv/src/domain/models/commission_revenue_info_model.dart';
import 'package:neorevv/src/domain/repository/commission_repository.dart';

import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';

class CommissionRevenueRepositoryImpl implements CommissionRepository {
  CommissionRevenueRepositoryImpl();

  @override
  Future<List<CommissionRevenueInfoModel>> fetchCommissionByUser(
    String userId,
  ) async {
    final dio = await DioClient.getDio();
    final url = '${APIConfig.commissionRevenueByUser}/$userId';
    final response = await dio.get(url);
    final data = response.data;

    if (data == null) return [];

    if (data is List) {
      return data
          .map(
            (e) =>
                CommissionRevenueInfoModel.fromJson(e as Map<String, dynamic>),
          )
          .toList();
    }
    if (data is Map && data['items'] is List) {
      return (data['items'] as List)
          .map(
            (e) =>
                CommissionRevenueInfoModel.fromJson(e as Map<String, dynamic>),
          )
          .toList();
    }

    if (data is Map) {
      return [
        CommissionRevenueInfoModel.fromJson(data as Map<String, dynamic>),
      ];
    }
    return [];
  }
}
