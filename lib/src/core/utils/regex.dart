class RegExUtils {
  RegExUtils._();

  static final RegExp usPhoneNumber = RegExp(r'^[0-9]{10}$');
  static final RegExp nonNumeric = RegExp(r'[^0-9]');
  static final RegExp emailRegex = RegExp(
    r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@' // local part
    r'((\[[0-9]{1,3}(\.[0-9]{1,3}){3}\])|' // IP address
    r'(([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+' // domain labels
    r'[a-zA-Z]{2,}))$', // TLD
  );

  static final zipCodeRegex = RegExp(r'^\d{5}(-\d{4})?$');
  static final RegExp startsWithUppercase = RegExp(r'^[A-Z]');
  static final RegExp currencyRegexMax2Decimals = RegExp(r'^\d*\.?\d{0,2}$');
  static final RegExp currencyRegexNoDecimals = RegExp(r'^\d*$');
  static final RegExp companyNameRegex = RegExp(r'[A-Za-z.\-\s]');
}
