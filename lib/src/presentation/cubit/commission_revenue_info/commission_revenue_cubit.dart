import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../domain/models/commission_revenue_info_model.dart';
import '../../../domain/repository/commission_repository.dart';

part 'commission_revenue_state.dart';

class CommissionRevenueCubit extends Cubit<CommissionRevenueState> {
  final CommissionRepository repository;

  CommissionRevenueCubit(this.repository)
    : super(const CommissionRevenueInitial());

  /// Call this from UI to load commission data for [userId]
  Future<void> fetchCommissionRevenueDetailsByUser(String userId) async {
    try {
      // keep existing items while loading if any
      List<CommissionRevenueInfoModel> existing = [];
      if (state is CommissionRevenueLoaded) {
        existing = (state as CommissionRevenueLoaded).items;
      } else if (state is CommissionRevenueLoading) {
        existing = (state as CommissionRevenueLoading).items;
      }

      emit(CommissionRevenueLoading(existing));

      final items = await repository.fetchCommissionByUser(userId);
      emit(CommissionRevenueLoaded(items));
    } catch (e, st) {
      // you can log st if needed
      emit(CommissionRevenueError(e.toString()));
    }
  }
}
