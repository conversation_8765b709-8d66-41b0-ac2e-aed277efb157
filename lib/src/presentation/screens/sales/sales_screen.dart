import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '/src/core/enum/sale_review_status.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import '../../../core/config/responsive.dart';
import '../../../core/enum/user_role.dart';
import '../../../core/services/sales_refresh_notifier.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/column_mapping_utils.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../core/utils/format_currency_dollar.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../cubit/filter/filter_cubit.dart';
import '../../cubit/sales_details/sales_details_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/tables/action_button_eye.dart';
import '/src/domain/models/sales.dart';
import '/src/domain/repository/sales_details_repository.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/services/locator.dart';
import 'package:neorevv/src/domain/models/user.dart';

class SalesScreen extends HookWidget {
  final Function(Sales sale)? handleSaleSelection;
  const SalesScreen({Key? key, this.handleSaleSelection}) : super(key: key);

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getSalesBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserCubit>().state.user;
    final userRole = user?.role.toString() ?? "";
    final filterCubit = context.watch<FilterCubit>();
    final brokeragefilterOptions = useState<List<TableFilter>>([]);
    final agentfilterOptions = useState<List<TableFilter>>([]);
    final propertyTypefilterOptions = useState<List<TableFilter>>([]);
    final currentFilters = useState<Map<String, dynamic>>({});
    final ValueNotifier<String?> sortBy = useState("created_at");
    final ValueNotifier<String?> sortOrder = useState("ASC");
    final searchDebouncer = useRef<Timer?>(null);
    final lastSearchValue = useRef<String>('');

    useEffect(() {
      Future.microtask(() async {
        await updateDropDownOptions(
          context,
          filterCubit,
          brokeragefilterOptions,
          user,
          agentfilterOptions,
          propertyTypefilterOptions,
        );
      });
      return null;
    }, []);

    useEffect(() {
      return () {
        searchDebouncer.value?.cancel();
      };
    }, []);

    List<String> getFormattedHeaders() {
      List<String> headers = [salesTransactionIdColumnHeader];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        headers.add(salesBrokerColumnHeader);
      }
      headers.addAll([
        salesAgentColumnHeader,
        // salesPropertyTypeColumnHeader,
        salesPropertyAddressColumnHeader,
        // salesPropertyValueColumnHeader,
        representingColumnHeader,
        representingNameColumnHeader,
        // representingAddressColumnHeader,
        salesListingDateColumnHeader,
        salesDateColumnHeader,
        salesAmountColumnHeader,
        // salesCommissionColumnHeader,
        salesCommissionAmtColumnHeader,
        salesCreatedDateColumnHeader,
        salesReviewStatusColumnHeader,
      ]);

      return headers;
    }

    List<String Function(Sales)> getCellBuilders() {
      List<String Function(Sales)> builders = [(sale) => sale.transactionId];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        builders.add((sale) => sale.brokerName);
      }
      builders.addAll([
        (sale) => sale.agentName,
        // (sale) => sale.propertyType,
        (sale) => sale.propertyAddress,
        // (sale) => formatCurrencyDollar(sale.propertyValue),
        (sale) => sale.representing,
        (sale) => sale.representingName,
        // (sale) => sale.representingAddress,
        (sale) => AppDateFormatter.formatDateMMddyyyy(sale.listingDate),
        (sale) => AppDateFormatter.formatDateMMddyyyy(sale.saleDate),
        (sale) => formatCurrencyDollar(sale.salePrice),
        // (sale) => '${formatCurrencyDollar(sale.commission)}%',
        (sale) => formatCurrencyDollar(sale.commissionAmt),
        (sale) => AppDateFormatter.formatDateMMddyyyy(sale.createdDate),
        (sale) => sale.reviewedStatus?.value ?? SaleReviewStatus.pending.value,
      ]);

      return builders;
    }

    List<Widget Function(BuildContext, Sales)?> getWidgetCellBuilders() {
      List<Widget Function(BuildContext, Sales)?> builders = [null];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        builders.add(null);
      }
      builders.addAll([
        null,
        // null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        // null,
        null,
        null,
        null,
      ]);

      return builders;
    }

    List<bool> getUseWidgetBuilders() {
      List<bool> flags = [false];

      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        flags.add(false);
      }
      flags.addAll([
        false,
        // false,
        false,
        false,
        false,
        false,
        false,
        false,
        false,
        // false,
        false,
        false,
        false,
      ]);

      return flags;
    }

    List<String> getFilterColumnNames() {
      List<String> filterColumns = [];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        filterColumns.add(salesBrokerColumnHeader);
      }
      filterColumns.addAll([
        salesAgentColumnHeader,
        // salesPropertyTypeColumnHeader,
        salesListingDateColumnHeader,
        salesDateColumnHeader,
      ]);

      return filterColumns;
    }

    List<String> getSortAllowedColumns() {
      List<String> headers = [salesTransactionIdColumnHeader];
      if (userRole == UserRole.admin.toString() ||
          userRole == UserRole.platformOwner.toString()) {
        headers.add(salesBrokerColumnHeader);
      }

      headers.addAll([
        salesAgentColumnHeader,
        // salesPropertyTypeColumnHeader,
        salesPropertyAddressColumnHeader,
        // salesPropertyValueColumnHeader,
        representingColumnHeader,
        representingNameColumnHeader,
        // representingAddressColumnHeader,
        salesListingDateColumnHeader,
        salesDateColumnHeader,
        salesAmountColumnHeader,
        // salesCommissionColumnHeader,
        salesCommissionAmtColumnHeader,
        salesCreatedDateColumnHeader,
        salesReviewStatusColumnHeader,
      ]);

      return headers;
    }

    final formattedHeaders = getFormattedHeaders();
    final cellBuilders = getCellBuilders();
    final widgetCellBuilders = getWidgetCellBuilders();
    final useWidgetBuilders = getUseWidgetBuilders();
    final filterColumnNames = getFilterColumnNames();
    final sortAllowedColumns = getSortAllowedColumns();

    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);
    final pageCount = useState<int>(0);
    final searchString = useState<String?>('');
    final currentpage = useState<int>(0);
    final agentName = useState<String?>(null);
    final brokerName = useState<String?>(null);
    final propertyType = useState<String?>(null);
    final salesData = useState<List<Sales>>([]);
    final listingDate = useState<DateTime?>(null);
    final saleDate = useState<DateTime?>(null);
    final totalsales = useState(0);

    return BlocProvider(
      create: (context) {
        return SalesDetailsCubit(locator<SalesDetailsRepository>())
          ..fetchSalesDetails({
            "page": 0,
            "size": 10,
            "sortBy": sortBy.value,
            "sortDirection": sortOrder.value,
            "search": searchString.value,
            "agentName": agentName.value,
            "brokerageName": brokerName.value,
            "propertyType": propertyType.value,
            "listingDate": listingDate.value,
            "saleDate": saleDate.value,
            "userId": user?.userId,
          });
      },
      child: HookBuilder(
        builder: (context) {
          // Listen for refresh notifications from document uploads
          useEffect(() {
            final subscription = SalesRefreshNotifier().refreshStream.listen((
              _,
            ) {
              // Refresh the sales data with current filters and pagination
              if (context.mounted) {
                _fetchSalesData(
                  context,
                  searchString: searchString.value,
                  brokerName: brokerName.value,
                  agentName: agentName.value,
                  propertyType: propertyType.value,
                  listingDate: listingDate.value,
                  saleDate: saleDate.value,
                  page: currentpage.value,
                  brokerageOptions: brokeragefilterOptions.value,
                  agentOptions: agentfilterOptions.value,
                  propertyTypeOptions: propertyTypefilterOptions.value,
                  sortColumn: sortBy.value,
                  sortOrder: sortOrder.value,
                );
              }
            });

            return () {
              subscription.cancel();
            };
          }, []);

          return BlocConsumer<SalesDetailsCubit, SalesDetailsState>(
            listener: (context, state) {
              if (state is SalesDetailsLoaded) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  pageCount.value = state.salesDetailsApi?.totalPages ?? 0;
                  salesData.value = state.salesDetailsApi?.sales ?? [];
                  totalsales.value = state.salesDetailsApi?.totalElements ?? 0;
                });
              }
            },
            builder: (context, state) {
              bool isLoading = state is SalesDetailsLoading;
              String? errorMessage;

              if (state is SalesDetailsError) {
                errorMessage = state.message;
              }

              List<Sales> currentSalesData = [];
              int currentPageCount = 0;
              int currentTotalSales = 0;

              if (state is SalesDetailsLoaded) {
                currentSalesData = state.salesDetailsApi?.sales ?? [];
                currentPageCount = state.salesDetailsApi?.totalPages ?? 0;
                currentTotalSales = state.salesDetailsApi?.totalElements ?? 0;
              } else {
                // Use stored values from useState
                currentSalesData = salesData.value;
                currentPageCount = pageCount.value;
                currentTotalSales = totalsales.value;
              }

              void handleSort(String columnName, bool ascending) async {
                // Handle sort reset - when columnName is empty, reset to default
                String backendColumnName;
                if (columnName.isEmpty) {
                  sortColumn.value = '';
                  sortAscending.value = false;
                  sortBy.value = "created_at";
                  sortOrder.value = "ASC";
                  backendColumnName = "created_at";
                } else {
                  sortColumn.value = columnName;
                  sortAscending.value = ascending;
                  backendColumnName = _getBackendColumnName(columnName);
                  sortBy.value = backendColumnName;
                  sortOrder.value = ascending ? 'ASC' : 'DESC';
                }

                if (context.mounted) {
                  await _fetchSalesData(
                    context,
                    searchString: searchString.value,
                    brokerName: brokerName.value,
                    agentName: agentName.value,
                    propertyType: propertyType.value,
                    listingDate: listingDate.value,
                    saleDate: saleDate.value,
                    page: currentpage.value,
                    brokerageOptions: brokeragefilterOptions.value,
                    agentOptions: agentfilterOptions.value,
                    propertyTypeOptions: propertyTypefilterOptions.value,
                    sortColumn: backendColumnName,
                    sortOrder: sortOrder.value,
                  );
                }
              }

              if (errorMessage != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 16),
                      Text(
                        errorMessage.contains(unauthorizedStatus) ||
                                errorMessage.contains(unauthorizedText)
                            ? authenticationRequired
                            : errorLoadingData,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          if (context.mounted) {
                            await _fetchSalesData(
                              context,
                              searchString: searchString.value,
                              brokerName: brokerName.value,
                              agentName: agentName.value,
                              propertyType: propertyType.value,
                              listingDate: listingDate.value,
                              saleDate: saleDate.value,
                              page: currentpage.value,
                              brokerageOptions: brokeragefilterOptions.value,
                              agentOptions: agentfilterOptions.value,
                              propertyTypeOptions:
                                  propertyTypefilterOptions.value,
                              sortColumn: sortBy.value,
                              sortOrder: sortOrder.value,
                            );
                          }
                        },
                        child: const Text(retry),
                      ),
                    ],
                  ),
                );
              }

              final isDesktop = Responsive.isDesktop(context);

              return ValueListenableBuilder(
                valueListenable: salesData,
                builder: (context, value, child) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: CustomDataTableWidget<Sales>(
                          data: value,
                          title: salesTab,
                          titleIcon: "$iconAssetpath/dollar_icon.png",
                          searchHint: searchHint,
                          searchFn: (sale) =>
                              sale.transactionId +
                              sale.brokerName +
                              sale.agentName +
                              // sale.propertyType +
                              sale.propertyAddress +
                              sale.representing +
                              sale.representingName +
                              // sale.representingAddress +
                              // AppDateFormatter.formatDateMMddyyyy(
                              //   sale.listingDate,
                              // ) +
                              AppDateFormatter.formatDateMMddyyyy(
                                sale.saleDate,
                              ) +
                              sale.salePrice.toString() +
                              // sale.commission.toString() +
                              sale.commissionAmt.toString() +
                              AppDateFormatter.formatDateMMddyyyy(
                                sale.createdDate,
                              ),
                          filterColumnNames: filterColumnNames,
                          filterValueExtractors: {
                            salesBrokerColumnHeader: (sale) => sale.brokerName,
                            salesAgentColumnHeader: (sale) => sale.agentName,
                            // salesPropertyTypeColumnHeader: (sale) =>
                            //     sale.propertyType,
                            salesListingDateColumnHeader: (sale) =>
                                AppDateFormatter.formatDateMMddyyyy(
                                  sale.listingDate,
                                ),
                            salesDateColumnHeader: (sale) =>
                                AppDateFormatter.formatDateMMddyyyy(
                                  sale.saleDate,
                                ),
                          },
                          filterSearchConfig: {
                            salesBrokerColumnHeader: true,
                            salesAgentColumnHeader: true,
                            // salesPropertyTypeColumnHeader: true,
                            // ...
                          },
                          showCrossButtonForFilter: const {
                            salesBrokerColumnHeader: true,
                            salesAgentColumnHeader: true,
                            // salesPropertyTypeColumnHeader: true,
                            salesListingDateColumnHeader: true,
                            salesDateColumnHeader: true,
                          },

                          dateFilterColumns: const [
                            salesListingDateColumnHeader,
                            salesDateColumnHeader,
                            salesCreatedDateColumnHeader,
                          ],
                          filterOptions: {
                            // if (propertyTypefilterOptions.value.isNotEmpty)
                            //   salesPropertyTypeColumnHeader:
                            //       propertyTypefilterOptions.value,
                            if (brokeragefilterOptions.value.isNotEmpty &&
                                (userRole == UserRole.admin.toString() ||
                                    userRole ==
                                        UserRole.platformOwner.toString()))
                              salesBrokerColumnHeader:
                                  brokeragefilterOptions.value,
                            if (agentfilterOptions.value.isNotEmpty)
                              salesAgentColumnHeader: agentfilterOptions.value,
                          },
                          filterOnChangeConfig: {
                            salesBrokerColumnHeader: true,
                            salesAgentColumnHeader: false,
                            // salesPropertyTypeColumnHeader: false,
                            salesListingDateColumnHeader: false,
                            salesDateColumnHeader: false,
                          },
                          filterOptionValueExtractors: {
                            // salesPropertyTypeColumnHeader: (dynamic item) =>
                            //     item.value?.toString() ?? '',
                            salesBrokerColumnHeader: (dynamic item) =>
                                item.value?.toString() ?? '',
                            salesAgentColumnHeader: (dynamic item) =>
                                item.value?.toString() ?? '',
                          },
                          onFilterChange:
                              (
                                filterKey,
                                selectedValue, [
                                clearDependentFilters,
                                searchString,
                              ]) async {
                                // If the filterKey is agentRelatedBrokerage, fetch agent filter options with selectedValue
                                if (filterKey == salesBrokerColumnHeader) {
                                  // Clear the dependent agent filter when broker changes
                                  if (clearDependentFilters != null) {
                                    clearDependentFilters([
                                      salesAgentColumnHeader,
                                    ]);
                                  }

                                  await filterCubit.getAgentFilterOptions(
                                    userId: user?.userId,
                                    selectedValueParam: selectedValue,
                                    includeSelf: true,
                                  );
                                  final state = filterCubit.state;
                                  if (state is FilterLoaded) {
                                    agentfilterOptions.value =
                                        state.filterOptions;
                                  }
                                }
                              },
                          columnNames: formattedHeaders,
                          cellBuilders: cellBuilders,
                          widgetCellBuilders: widgetCellBuilders,
                          useWidgetBuilders: useWidgetBuilders,
                          showSortIconColumns: sortAllowedColumns,
                          actionBuilders: [
                            (context, sale) => ActionButtonEye(
                              onPressed: () => _onSaleAction(context, sale),
                              isCompact: true,
                              isMobile: false,
                            ),
                          ],

                          mobileCardBuilder: (context, sale) =>
                              _buildMobileSaleCard(sale, context, userRole),
                          onSort: handleSort,
                          emptyStateMessage: _getEmptyMessage(
                            state,
                            currentSalesData,
                          ),
                          useMinHeight: false,
                          pageCount: pageCount.value,
                          totalElements: totalsales.value,
                          isLoading: state is SalesDetailsLoading,
                          // TODO: Remove later

                          // onDateFilterMultipleChanged: (selectedDates) async {
                          //   DateTime? listingDateValue;
                          //   DateTime? saleDateValue;
                          //   if (selectedDates.containsKey(
                          //     salesListingDateColumnHeader,
                          //   )) {
                          //     listingDateValue =
                          //         selectedDates[salesListingDateColumnHeader];
                          //     listingDate.value = listingDateValue;
                          //   }
                          //   if (selectedDates.containsKey(salesDateColumnHeader)) {
                          //     saleDateValue = selectedDates[salesDateColumnHeader];
                          //     saleDate.value = saleDateValue;
                          //   }
                          //   await _fetchSalesData(
                          //     context,
                          //     searchString: searchString.value,
                          //     page: currentpage.value,
                          //     brokerName: brokerName.value,
                          //     agentName: agentName.value,
                          //     propertyType: propertyType.value,
                          //     saleDate: saleDateValue,
                          //     listingDate: listingDateValue,
                          //   );
                          // },
                          currentPageIndex: currentpage.value,
                          onAllFiltersChanged: (allFilters) async {
                            // Store current filters
                            currentFilters.value = allFilters;
                            currentpage.value = 0;
                            // Extract individual filter values
                            String? brokerNameValue;
                            String? agentNameValue;
                            String? propertyTypeValue;
                            DateTime? listingDateValue;
                            DateTime? saleDateValue;

                            // Extract dropdown filters
                            if (allFilters.containsKey(
                              salesBrokerColumnHeader,
                            )) {
                              brokerNameValue =
                                  allFilters[salesBrokerColumnHeader]
                                      ?.toString();
                            }
                            brokerName.value = brokerNameValue;
                            if (allFilters.containsKey(
                              salesAgentColumnHeader,
                            )) {
                              agentNameValue =
                                  allFilters[salesAgentColumnHeader]
                                      ?.toString();
                            }
                            agentName.value = agentNameValue;
                            // if (allFilters.containsKey(
                            //   salesPropertyTypeColumnHeader,
                            // )) {
                            //   propertyTypeValue =
                            //       allFilters[salesPropertyTypeColumnHeader]
                            //           ?.toString();
                            // }
                            // propertyType.value = propertyTypeValue;
                            // Extract date filters
                            if (allFilters.containsKey(
                              salesListingDateColumnHeader,
                            )) {
                              listingDateValue =
                                  allFilters[salesListingDateColumnHeader]
                                      as DateTime?;
                            }
                            listingDate.value = listingDateValue;
                            if (allFilters.containsKey(salesDateColumnHeader)) {
                              saleDateValue =
                                  allFilters[salesDateColumnHeader]
                                      as DateTime?;
                            }
                            saleDate.value = saleDateValue;
                            // Call API with all filters
                            await _fetchSalesData(
                              context,
                              brokerageOptions: brokeragefilterOptions.value,
                              agentOptions: agentfilterOptions.value,
                              propertyTypeOptions:
                                  propertyTypefilterOptions.value,
                              searchString: searchString.value,
                              page: 0,
                              brokerName: brokerNameValue,
                              agentName: agentNameValue,
                              propertyType: propertyTypeValue,
                              saleDate: saleDateValue,
                              listingDate: listingDateValue,
                              sortColumn: sortBy.value,
                              sortOrder: sortOrder.value,
                            );
                          },

                          handleTableSearch: (value) async {
                            // Cancel any pending search
                            searchDebouncer.value?.cancel();

                            // Store the search value immediately for UI feedback
                            searchString.value = value;

                            // Don't search if value hasn't changed
                            if (lastSearchValue.value == value) {
                              return;
                            }
                            lastSearchValue.value = value;
                            // Debounce: wait 300ms before making the API call
                            searchDebouncer.value = Timer(
                              const Duration(milliseconds: 300),
                              () async {
                                currentpage.value = 0;
                                await _fetchSalesData(
                                  context,
                                  brokerageOptions:
                                      brokeragefilterOptions.value,
                                  agentOptions: agentfilterOptions.value,
                                  propertyTypeOptions:
                                      propertyTypefilterOptions.value,
                                  page: 0,
                                  searchString: value,
                                  brokerName: brokerName.value,
                                  agentName: agentName.value,
                                  propertyType: propertyType.value,
                                  saleDate: saleDate.value,
                                  listingDate: listingDate.value,
                                  sortColumn: sortBy.value,
                                  sortOrder: sortOrder.value,
                                );
                              },
                            );
                          },
                          handlePagination: (page) async {
                            currentpage.value = page;
                            await _fetchSalesData(
                              context,
                              brokerageOptions: brokeragefilterOptions.value,
                              agentOptions: agentfilterOptions.value,
                              propertyTypeOptions:
                                  propertyTypefilterOptions.value,
                              searchString: searchString.value,
                              page: page,
                              brokerName: brokerName.value,
                              agentName: agentName.value,
                              propertyType: propertyType.value,
                              saleDate: saleDate.value,
                              listingDate: listingDate.value,
                              sortColumn: sortBy.value,
                              sortOrder: sortOrder.value,
                            );
                          },
                          onResetFilters: ({required bool isFilterOptionOnlyReload}) async {
                            if (isFilterOptionOnlyReload) {
                              //reset filter options
                              await updateDropDownOptions(
                                context,
                                filterCubit,
                                brokeragefilterOptions,
                                user,
                                agentfilterOptions,
                                propertyTypefilterOptions,
                              );
                            } else {
                              //reset filter options
                              await updateDropDownOptions(
                                context,
                                filterCubit,
                                brokeragefilterOptions,
                                user,
                                agentfilterOptions,
                                propertyTypefilterOptions,
                              );
                              searchString.value =
                                  ''; // String? type (empty string)
                              currentpage.value =
                                  0; // int type (reset to first page, usually 0 or 1)
                              brokerName.value =
                                  null; // String? type (null for no filter)
                              agentName.value =
                                  null; // String? type (null for no filter)
                              propertyType.value =
                                  null; // String? type (null for no filter)
                              saleDate.value =
                                  null; // DateTime? type (null for no filter)
                              listingDate.value =
                                  null; // DateTime? type (null for no filter)
                              sortBy.value =
                                  'created_at'; // String? type (default sort column, or set to '')
                              sortOrder.value = 'ASC';
                              await _fetchSalesData(
                                context,
                                brokerageOptions: brokeragefilterOptions.value,
                                agentOptions: agentfilterOptions.value,
                                propertyTypeOptions:
                                    propertyTypefilterOptions.value,
                                searchString: searchString.value,
                                page: 0,
                                brokerName: brokerName.value,
                                agentName: agentName.value,
                                propertyType: propertyType.value,
                                saleDate: saleDate.value,
                                listingDate: listingDate.value,
                                sortColumn: sortBy.value,
                                sortOrder: sortOrder.value,
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Future<void> updateDropDownOptions(
    BuildContext context,
    FilterCubit filterCubit,
    ValueNotifier<List<TableFilter>> brokeragefilterOptions,
    User? user,
    ValueNotifier<List<TableFilter>> agentfilterOptions,
    ValueNotifier<List<TableFilter>> propertyTypefilterOptions,
  ) async {
    if (context.mounted) {
      await filterCubit.getBrokerageFilterOptions();
      final brokerageState = filterCubit.state;
      if (brokerageState is FilterLoaded) {
        brokeragefilterOptions.value = brokerageState.filterOptions;
      }

      await filterCubit.getAgentFilterOptions(
        userId: user?.userId,
        selectedValueParam: null,
        includeSelf: true,
      );
      final agentState = filterCubit.state;
      if (agentState is FilterLoaded) {
        agentfilterOptions.value = agentState.filterOptions;
      }

      await filterCubit.getProperyTypeFilterOptions();
      final state = filterCubit.state;
      if (state is FilterLoaded) {
        propertyTypefilterOptions.value = state.filterOptions;
      }
    }
  }

  Future<void> _fetchSalesData(
    BuildContext context, {
    required String? searchString,
    required String? brokerName,
    required String? agentName,
    required String? propertyType,
    required DateTime? listingDate,
    required DateTime? saleDate,
    required int page,
    required List<TableFilter> brokerageOptions,
    required List<TableFilter> agentOptions,
    required List<TableFilter> propertyTypeOptions,
    required String? sortColumn,
    required String? sortOrder,
  }) async {
    String? userId = context.mounted
        ? context.read<UserCubit>().state.user?.userId
        : null;
    if (context.mounted && userId != null && userId != '') {
      String? mapIdToName(String? id, List<TableFilter> options) {
        if (id == null || id.isEmpty || options.isEmpty) return null;
        return options
                .firstWhere(
                  (f) => f.id == id,
                  orElse: () => TableFilter(id: '', key: '', value: ''),
                )
                .value
                .isEmpty
            ? null
            : options.firstWhere((f) => f.id == id).value;
      }

      String formattedListingDate = listingDate != null
          ? AppDateFormatter.formatDateyyyyMMdd(listingDate)
          : '';
      String formattedSaleDate = saleDate != null
          ? AppDateFormatter.formatDateyyyyMMdd(saleDate)
          : '';

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": sortColumn,
        "sortDirection": sortOrder,
        "search": searchString,
        "agentName": mapIdToName(agentName, agentOptions),
        "brokerageName": mapIdToName(brokerName, brokerageOptions),
        "propertyType": propertyType,
        "listingDate": formattedListingDate,
        "saleDate": formattedSaleDate,
        "userId": userId,
      };
      await context.read<SalesDetailsCubit>().fetchSalesDetails(payload);
    }
  }

  String _getEmptyMessage(SalesDetailsState state, List<Sales> currentData) {
    if (state is SalesDetailsLoading && currentData.isEmpty) {
      return '';
    }

    if (state is SalesDetailsLoaded && currentData.isEmpty) {
      return noDataAvailable;
    }

    return '';
  }

  void _onSaleAction(BuildContext context, Sales sale) {
    handleSaleSelection?.call(sale);
  }

  Widget _buildMobileSaleCard(
    Sales sale,
    BuildContext context,
    String userRole,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SelectableText(
                sale.transactionId,
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              statusTextView(sale),
            ],
          ),
          const SizedBox(height: 8),

          if (userRole == UserRole.admin.toString() ||
              userRole == UserRole.platformOwner.toString())
            SelectableText('$salesBrokerColumnHeader: ${sale.brokerName}'),
          SelectableText('$salesAgentColumnHeader: ${sale.agentName}'),
          // Text('$salesPropertyTypeColumnHeader: ${sale.propertyType}'),
          SelectableText(
            '$salesPropertyAddressColumnHeader: ${sale.propertyAddress}',
          ),
          // SelectableText(
          //   '$salesPropertyValueColumnHeader: ${formatCurrencyDollar(sale.propertyValue)}',
          // ),
          SelectableText('$representingColumnHeader: ${sale.representing}'),
          SelectableText(
            '$representingNameColumnHeader: ${sale.representingName}',
          ),
          // SelectableText(
          //   '$representingAddressColumnHeader: ${sale.representingAddress}',
          // ),
          SelectableText(
            '$salesListingDateColumnHeader: ${AppDateFormatter.formatDateMMddyyyy(sale.listingDate)}',
          ),
          SelectableText(
            '$salesDateColumnHeader: ${AppDateFormatter.formatDateMMddyyyy(sale.saleDate)}',
          ),

          SelectableText(
            '$salesAmountColumnHeader: ${formatCurrencyDollar(sale.salePrice)}',
          ),
          // Text(
          //   '$salesCommissionColumnHeader: ${formatCurrencyDollar(sale.commission)}',
          // ),
          SelectableText(
            '$salesCommissionAmtColumnHeader: ${formatCurrencyDollar(sale.commissionAmt)}',
          ),
          SelectableText(
            '$salesCreatedDateColumnHeader: ${AppDateFormatter.formatDateMMddyyyy(sale.createdDate)}',
          ),
          SelectableText(
            '$salesReviewStatusColumnHeader: ${sale.reviewedStatus?.value}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onSaleAction(context, sale),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget statusTextView(Sales sale) {
    // List of colors for property types (add more as needed)
    final List<Color> propertyTypeColors = [
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.brown,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
    ];

    // Get property type value
    final String? type = sale.propertyType;

    // If value is empty or null, return nothing
    if (type == null || type.isEmpty) {
      return SizedBox.shrink();
    }

    // Get a color index based on hashCode (so new types get a color)
    final int colorIndex = type.hashCode.abs() % propertyTypeColors.length;
    final Color color = propertyTypeColors[colorIndex];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: SelectableText(
        type,
        style: AppFonts.mediumTextStyle(12, color: color),
      ),
    );
  }
}
