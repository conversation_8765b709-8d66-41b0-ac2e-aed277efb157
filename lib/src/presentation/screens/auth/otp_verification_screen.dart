import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/config/app_strings.dart' as app_strings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../shared/components/elevated_button.dart';

class OtpVerificationScreen extends HookWidget {
  const OtpVerificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // State for OTP digits
    final otpControllers = List.generate(
      4,
      (index) => useTextEditingController(),
    );
    final focusNodes = List.generate(4, (index) => useFocusNode());

    // Timer state
    final timerSeconds = useState(41);
    final timerActive = useState(true);

    // Timer effect
    useEffect(() {
      Timer? timer;
      if (timerActive.value) {
        timer = Timer.periodic(const Duration(seconds: 1), (t) {
          if (timerSeconds.value > 0) {
            timerSeconds.value--;
          } else {
            timerActive.value = false;
            t.cancel();
          }
        });
      }
      return timer?.cancel;
    }, [timerActive.value]);

    final isMobile = Responsive.isMobile(context);

    // Main content builder
    Widget buildContent() {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: defaultPadding * 2),
          // Title
          Text(
            'Check your email',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w700, // Bold
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: defaultPadding),
          // Subtitle
          Text(
            'Please enter the four digit verification code we sent to',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppTheme.secondaryTextColor,
            ),
          ),
          Text(
            '<EMAIL>', // Placeholder or pass via arguments
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w700, // Bold
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: defaultPadding * 3),

          // OTP Inputs
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(4, (index) {
              return Container(
                width: 60,
                height: 60,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3F3F3), // Light grey background
                  borderRadius: BorderRadius.circular(30), // Circular
                  border: Border.all(
                    color: focusNodes[index].hasFocus
                        ? AppTheme.roundIconColor
                        : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Center(
                  child: TextFormField(
                    controller: otpControllers[index],
                    focusNode: focusNodes[index],
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(1),
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      counterText: '',
                    ),
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.roundIconColor,
                    ),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        if (index < 3) {
                          FocusScope.of(
                            context,
                          ).requestFocus(focusNodes[index + 1]);
                        } else {
                          focusNodes[index].unfocus();
                        }
                      } else {
                        if (index > 0) {
                          FocusScope.of(
                            context,
                          ).requestFocus(focusNodes[index - 1]);
                        }
                      }
                    },
                  ),
                ),
              );
            }),
          ),

          const SizedBox(height: defaultPadding * 3),

          // Confirm Button
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: app_strings.confirmLabel,
              backgroundColor: AppTheme.roundIconColor,
              foregroundColor: Colors.white,
              borderRadius: 30, // Rounded pill shape
              padding: const EdgeInsets.symmetric(
                vertical: defaultPadding * 1.5,
              ),
              useMinSize: false,
              onPressed: () {
                // TODO: Implement OTP verification logic
                // final otp = otpControllers.map((c) => c.text).join();
                // Log or handle OTP
                // context.go(AppRoutes.dashboard.path); // Example navigation
              },
            ),
          ),
          const SizedBox(height: defaultPadding * 2),

          // Timer / Resend
          Text(
            timerSeconds.value > 0
                ? "Didn't get the email? Resent in 00:${timerSeconds.value.toString().padLeft(2, '0')}"
                : "Didn't get the email? Resend now",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: defaultPadding * 2),

          // Back Button
          TextButton.icon(
            onPressed: () {
              if (context.canPop()) {
                context.pop();
              } else {
                context.go(AppRoutes.login.path);
              }
            },
            icon: const Icon(
              Icons.arrow_back,
              size: 16,
              color: AppTheme.secondaryTextColor,
            ),
            label: Text(
              'back',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppTheme.secondaryTextColor, // Matches design grey
              ),
            ),
          ),
        ],
      );
    }

    return Center(
      child: Container(
        width: isMobile ? double.infinity : 550,
        padding: const EdgeInsets.all(defaultPadding * 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: buildContent(),
      ),
    );
  }
}
