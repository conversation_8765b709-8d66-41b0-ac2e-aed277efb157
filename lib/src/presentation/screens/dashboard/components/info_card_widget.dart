import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';

class InfoCardWidget extends StatelessWidget {
  final String title;
  final String value;
  final String icon;
  final Color iconColor;
  final String subtitle;
  final String additionalInfo;
  final String? footerIcon;

  const InfoCardWidget({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.iconColor,
    required this.subtitle,
    required this.additionalInfo,
    required this.footerIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);
    final bool isDesktop = Responsive.isDesktop(context);
    final double iconSize = isMobile || Responsive.isTablet(context) ? 45 : 65;
    final double screenWidth = MediaQuery.of(context).size.width;
    double _scaleFont(double min, double max) {
      const double minWidth = 360; // width at which font = min
      const double maxWidth = 1600; // width at which font = max
      final t = ((screenWidth - minWidth) / (maxWidth - minWidth)).clamp(
        0.0,
        1.0,
      );
      return min + (max - min) * t;
    }

    final double titleFont = _scaleFont(14, 18); // title scales modestly
    final double valueFont = _scaleFont(14, 27);
    final double subTitleFont = _scaleFont(10, 14); // title scales modestly
    return Container(
      // padding: const EdgeInsets.only(top: defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title section

          // Value and icon section
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,

                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppFonts.mediumTextStyle(
                            titleFont,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        AutoSizeText(
                          value,
                          style: AppFonts.boldTextStyle(
                            valueFont,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    constraints: BoxConstraints(
                      maxWidth: iconSize,
                      maxHeight: iconSize,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      image: DecorationImage(image: AssetImage(icon)),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Footer section
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: AppTheme.borderColor, width: 1),
              ),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: defaultMargin,
              vertical: defaultPadding / 2, // Add vertical padding
            ),
            child: Row(
              children: [
                // Left section with calendar icon and subtitle
                Expanded(
                  flex: 3,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        '$iconAssetpath/calendar.png',
                        width: 16,
                        height: 16,
                        color: AppTheme.primaryTextColor,
                      ),
                      const SizedBox(width: 5),
                      Flexible(
                        child: Text(
                          subtitle,
                          style: AppFonts.mediumTextStyle(
                            subTitleFont,
                            color: AppTheme.primaryTextColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 8),

                // Right section with divider and additional info
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: const EdgeInsets.only(left: 8),
                    decoration: const BoxDecoration(
                      border: Border(
                        left: BorderSide(color: AppTheme.borderColor, width: 1),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (footerIcon != null && footerIcon!.isNotEmpty) ...[
                          Image.asset(
                            footerIcon!,
                            width: 16,
                            height: 16,
                            color: AppTheme.primaryTextColor,
                          ),
                          const SizedBox(width: 5),
                        ],
                        Flexible(
                          child: Text(
                            additionalInfo,
                            style: AppFonts.boldTextStyle(
                              subTitleFont,
                              color: AppTheme.primaryTextColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
