import 'package:flutter/material.dart';

import '../theme/app_theme.dart';
import 'app_snack_bar.dart';

class SuccessSnackBar {
  const SuccessSnackBar._();

  static Future<void> showSnackBar(
    BuildContext context,
    String message,
    SnackBarType type, {
    Duration? timeout = const Duration(seconds: 3),
  }) async {
    if (context.mounted) {
      await ScaffoldMessenger.of(context)
          .showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: _getBackgroundColor(type),
              duration: timeout ?? const Duration(seconds: 3),
            ),
          )
          .closed;
    }
  }

  static Color _getBackgroundColor(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return AppTheme.successColor;
      case SnackBarType.error:
        return AppTheme.errorColor;
      case SnackBarType.warning:
        return AppTheme.warningColor;
      case SnackBarType.info:
        return Colors.blue;
    }
  }
}
