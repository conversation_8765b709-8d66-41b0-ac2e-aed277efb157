part of 'sales_details_cubit.dart';

abstract class SalesDetailsState {}

final class SalesDetailsInitial extends SalesDetailsState {}

final class SalesDetailsLoading extends SalesDetailsState {}

final class SalesDetailsLoaded extends SalesDetailsState {
  final SalesDetailsApi? salesDetailsApi;

  SalesDetailsLoaded({required this.salesDetailsApi});
}

final class SalesDetailsError extends SalesDetailsState {
  final String message;
  final int? statusCode;

  SalesDetailsError({required this.message, this.statusCode});

  List<Object?> get props => [message, statusCode];
}

final class SalesDetailsClosingDocumentLoaded extends SalesDetailsState {
  final SalesClosingDocDetailsApi? salesClosingDocDetailsApi;

  SalesDetailsClosingDocumentLoaded({required this.salesClosingDocDetailsApi});
}

final class SalesClosingDocumentFileUploaded extends SalesDetailsState {
  final String message;
  final String salesId;
  final List<String> uploadedFiles;
  final String? currentUploadingFile;
  final int? currentIndex;
  final int? totalFiles;
  final bool isComplete;

  SalesClosingDocumentFileUploaded({
    required this.message,
    required this.salesId,
    this.uploadedFiles = const [],
    this.currentUploadingFile,
    this.currentIndex,
    this.totalFiles,
    this.isComplete = false,
  });

  List<Object?> get props => [message, salesId, currentUploadingFile, currentIndex, totalFiles, isComplete];
}

final class SalesFileUploaded extends SalesDetailsState {
  final SalesDocumentsType documentType;
  final String salesId;

  SalesFileUploaded({required this.documentType, required this.salesId});

  List<Object> get props => [documentType, salesId];
}

final class SalesClosingDocumentEdited extends SalesDetailsState {
  final String message;

  SalesClosingDocumentEdited({required this.message});

  List<Object> get props => [message];
}

final class SalesClosingDocumentUploadStatusUpdated extends SalesDetailsState {
  final String message;

  SalesClosingDocumentUploadStatusUpdated({required this.message});

  List<Object> get props => [message];
}

final class SalesClosingDocumentUploadCommissionUpdated
    extends SalesDetailsState {
  final String message;

  SalesClosingDocumentUploadCommissionUpdated({required this.message});

  List<Object> get props => [message];
}

final class SalesDetailsTransactionTypesLoaded extends SalesDetailsState {
  final List<TransactionType> transactionTypeList;
  SalesDetailsTransactionTypesLoaded({required this.transactionTypeList});
}

final class SalesDetailsLeadSourceLoaded extends SalesDetailsState {
  final List<LeadSource> leadSourceList;
  SalesDetailsLeadSourceLoaded({required this.leadSourceList});
}

final class SalesDetailsRepresentingLoaded extends SalesDetailsState {
  final List<RepresentingTypes> representingTypes;
  SalesDetailsRepresentingLoaded({required this.representingTypes});
}

class SalesDetailsFormState extends SalesDetailsState {
  final TransactionType? selectedTransactionType;
  final RepresentingTypes? selectedRepresentingType;
  final LeadSource? selectedLeadSource;
  final String? closingDocFileName;
  final String? closingDocFileUrl;
  final List<FileDetails>? documentList;

  SalesDetailsFormState({
    this.selectedTransactionType,
    this.selectedRepresentingType,
    this.selectedLeadSource,
    this.closingDocFileName,
    this.closingDocFileUrl,
    this.documentList,
  });

  SalesDetailsFormState copyWith({
    TransactionType? selectedTransactionType,
    RepresentingTypes? selectedRepresentingType,
    LeadSource? selectedLeadSource,
    String? closingDocFileName,
    String? closingDocFileUrl,
    List<FileDetails>? documentList,
  }) {
    return SalesDetailsFormState(
      selectedTransactionType:
          selectedTransactionType ?? this.selectedTransactionType,
      selectedRepresentingType:
          selectedRepresentingType ?? this.selectedRepresentingType,
      selectedLeadSource: selectedLeadSource ?? this.selectedLeadSource,
      closingDocFileName: closingDocFileName ?? this.closingDocFileName,
      closingDocFileUrl: closingDocFileUrl ?? this.closingDocFileUrl,
      documentList: documentList ?? this.documentList,
    );
  }
}
