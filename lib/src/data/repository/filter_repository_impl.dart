import 'package:dio/dio.dart';
import '../../core/config/app_strings.dart';
import '/src/core/network/api_config.dart';

import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/filter/table_filter.dart';
import '../../domain/repository/filter_repository.dart';

class FilterRepositoryImpl extends FilterRepository {
  FilterRepositoryImpl();
  static const String propertyTypeFilterUrl = APIConfig.propertyTypeFilter;
  static const String brokerFilterOptionsUrl = APIConfig.brokerageFilterOptions;
  static const String agentFilterOptionsUrl = APIConfig.agentFilterOptions;
  static const String userStatusFilterUrl = APIConfig.userStatusFilter;
  static const String countriesFilterUrl = APIConfig.countries;
  static const String statesFilterUrl = APIConfig.states;
  static const String citiesFilterUrl = APIConfig.cities;

  @override
  Future<List<TableFilter>> getBrokerageFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(brokerFilterOptionsUrl);

      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        // filterOptions.sort((a, b) => a.value.compareTo(b.value));
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getAgentFilterOptions(
    String? userId,
    String? selectedKey,
    bool? includeSelf,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        agentFilterOptionsUrl,
        queryParameters: {
          "requestingId": userId,
          "associatedBrokerageId": selectedKey,
          "includeSelf": includeSelf,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        // filterOptions.sort((a, b) => a.value.compareTo(b.value));
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getProperyTypeFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(propertyTypeFilterUrl);
      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e as Map<String, dynamic>))
            .toList();
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, 'errorFetchingFilterOptions');
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getUserStatusFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(userStatusFilterUrl);
      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e as Map<String, dynamic>))
            .toList();
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, 'errorFetchingFilterOptions');
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getCountiresFilterOptions() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(countriesFilterUrl);
      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e as Map<String, dynamic>))
            .toList();
        //   filterOptions.addAll([
        //   TableFilter(id: "2", key: "CAN", value: "Canada"),
        //   TableFilter(id: "3", key: "GER", value: "Germany"),
        //   TableFilter(id: "4", key: "JPN", value: "Japan"),
        // ]);
        // filterOptions.sort((a, b) => a.value.compareTo(b.value));
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, 'Error fetching Countires');
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getStatesFilterOptions(
    String? selectedCountry,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        statesFilterUrl,
        queryParameters: {"countryId": selectedCountry},
      );
      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        // filterOptions.sort((a, b) => a.value.compareTo(b.value));
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<TableFilter>> getCitiesFilterOptions(
    String? selectedState,
    String? searchString,
  ) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(
        citiesFilterUrl,
        queryParameters: {"stateId": selectedState, "letters": searchString},
      );

      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => TableFilter.fromJson(e))
            .toList();
        // filterOptions.sort((a, b) => a.value.compareTo(b.value));
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
