import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import '/src/core/utils/app_snack_bar.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/navigation/web_router.dart';
import '../../../core/services/locator.dart';
import '../../../core/services/sales_refresh_notifier.dart';
import '../../../core/services/upload_service.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/models/upload_task_model.dart';
import '../../cubit/upload/upload_cubit.dart';
import 'customDialogues/alert_dialogue.dart';
import 'upload_completion_dialog.dart';

/// A global widget that shows upload progress across all screens
class GlobalUploadIndicator extends StatefulWidget {
  const GlobalUploadIndicator({super.key});

  @override
  State<GlobalUploadIndicator> createState() => _GlobalUploadIndicatorState();
}

class _GlobalUploadIndicatorState extends State<GlobalUploadIndicator> {
  final Set<String> _shownCompletionDialogs = {};
  Offset _position = const Offset(
    50,
    50,
  ); // Initial position (right: 50, bottom: 50)

  /*
  // Define routes where the indicator should be hidden
  final List<String> _hiddenRoutes = [
    AppRoutes.uploadDocument.path,
    // Add other routes if needed
  ];

  bool _shouldHideIndicator(BuildContext context) {
    try {
      final currentRoute = GoRouterState.of(context).matchedLocation;
      return _hiddenRoutes.any((route) => currentRoute.contains(route));
    } catch (e) {
      return false;
    }
  }
  */

  @override
  Widget build(BuildContext context) {
    // Hide indicator on specific routes
    // if (_shouldHideIndicator(context)) {
    //   return const SizedBox.shrink();
    // }

    return BlocListener<UploadCubit, UploadState>(
      listener: (context, state) {
        // Check if any task just completed
        if (state is UploadTaskUpdated) {
          final task = state.task;

          // Check if user has navigated away by comparing current route with originating route
          // Use post-frame callback to avoid accessing inherited widgets during build
          if (task.originatingRoute != null && !task.userNavigatedAway) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!mounted) return;

              final currentRoute = GoRouterState.of(context).matchedLocation;
              if (currentRoute != task.originatingRoute) {
                // Mark that user has navigated away
                final uploadService = locator<UploadService>();
                uploadService.markUserNavigatedAway(task.taskId, currentRoute);
              }
            });
          }
          // If task failed and user navigated away, show dialog
          if (task.status == UploadTaskStatus.failed &&
              task.userNavigatedAway) {
            _showFailureDialog(context, task);
          }

          // If task completed and user navigated away, show dialog
          if (task.status == UploadTaskStatus.completed &&
              task.userNavigatedAway &&
              !_shownCompletionDialogs.contains(task.taskId)) {
            _shownCompletionDialogs.add(task.taskId);
            _showCompletionDialog(context, task);
          } else if (task.status == UploadTaskStatus.completed) {
            // Navigate to review screen
            // delay to show the success snackbar
            Future.delayed(Duration(seconds: 2), () {
              context.go(
                AppRoutes.saleReviewDoc.path,
                extra: {'salesId': task.salesId, 'isAgentEdit': true},
              );
            });

            // Remove the task from the list
            locator<UploadService>().removeTask(task.taskId);
          }
        }
      },
      child: BlocBuilder<UploadCubit, UploadState>(
        builder: (context, state) {
          final uploadCubit = context.read<UploadCubit>();

          // Only show if there are active uploads
          if (!uploadCubit.hasActiveUploads) {
            return const SizedBox.shrink();
          }

          // Get the first active task
          final activeTasks = uploadCubit.activeTasks;
          if (activeTasks.isEmpty) {
            return const SizedBox.shrink();
          }

          final task = activeTasks.first;
          final screenSize = MediaQuery.of(context).size;

          return Positioned(
            right: _position.dx,
            bottom: _position.dy,
            child: GestureDetector(
              onPanUpdate: (details) {
                setState(() {
                  // Update position based on drag
                  // Note: We're using right/bottom positioning, so we subtract the delta
                  _position = Offset(
                    (_position.dx - details.delta.dx).clamp(
                      0,
                      screenSize.width - 320,
                    ),
                    (_position.dy - details.delta.dy).clamp(
                      0,
                      screenSize.height - 200,
                    ),
                  );
                });
              },
              child: MouseRegion(
                cursor: SystemMouseCursors.move,
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: 320,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.primaryColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with status and drag handle
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                _buildStatusIcon(task.status),
                                const SizedBox(width: 8),
                                Text(
                                  _getStatusTitle(task.status),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.primaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                            // if (task.canBeCancelled)
                            //   IconButton(
                            //     icon: const Icon(Icons.close, size: 18),
                            //     padding: EdgeInsets.zero,
                            //     constraints: const BoxConstraints(),
                            //     onPressed: () {
                            //       context.read<UploadCubit>().removeTask(task.taskId);
                            //     },
                            //   ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Progress bar
                        if (task.isInProgress) ...[
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: task.progress,
                              backgroundColor: AppTheme.primaryColor
                                  .withOpacity(0.1),
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getProgressColor(task.status),
                              ),
                              minHeight: 6,
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        // File info
                        if (task.currentFileName != null &&
                            task.currentFileName!.isNotEmpty)
                          Text(
                            task.currentFileName!,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryTextColor.withOpacity(0.7),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),

                        // Progress text
                        if (task.isInProgress) ...[
                          Text(
                            '${task.currentFileIndex} of ${task.totalFiles} files • ${(task.progress * 100).toStringAsFixed(0)}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryTextColor.withOpacity(0.6),
                            ),
                          ),
                          // Show the same upload status text as in UploadDocumentScreen
                          if (task.uploadStatus != null &&
                              task.uploadStatus!.isNotEmpty)
                            if (task.uploadStatus == AppStrings.processing)
                              processingStatusText(task.uploadStatus!),
                          SizedBox(
                            height: task.uploadStatus == AppStrings.processing
                                ? 2
                                : 0,
                          ),
                        ],

                        // Error message
                        if (task.status == UploadTaskStatus.failed &&
                            task.errorMessage != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              task.errorMessage!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                        // Action buttons for failed uploads
                        if (task.status == UploadTaskStatus.failed)
                          Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                TextButton(
                                  onPressed: () {
                                    context.read<UploadCubit>().removeTask(
                                      task.taskId,
                                    );
                                  },
                                  child: const Text('Dismiss'),
                                ),
                                /* 
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: () {
                                // TODO: Implement retry functionality
                                // uploadService.retryUpload(task.taskId);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: const Text('Retry'),
                            ),*/
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showCompletionDialog(BuildContext context, UploadTask task) {
    if (task.salesId == null) return;

    // Show dialog after a short delay to ensure UI is ready
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => UploadCompletionDialog(
          salesId: task.salesId!,
          totalFiles: task.totalFiles,
          onNavigateToReview: () {
            Navigator.of(dialogContext).pop();

            // Navigate to review screen
            context.go(
              AppRoutes.saleReviewDoc.path,
              extra: {'salesId': task.salesId, 'isAgentEdit': true},
            );

            // Remove the task from the list
            locator<UploadService>().removeTask(task.taskId);
          },
          onDismiss: () {
            Navigator.of(dialogContext).pop();

            // Remove the task from the list
            locator<UploadService>().removeTask(task.taskId);

            // Notify sales screen to refresh after a short delay
            // to ensure the dialog is fully closed and context is available
            Future.delayed(const Duration(milliseconds: 100), () {
              SalesRefreshNotifier().notifyRefresh();
            });
          },
        ),
      );
    });
  }

  void _showFailureDialog(BuildContext context, UploadTask task) {
    // Show dialog after a short delay to ensure UI is ready
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) return;
      String errorString = task.errorMessage ?? AppStrings.errorOccured;
      errorString = errorString.endsWith('.') ? errorString : '$errorString.';
      errorString = '$errorString\n${AppStrings.uploadCleared}';

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => showAlertDialogue(
          context,
          title: AppStrings.uploadFailed,
          content: errorString,
          primaryColor: AppTheme.alertError,
          positiveButtonText: AppStrings.ok,
          negativeButtonText: '',
          alertType: SnackBarType.error,
        ),
      );
    });
  }

  Widget _buildStatusIcon(UploadTaskStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case UploadTaskStatus.queued:
        icon = Icons.schedule;
        color = Colors.orange;
        break;
      case UploadTaskStatus.uploading:
        icon = Icons.cloud_upload;
        color = AppTheme.primaryColor;
        break;
      case UploadTaskStatus.processing:
        icon = Icons.hourglass_empty;
        color = AppTheme.primaryColor;
        break;
      case UploadTaskStatus.completed:
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case UploadTaskStatus.failed:
        icon = Icons.error;
        color = Colors.red;
        break;
      case UploadTaskStatus.cancelled:
        icon = Icons.cancel;
        color = Colors.grey;
        break;
    }

    return Icon(icon, size: 18, color: color);
  }

  String _getStatusTitle(UploadTaskStatus status) {
    switch (status) {
      case UploadTaskStatus.queued:
        return 'Queued';
      case UploadTaskStatus.uploading:
        return 'Uploading';
      case UploadTaskStatus.processing:
        return 'Processing';
      case UploadTaskStatus.completed:
        return 'Upload Complete';
      case UploadTaskStatus.failed:
        return 'Upload Failed';
      case UploadTaskStatus.cancelled:
        return 'Upload Cancelled';
    }
  }

  Color _getProgressColor(UploadTaskStatus status) {
    switch (status) {
      case UploadTaskStatus.uploading:
        return AppTheme.primaryColor;
      case UploadTaskStatus.processing:
        return Colors.orange;
      case UploadTaskStatus.completed:
        return Colors.green;
      case UploadTaskStatus.failed:
        return Colors.red;
      default:
        return AppTheme.primaryColor;
    }
  }

  /// --- File upload status updating view ---
  Widget processingStatusText(String status) {
    return HookBuilder(
      builder: (context) {
        final messages = [
          AppStrings.processingInitiated,
          AppStrings.analyzingFile,
          AppStrings.optimizingUpload,
          AppStrings.almostDone,
        ];

        final index = useState(0);

        // cycle messages every 2 seconds
        useEffect(() {
          final timer = Timer.periodic(const Duration(seconds: 3), (_) {
            index.value = (index.value + 1) % messages.length;
          });
          return timer.cancel;
        }, []);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              messages[index.value],
              style: AppFonts.regularTextStyle(
                11,
                color: Colors.orange, // stage message color
              ),
            ),
          ],
        );
      },
    );
  }
}
