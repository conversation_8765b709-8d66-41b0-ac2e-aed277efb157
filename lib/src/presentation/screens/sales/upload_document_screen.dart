import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '/src/core/utils/success_snack_bar.dart';
import 'package:neorevv/src/domain/models/representing_type_model.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/enum/sale_doc_type.dart';
import '../../../core/navigation/navigation_utils.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/network/api_consts.dart';
import '../../../core/services/locator.dart';
import '../../../core/services/upload_service.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../../core/utils/helper.dart';
import '../../../domain/models/sales_details.dart';
import '../../../domain/models/upload_task_model.dart';
import '../../cubit/sales_details/sales_details_cubit.dart';
import '../../cubit/upload/upload_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/customDialogues/alert_dialogue.dart';
import '../../shared/components/elevated_button.dart';
import '../../shared/components/radio_btn.dart';

// Document info class

class UploadDocumentScreen extends HookWidget {
  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();
  final ValueNotifier<double> uploadProgress = ValueNotifier(0.0);

  // final ValueNotifier<PlatformFile?> docFile = ValueNotifier(null);
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);
  final ValueNotifier<String> uploadStatus = ValueNotifier('');
  final ValueNotifier<bool> isUploading = ValueNotifier(false);
  final ValueNotifier<List<SalesDocumentInfo>> requiredDocs = ValueNotifier([]);

  final ValueNotifier<List<SalesDocumentInfo>> addendumDocs = ValueNotifier([]);
  final ValueNotifier<String> currentUploadingFileName = ValueNotifier('');
  final ValueNotifier<int> currentFileIndex = ValueNotifier(0);
  final ValueNotifier<int> totalFilesCount = ValueNotifier(0);

  // Global notifier to track when any file changes
  final ValueNotifier<int> fileChangeNotifier = ValueNotifier(0);
  final ValueNotifier<List<RepresentingTypes>> representingList = ValueNotifier(
    [],
  );
  final Map<String, PlatformFile?> _fileCache = {};
  // Timer for progress simulation
  Timer? _progressTimer;

  // Current upload task ID
  String? _currentUploadTaskId;

  // Get buyer documents
  List<SalesDocumentInfo> _getBuyerDocuments() {
    return [
      SalesDocumentInfo(
        id: 'closing_document',
        type: SalesDocumentsType.CLOSING_DOCUMENT.name,
        title: AppStrings.closingDocument,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'purchase_agreement',
        type: SalesDocumentsType.PURCHASE_AGREEMENT.name,
        title: AppStrings.purchaseAgreement,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'agency_disclosure',
        type: SalesDocumentsType.AGENCY_DISCLOSURE.name,
        title: AppStrings.agencyDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'lead_based_paint_disclosure',
        type: SalesDocumentsType.LEAD_BASED_PAINT_DISCLOSURE.name,
        title: AppStrings.leadBasedPaintDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'seller_disclosure',
        type: SalesDocumentsType.SELLER_DISCLOSURE.name,
        title: AppStrings.sellerDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'exclusive_buyer_agency_agreement',
        type: SalesDocumentsType.EXCLUSIVE_BUYER_AGENCY_AGREEMENT.name,
        title: AppStrings.exclusiveBuyerAgencyAgreement,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
    ];
  }

  // Get seller documents
  List<SalesDocumentInfo> _getSellerDocuments() {
    return [
      SalesDocumentInfo(
        id: 'closing_document',
        type: SalesDocumentsType.CLOSING_DOCUMENT.name,
        title: AppStrings.closingDocument,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'purchase_agreement',
        type: SalesDocumentsType.PURCHASE_AGREEMENT.name,
        title: AppStrings.purchaseAgreement,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'agency_disclosure',
        type: SalesDocumentsType.AGENCY_DISCLOSURE.name,
        title: AppStrings.agencyDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'lead_based_paint_disclosure',
        type: SalesDocumentsType.LEAD_BASED_PAINT_DISCLOSURE.name,
        title: AppStrings.leadBasedPaintDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'seller_disclosure',
        type: SalesDocumentsType.SELLER_DISCLOSURE.name,
        title: AppStrings.sellerDisclosure,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
      SalesDocumentInfo(
        id: 'exclusive_right_to_sell',
        type: SalesDocumentsType.EXCLUSIVE_RIGHT_TO_SELL.name,
        title: 'Exclusive Right to Sell',
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);
    final representing = useState<RepresentingTypes?>(null);
    // Get documents based on representing
    final documents = useState<List<SalesDocumentInfo>>([]);
    final uploadStatus = context.watch<UploadCubit>().hasActiveUploads;
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fetchRepresentingTypes(context, representing);
      });
      return null;
    }, []);

    useEffect(() {
      if (documents.value.isEmpty) {
        documents.value = _getBuyerDocuments();
      }

      return;
    }, []);

    useEffect(() {
      isUploading.value = uploadStatus;
      return null;
    }, [uploadStatus]);

    // Update only the last document when representing changes
    useEffect(() {
      if (documents.value.isNotEmpty && representing.value != null) {
        final isBuyer = representing.value?.value?.toLowerCase() == 'buyer';
        final newLastDoc = isBuyer
            ? _getBuyerDocuments().last
            : _getSellerDocuments().last;

        // Replace only the last document (exclusive one)
        final updatedDocs = List<SalesDocumentInfo>.from(documents.value);
        updatedDocs[updatedDocs.length - 1] = newLastDoc;
        documents.value = updatedDocs;
      }
      return null;
    }, [representing.value]);

    final addendumSections = useState<List<SalesDocumentInfo>>([
      SalesDocumentInfo(
        id: 'addendum',
        type: SalesDocumentsType.ADDENDUMS.name,
        title: AppStrings.addendum,
        description: AppStrings.pdfFormatsOnly,
        allowedExtensions: APIConsts.allowedFileExtensions,
        isRequired: false,
      ),
    ]);

    requiredDocs.value = documents.value;

    useEffect(() {
      showFileUploadError.value = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_formKey.currentState != null) {
          _formKey.currentState!.reset();
        }
      });
      return null;
    }, [representing.value]);

    useEffect(() {
      addendumDocs.value = addendumSections.value;
      // Clear file and error state when navigating back
      return () {
        setFileToNull();
        showFileUploadError.value = false;
      };
    }, []);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isMobile ? double.infinity : 1000,
        ),
        margin: const EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.roundIconColor,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ValueListenableBuilder(
          valueListenable: isUploading,
          builder: (context, value, child) {
            return Stack(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _formHeader(context),
                    _formContent(isMobile, context, representing),
                  ],
                ),
                /*
                if (isUploading.value) ...[
                  Positioned.fill(
                    child: Container(
                      color: Colors.transparent,
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  ),
                ],
                */
              ],
            );
          },
        ),
      ),
      //     ),
      //   ],
      // ),
    );
  }

  Future<void> _fetchRepresentingTypes(
    BuildContext context,
    ValueNotifier<RepresentingTypes?> representing,
  ) async {
    final salesCubit = context.read<SalesDetailsCubit>();
    await salesCubit.fetchRepresentingTypes();

    if (salesCubit.state is SalesDetailsRepresentingLoaded) {
      final state = salesCubit.state as SalesDetailsRepresentingLoaded;
      representingList.value = state.representingTypes;
      if (representing.value == null && state.representingTypes.isNotEmpty) {
        representing.value = state.representingTypes.firstWhere(
          (type) => type.value?.toLowerCase() == 'buyer',
          orElse: () => state.representingTypes.first,
        );
      }
    }
  }

  /// --- HEADER (blue rounded top) ---
  Container _formHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 2,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Center(
        child: Text(
          AppStrings.uploadSalesDocument,
          style: AppFonts.semiBoldTextStyle(22, color: Colors.white),
        ),
      ),
    );
  }

  Widget _formContent(
    bool isMobile,
    BuildContext context,
    ValueNotifier<RepresentingTypes?> representing,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;

    return ValueListenableBuilder<List<SalesDocumentInfo>>(
      valueListenable: requiredDocs,
      builder: (context, docs, _) {
        return ValueListenableBuilder(
          valueListenable: addendumDocs,
          builder: (context, addendums, child) {
            final addendumsToUpload = addendumDocs.value
                .where((doc) => doc.file.value != null)
                .toList();
            final showProgress = addendumsToUpload.isEmpty
                ? requiredDocs.value.any((doc) => doc.file.value != null)
                : (requiredDocs.value.any((doc) => doc.file.value != null) &&
                      addendumsToUpload.any((doc) => doc.file.value != null));
            return ValueListenableBuilder<int>(
              valueListenable: fileChangeNotifier,
              builder: (context, _, __) {
                // Check if any files are selected (enable clear button when files are chosen)
                final hasAnyFiles =
                    requiredDocs.value.any((doc) => doc.file.value != null) ||
                    addendumDocs.value.any((doc) => doc.file.value != null);
                final fileNames =
                    requiredDocs.value
                        .where((doc) => doc.file.value != null)
                        .map((doc) => doc.file.value!.name)
                        .toList() +
                    addendumDocs.value
                        .where((doc) => doc.file.value != null)
                        .map((doc) => doc.file.value!.name)
                        .toList();
                return ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                    bottomLeft: Radius.circular(25),
                    bottomRight: Radius.circular(25),
                  ),
                  child: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(defaultPadding * 2),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          _buildRepresentingSection(representing),
                          const SizedBox(height: defaultPadding * 2),

                          /// Upload Field
                          LayoutBuilder(
                            builder: (context, constraints) {
                              final isWide = constraints.maxWidth > 600;
                              final itemWidth = isWide
                                  ? (constraints.maxWidth - 16) / 2
                                  : constraints.maxWidth;
                              return Wrap(
                                spacing: 16,
                                runSpacing: 16,
                                children: docs.map((document) {
                                  return Container(
                                    width: itemWidth,
                                    child: _buildUploadField(
                                      AppStrings.chooseFileOrDragDrop,
                                      AppStrings.pdfFormatInfo,
                                      // docFile,
                                      APIConsts.allowedFileExtensions,
                                      document,
                                    ),
                                  );
                                }).toList(),
                              );
                            },
                          ),

                          const SizedBox(height: defaultPadding * 2),

                          _buildAddendumsSection(
                            context,
                            isMobile,
                            screenWidth,
                            addendums,
                          ),
                          const SizedBox(height: defaultPadding * 2),

                          /// Show Progress Section only when file selected
                          if (isUploading.value) ...[
                            _fileUploadRow(fileNames.join(', ')),
                            const SizedBox(height: 20),
                          ],

                          /// Footer Buttons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              AppButton(
                                label: AppStrings.clear,
                                backgroundColor: AppTheme.scaffoldBgColor,
                                foregroundColor: AppTheme.primaryTextColor,
                                borderRadius: 25,
                                padding: EdgeInsets.symmetric(
                                  horizontal: defaultPadding * 2,
                                  vertical: defaultPadding * 1.2,
                                ),
                                useMinSize: true,
                                onPressed: isUploading.value
                                    ? null
                                    : !hasAnyFiles
                                    ? null
                                    : () async {
                                        final confirmed =
                                            await showDialog<bool>(
                                              context: context,
                                              builder: (ctx) =>
                                                  showAlertDialogue(
                                                    ctx,
                                                    title: AppStrings.clear,
                                                    content: AppStrings
                                                        .clearFileSelection,
                                                    primaryColor: AppTheme
                                                        .primaryBlueColor,
                                                    positiveButtonText:
                                                        AppStrings.yes,
                                                    negativeButtonText:
                                                        AppStrings.no,
                                                  ),
                                            );

                                        if (confirmed == true) {
                                          setFileToNull();
                                          showFileUploadError.value = false;
                                        }
                                      },
                              ),
                              const SizedBox(width: defaultPadding),
                              AppButton(
                                label: AppStrings.uploadAndContinue,
                                backgroundColor: AppTheme.roundIconColor,
                                foregroundColor: Colors.white,
                                borderRadius: 25,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: defaultPadding * 2,
                                  vertical: defaultPadding * 1.2,
                                ),
                                useMinSize: true,
                                onPressed: isUploading.value
                                    ? null
                                    : () {
                                        final missingDocuments = requiredDocs
                                            .value
                                            .where(
                                              (doc) =>
                                                  doc.isRequired &&
                                                  doc.file.value == null,
                                            )
                                            .toList();

                                        if (missingDocuments.isNotEmpty) {
                                          showFileUploadError.value = true;
                                          AppSnackBar.showSnackBar(
                                            context,
                                            pleaseUploadRequiredDocuments,
                                            SnackBarType.error,
                                          );
                                        } else {
                                          showFileUploadError.value = false;
                                          _uploadDocument(
                                            context,
                                            representing,
                                          );
                                        }
                                      },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  bool isDocumentUploadValid() {
    final missingDocuments = requiredDocs.value
        .where((doc) => doc.isRequired && doc.file.value == null)
        .toList();
    return missingDocuments.isEmpty;
  }

  // Helper method to notify file changes
  void _notifyFileChange() {
    fileChangeNotifier.value = fileChangeNotifier.value + 1;
  }

  void setFileToNull() {
    [
      requiredDocs.value,
      addendumDocs.value,
    ].expand((docs) => docs).forEach((doc) => doc.file.value = null);
    _fileCache.clear();
    _notifyFileChange();
  }

  void clearClosingAndExclusiveFiles() {
    // Only clear closing document and the last document (exclusive one)
    for (var doc in requiredDocs.value) {
      if (doc.id == 'closing_document') {
        doc.file.value = null;
        _fileCache.remove(doc.id);
      }
    }

    // Clear the last document (exclusive document) - it will be replaced
    if (requiredDocs.value.isNotEmpty) {
      final lastDoc = requiredDocs.value.last;
      lastDoc.file.value = null;
      _fileCache.remove(lastDoc.id);
    }

    _notifyFileChange();
  }

  Future<void> _showAlert(
    BuildContext context,
    ValueNotifier<RepresentingTypes?> currentRepresenting,
    RepresentingTypes newRepresenting,
  ) async {
    // Only show confirmation if Closing or Exclusive docs are uploaded.
    final hasClosingDoc = requiredDocs.value.any(
      (doc) => doc.id == 'closing_document' && doc.file.value != null,
    );

    final hasExclusiveBuyerDoc = requiredDocs.value.any(
      (doc) =>
          doc.id == 'exclusive_buyer_agency_agreement' &&
          doc.file.value != null,
    );

    final hasExclusiveSellerDoc = requiredDocs.value.any(
      (doc) => doc.id == 'exclusive_right_to_sell' && doc.file.value != null,
    );

    final shouldShowAlert =
        hasClosingDoc || hasExclusiveBuyerDoc || hasExclusiveSellerDoc;

    // If none of the critical documents are uploaded, switch immediately.
    if (!shouldShowAlert) {
      currentRepresenting.value = newRepresenting;
      return;
    }

    // Determine the unique document for each type
    final currentUniqueDoc =
        currentRepresenting.value?.value?.toLowerCase() == 'buyer'
        ? 'Exclusive Buyer Agency Agreement'
        : 'Exclusive Right to Sell';

    // Check if there are common documents uploaded (documents other than closing and exclusive)
    final commonDocIds = [
      'purchase_agreement',
      'agency_disclosure',
      'lead_based_paint_disclosure',
      'seller_disclosure',
    ];

    final hasCommonDocsUploaded = requiredDocs.value.any(
      (doc) => commonDocIds.contains(doc.id) && doc.file.value != null,
    );

    // Create appropriate warning message
    String content;

    if (hasClosingDoc && (hasExclusiveBuyerDoc || hasExclusiveSellerDoc)) {
      // Case 1: Both Closing Document and Exclusive document uploaded
      content =
          'Switching from ${currentRepresenting.value?.value ?? ''} to ${newRepresenting.value ?? ''} will clear the following documents:\n\n'
          '• Closing Document\n'
          '• $currentUniqueDoc\n\n';

      if (hasCommonDocsUploaded) {
        content += 'All other uploaded documents will be retained.\n\n';
      }

      content += 'Do you want to continue?';
    } else if (hasClosingDoc) {
      // Case 2: Only Closing Document uploaded
      content =
          'Switching from ${currentRepresenting.value?.value ?? ''} to ${newRepresenting.value ?? ''} will clear the "Closing Document".\n\n';

      if (hasCommonDocsUploaded) {
        content += 'All other uploaded documents will be retained.\n\n';
      }

      content += 'Do you want to continue?';
    } else if (hasExclusiveBuyerDoc || hasExclusiveSellerDoc) {
      // Case 3: Only Exclusive document uploaded
      content =
          'Switching from ${currentRepresenting.value?.value ?? ''} to ${newRepresenting.value ?? ''} will clear the "$currentUniqueDoc".\n\n';

      if (hasCommonDocsUploaded) {
        content += 'All other uploaded documents will be retained.\n\n';
      }

      content += 'Do you want to continue?';
    } else {
      // Fallback (shouldn't reach here because of earlier guard)
      content =
          'You are switching from ${currentRepresenting.value?.value ?? ''} to ${newRepresenting.value ?? ''}.\n\n'
          'Do you want to continue?';
    }

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final size = MediaQuery.of(context).size;
        return showAlertDialogue(
          context,
          title: 'Change Representing Type',
          content: content,
          width: size.width <= 500 ? 350 : 500,
          titlePaddingRatio: 1.5,
          primaryColor: AppTheme.primaryBlueColor,
          contentTextAlign: TextAlign.start,
          positiveButtonText: AppStrings.yes,
          negativeButtonText: AppStrings.no,
          onPositivePressed: () {
            Navigator.of(context).pop(true);
          },
          onNegativePressed: () {
            Navigator.of(context).pop(false);
          },
        );
      },
    );

    if (confirmed == true) {
      clearClosingAndExclusiveFiles();
      showFileUploadError.value = false;
      if (_formKey.currentState != null) {
        _formKey.currentState!.reset();
      }
      // Update the representing value only after confirmation
      currentRepresenting.value = newRepresenting;
    }
  }

  Widget _buildRepresentingSection(
    ValueNotifier<RepresentingTypes?> representing,
  ) {
    return ValueListenableBuilder<List<RepresentingTypes>>(
      valueListenable: representingList,
      builder: (context, types, _) {
        if (types.isEmpty) {
          return Center(child: CircularProgressIndicator());
        }

        return Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                AppStrings.representing,
                style: AppFonts.regularTextStyle(15, color: AppTheme.black),
              ),
              const SizedBox(width: defaultPadding),
              if (types.isNotEmpty)
                ...types.map((type) {
                  return Padding(
                    padding: const EdgeInsets.only(right: defaultPadding),
                    child: RadioOption<RepresentingTypes>(
                      value: type,
                      groupValue: representing.value ?? types.first,
                      // onChanged: (value) => representing.value = value!,
                      onChanged: (value) {
                        // Don't allow changes during upload
                        if (isUploading.value) return;

                        // If selecting the same value, do nothing
                        if (value == representing.value) return;

                        // Show confirmation dialog before switching
                        _showAlert(context, representing, value!);
                      },
                      label:
                          type.value ??
                          '', // Display the value (e.g., "Buyer", "Seller")
                      enabled: !isUploading.value,
                    ),
                  );
                }).toList(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddendumsSection(
    BuildContext context,
    bool isMobile,
    double width,
    List<SalesDocumentInfo> addendums,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.addendumIfAny,
          style: AppFonts.regularTextStyle(
            13,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: defaultPadding),

        ...addendums.asMap().entries.map((entry) {
          final index = entry.key;
          final addendum = entry.value;
          return Container(
            width: width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      bottom: defaultPadding,
                      right: defaultPadding,
                    ),
                    child: SizedBox(
                      child: _buildUploadField(
                        AppStrings.chooseFileOrDragDrop,
                        AppStrings.pdfFormatInfo,
                        // docFile,
                        APIConsts.allowedFileExtensions,
                        addendum,
                        showTitle: index != 0,
                      ),
                    ),
                  ),
                ),

                IconButton(
                  padding: EdgeInsets.zero,
                  icon: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.cancelBgColor,
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      index == 0 ? Icons.add : Icons.delete_outline,
                      color: isUploading.value
                          ? Colors.black.withValues(alpha: 0.5)
                          : Colors.black,
                      size: 20,
                    ),
                  ),
                  onPressed: isUploading.value
                      ? null
                      : index == 0
                      ? () => _addNewAddendum(addendumDocs)
                      : () => _removeAddendum(addendumDocs, index),
                ),

                //          _buildDocumentCard(
                //   context,
                //   addendum,
                //   isAddendum: true,
                //   addendumIndex: index,
                //   onRemove: () => _removeAddendum(addendumSections, index),
                // ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    //ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
    SalesDocumentInfo documentInfo, {
    bool showTitle = true,
  }) {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: documentInfo.file,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);
            bool validField = file != null;

            //documentInfo.showError = hasError && !validField;
            bool showErrorForField =
                hasError && documentInfo.isRequired && !validField;
            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (validField) {
              borderColor = Colors.green.shade200;
            } else if (showErrorForField) {
              borderColor = Colors.red;
              showErrorForField = true;
            }

            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showTitle)
                  RichText(
                    text: TextSpan(
                      text: documentInfo.title,
                      style: AppFonts.regularTextStyle(
                        13,
                        color: AppTheme.primaryTextColor,
                      ),
                      children: documentInfo.isRequired
                          ? [
                              TextSpan(
                                text: ' *',
                                style: AppFonts.regularTextStyle(
                                  13,
                                  color: AppTheme.textFieldMandatoryColor,
                                ),
                              ),
                            ]
                          : [],
                    ),
                  ),
                SizedBox(height: showTitle ? defaultPadding / 2 : 0),
                Container(
                  width: double.infinity,
                  child: buildDottedBorderContainerWithRadius(
                    borderRadius: 25.0,
                    borderColor: borderColor,
                    child: Container(
                      // width: double.infinity,
                      height: isSmallMobile ? 100 : 120,

                      decoration: BoxDecoration(
                        color: validField
                            ? Colors.green.shade50
                            : showErrorForField
                            ? Colors.red.shade50
                            : AppTheme.docUploadBgColor,
                        borderRadius: BorderRadius.circular(25),
                        border: validField
                            ? Border.all(color: Colors.green.shade200)
                            : showErrorForField
                            ? Border.all(color: Colors.red.shade200)
                            : null,
                      ),
                      child: validField
                          ? Stack(
                              children: [
                                Padding(
                                  padding: EdgeInsets.all(
                                    isSmallMobile
                                        ? defaultPadding / 2
                                        : defaultPadding,
                                  ),
                                  child: Center(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.check_circle,
                                          color: Colors.green,
                                          size: isSmallMobile ? 16 : 20,
                                        ),
                                        SizedBox(width: isSmallMobile ? 8 : 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                file.name,
                                                style: AppFonts.mediumTextStyle(
                                                  isSmallMobile ? 12 : 14,
                                                  color: Colors.green.shade700,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                                textAlign: TextAlign.center,
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                formatFileSize(file.size),
                                                style:
                                                    AppFonts.regularTextStyle(
                                                      isSmallMobile ? 10 : 12,
                                                      color:
                                                          Colors.green.shade600,
                                                    ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Positioned(
                                  top: defaultPadding / 4,
                                  right: defaultPadding / 4,
                                  child: IconButton(
                                    onPressed: isUploading.value
                                        ? null
                                        : () {
                                            documentInfo.file.value = null;
                                            // Remove from cache when file is deleted
                                            _fileCache.remove(documentInfo.id);
                                            // Reset validation error when file is removed
                                            _handleFileValidation();
                                            _notifyFileChange();
                                          },
                                    padding: EdgeInsets.zero,
                                    icon: Icon(
                                      Icons.close,
                                      color: isUploading.value
                                          ? Colors.red.withValues(alpha: 0.5)
                                          : Colors.red,
                                      size: isSmallMobile ? 16 : 20,
                                    ),
                                    tooltip: isUploading.value
                                        ? ''
                                        : 'Remove file',
                                    splashRadius: isSmallMobile ? 18 : 22,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: isUploading.value
                                      ? null
                                      : () => _showFilePickerOptions(
                                          context,
                                          // fileNotifier,
                                          documentInfo.file,
                                          allowedExtensions,
                                        ),
                                  icon: Image.asset(
                                    '$iconAssetpath/upload.png',
                                    height: isSmallMobile ? 14 : 16,
                                    width: isSmallMobile ? 14 : 16,
                                  ),
                                  label: Text(
                                    AppStrings.browseFile,
                                    style: AppFonts.mediumTextStyle(
                                      isSmallMobile ? 12 : 14,
                                      color: AppTheme.black,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: AppTheme.primaryTextColor,
                                    elevation: 0,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: isSmallMobile ? 8 : 12,
                                      vertical: isSmallMobile ? 4 : 8,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side: BorderSide(
                                        color: AppTheme.borderColor,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: isSmallMobile ? 4 : 8),
                                Text(
                                  hintText,
                                  textAlign: TextAlign.center,
                                  style: AppFonts.mediumTextStyle(
                                    isSmallMobile ? 10 : 12,
                                    color: AppTheme.black,
                                  ),
                                ),
                                Text(
                                  formatText,
                                  textAlign: TextAlign.center,
                                  style: AppFonts.regularTextStyle(
                                    isSmallMobile ? 9 : 12,
                                    color: AppTheme.ternaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// --- File Upload Row (with progress bar & remove button) ---
  Widget processingStatusText(double progress, String status, int size) {
    return HookBuilder(
      builder: (context) {
        final messages = [
          AppStrings.processingInitiated,
          AppStrings.analyzingFile,
          AppStrings.optimizingUpload,
          AppStrings.almostDone,
        ];

        final index = useState(0);

        // cycle messages every 2 seconds
        useEffect(() {
          final timer = Timer.periodic(const Duration(seconds: 3), (_) {
            index.value = (index.value + 1) % messages.length;
          });
          return timer.cancel;
        }, []);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              messages[index.value],
              style: AppFonts.regularTextStyle(
                11,
                color: Colors.orange, // stage message color
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _fileUploadRow(String fileNames) {
    return ValueListenableBuilder<double>(
      valueListenable: uploadProgress,
      builder: (context, progress, _) {
        return ValueListenableBuilder<String>(
          valueListenable: uploadStatus,
          builder: (context, status, _) {
            final totalCount =
                (requiredDocs.value +
                        (addendumDocs.value
                            .where((doc) => doc.file.value != null)
                            .toList()))
                    .length;

            final totalFileSize =
                requiredDocs.value.fold(
                  0,
                  (previousValue, element) =>
                      previousValue + (element.file.value?.size ?? 0),
                ) +
                addendumDocs.value.fold(
                  0,
                  (previousValue, element) =>
                      previousValue + (element.file.value?.size ?? 0),
                );
            final uploadingDocSize = formatFileSize(totalFileSize.toInt());
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.picture_as_pdf, color: Colors.red, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Uploading $totalCount files · $uploadingDocSize',
                          style: AppFonts.mediumTextStyle(
                            13,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey[300],
                          color:
                              status == AppStrings.uploadCompletedSuccessfully
                              ? Colors.green
                              : status == AppStrings.uploadFailed
                              ? Colors.red
                              : AppTheme.roundIconColor,
                          minHeight: 4,
                        ),
                        const SizedBox(height: 2),
                        if (status == AppStrings.processing)
                          processingStatusText(
                            progress,
                            status,
                            totalFileSize.toInt(),
                          ),
                        SizedBox(
                          height: status == AppStrings.processing ? 2 : 0,
                        ),

                        Text(
                          _getUploadStatusText(context, progress, status),
                          style: AppFonts.regularTextStyle(
                            11,
                            color: _getStatusColor(status),
                          ),
                        ),
                      ],
                    ),
                  ),

                  /// progress close button
                  if (progress < 1.0 && status != AppStrings.uploading)
                    IconButton(
                      onPressed: () => _showCancelUploadDialog(context),
                      icon: const Icon(Icons.close, color: Colors.grey),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _getUploadStatusText(
    BuildContext context,
    double progress,
    String status,
  ) {
    final uploadCubit = context.read<UploadCubit>();
    final activeTasks = uploadCubit.activeTasks;
    if (activeTasks.isNotEmpty) {
      final task = activeTasks.first;
      currentUploadingFileName.value = task.currentFileName ?? '';
    }
    // Get the current file name
    final currentFileName = currentUploadingFileName.value.isNotEmpty
        ? currentUploadingFileName.value
        : AppStrings.file;

    // Get the count string
    final countStr = totalFilesCount.value > 0
        ? '(${currentFileIndex.value}/${totalFilesCount.value})'
        : '';

    if (status == AppStrings.uploading ||
        status == AppStrings.preparingUpload ||
        status == AppStrings.uploadingDocument ||
        status == AppStrings.processing ||
        status == AppStrings.finalizingUpload) {
      return '$currentFileName ${(progress * 100).toInt()}% $countStr';
    } else if (status == AppStrings.uploadCompletedSuccessfully) {
      return '$currentFileName $countStr';
    } else if (status == AppStrings.uploadFailed) {
      return '$currentFileName - ${AppStrings.uploadFailed} $countStr';
    } else if (status == AppStrings.uploadCompleteProcessing) {
      return '$currentFileName - ${AppStrings.uploadCompleteProcessing} $countStr';
    } else {
      return currentFileName.isNotEmpty
          ? '$currentFileName $countStr'
          : (status.isEmpty
                ? '${AppStrings.readyToUpload} $countStr'
                : '$status $countStr');
    }
  }

  Color _getStatusColor(String status) {
    if (status == AppStrings.uploading ||
        status == '' ||
        status == AppStrings.preparingUpload ||
        status == AppStrings.uploadingDocument ||
        status == AppStrings.processing ||
        status == AppStrings.uploadCompleteProcessing ||
        status == AppStrings.finalizingUpload) {
      return Colors.grey[600]!;
    } else if (status == AppStrings.uploadCompletedSuccessfully) {
      return Colors.green;
    } else if (status == AppStrings.uploadFailed) {
      return Colors.red;
    } else {
      return Colors.grey[600]!;
    }
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    uploadProgress.value = 0.0; // Reset progress on error

    uploadStatus.value = '';
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        _handleFileValidation();
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        _handleFileValidation();
      }
    } catch (e) {
      AppSnackBar.showSnackBar(
        context,
        '${AppStrings.failedToCaptureImage}: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      debugPrint('File picker result: ${result?.files.length ?? 0} files');

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;
        debugPrint(
          'Selected file: ${pickedFile.name}, size: ${pickedFile.size}, extension: ${pickedFile.extension}',
        );
        // Check if file size exceeds 25MB
        if (pickedFile.size > maxFileSizeInBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileSizeExceeded,
            SnackBarType.error,
          );
          return;
        }
        if (pickedFile.size <= 0) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileEmpty,
            SnackBarType.error,
          );
          return;
        }
        // try {
        //   // File? file = await convertPlatformFileToFile(pickedFile);
        //   final bytes = pickedFile?.bytes;
        //   if (bytes != null) await decodeImageFromList(bytes); // validate
        //   fileNotifier.value = PlatformFile(
        //     name: pickedFile.name,
        //     path: pickedFile.path,
        //     size: pickedFile.size,
        //   );
        // } catch (e) {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(
        //       content: Text(e.toString()),
        //       backgroundColor: Colors.red,
        //       duration: Duration(seconds: 3),
        //     ),
        //   );
        // }

        if (pickedFile.extension != 'pdf') {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.selectPdfFile,
            SnackBarType.error,
            showCloseButton: false,
            isTimerNeeded: true,
          );
          return;
        }

        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = pickedFile.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            AppSnackBar.showSnackBar(
              context,
              '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
              SnackBarType.error,
              showCloseButton: false,
              isTimerNeeded: true,
            );

            return;
          }
        }

        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = pickedFile.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            AppSnackBar.showSnackBar(
              context,
              '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
              SnackBarType.error,
              showCloseButton: false,
              isTimerNeeded: true,
            );

            return;
          }
        }
        // PDF integrity check: detect corrupted PDFs by validating header/footer
        try {
          if (pickedFile.extension?.toLowerCase() == 'pdf') {
            final isCorrupted = await isPdfCorrupted(pickedFile);
            if (isCorrupted) {
              AppSnackBar.showSnackBar(
                context,
                AppStrings.selectedPdfAppearsToBeCorruptedOrInvalid,
                SnackBarType.error,
                showCloseButton: false,
                isTimerNeeded: true,
              );

              return;
            }
          }
        } catch (e, st) {
          debugPrint('PDF validation error (non-fatal): $e\n$st');
          AppSnackBar.showSnackBar(
            context,
            AppStrings.couldNotFullyValidatePdf,
            SnackBarType.error,
            showCloseButton: false,
            isTimerNeeded: true,
          );
        }

        // Validate file based on platform
        if (kIsWeb) {
          if (pickedFile.bytes != null) {
            fileNotifier.value = pickedFile;
            // Clear validation error when file is selected
            _handleFileValidation();
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (pickedFile.path != null) {
            fileNotifier.value = pickedFile;
            // Clear validation error when file is selected
            _handleFileValidation();
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('$errorPickingFile: $e');
      // Show user-friendly error message
      AppSnackBar.showSnackBar(
        context,
        '$failedToOpenFilePicker: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: false,
        isTimerNeeded: true,
      );
    }
  }

  _handleFileValidation() {
    final hasInvalidFields = requiredDocs.value.any(
      (doc) => doc.isRequired && doc.file.value == null,
    );
    if (showFileUploadError.value) {
      showFileUploadError.value = hasInvalidFields ? true : false;
    }
    _notifyFileChange();
  }

  /// Simulates gradual progress during upload
  void _startProgressSimulation({UploadCubit? uploadCubit, String? taskId}) {
    _progressTimer?.cancel(); // Cancel any existing timer

    const totalDuration = Duration(seconds: 8); // Total expected upload time
    const updateInterval = Duration(milliseconds: 100); // Update every 100ms
    final totalUpdates =
        totalDuration.inMilliseconds / updateInterval.inMilliseconds;

    int currentUpdate = 0;

    _progressTimer = Timer.periodic(updateInterval, (timer) {
      if (!isUploading.value) {
        timer.cancel();
        return;
      }

      currentUpdate++;

      // Create a realistic progress curve (slower at start and end, faster in middle)
      double normalizedProgress = currentUpdate / totalUpdates;

      // Use a sigmoid-like curve for more realistic progress
      double simulatedProgress;
      if (normalizedProgress < 0.1) {
        // Slow start (0-10%)
        simulatedProgress = normalizedProgress * 0.5; // 0-5%
      } else if (normalizedProgress < 0.8) {
        // Fast middle section (10-80% -> 5-85%)
        simulatedProgress = 0.05 + (normalizedProgress - 0.1) * 0.8 / 0.7;
      } else {
        // Slow end (80-100% -> 85-95%)
        simulatedProgress = 0.85 + (normalizedProgress - 0.8) * 0.1 / 0.2;
      }

      // Don't exceed 95% until actual upload completes
      simulatedProgress = simulatedProgress.clamp(0.0, 0.95);

      // Only update if simulated progress is higher than current
      if (simulatedProgress > uploadProgress.value) {
        uploadProgress.value = simulatedProgress;

        // Update status messages based on progress
        String newStatus;
        if (simulatedProgress < 0.3) {
          newStatus = AppStrings.preparingUpload;
        } else if (simulatedProgress < 0.7) {
          newStatus = AppStrings.uploadingDocument;
        } else {
          newStatus = AppStrings.processing;
        }

        uploadStatus.value = newStatus;

        // Also update the global UploadCubit if provided
        if (uploadCubit != null && taskId != null) {
          // Update progress
          uploadCubit.updateTaskProgress(
            taskId: taskId,
            progress: simulatedProgress,
            currentFileName: currentUploadingFileName.value.isNotEmpty
                ? currentUploadingFileName.value
                : null,
            currentFileIndex: currentFileIndex.value,
            uploadStatus: uploadStatus.value,
          );

          // Update status in UploadCubit based on progress
          if (simulatedProgress >= 0.7 && newStatus == AppStrings.processing) {
            uploadCubit.markTaskAsProcessing(taskId);
          }
        }
      }

      // Stop simulation at 95% and let actual upload progress take over
      if (simulatedProgress >= 0.95) {
        timer.cancel();
      }
    });
  }

  /// Stops the progress simulation
  void _stopProgressSimulation() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  void _uploadDocument(
    BuildContext context,
    ValueNotifier<RepresentingTypes?> representing,
  ) async {
    if (requiredDocs.value.any(
      (doc) => doc.isRequired && doc.file.value == null,
    )) {
      showFileUploadError.value = true;
      return;
    }

    final addendumsToUpload = addendumDocs.value
        .where((doc) => doc.file.value != null)
        .toList();

    final mandatoryFiles = requiredDocs.value
        .where((doc) => doc.file.value != null)
        .toList();
    final allFilesToUpload = mandatoryFiles + addendumsToUpload;

    totalFilesCount.value = allFilesToUpload.length;
    currentFileIndex.value = 0;

    isUploading.value = true;
    uploadProgress.value = 0.0;
    uploadStatus.value = AppStrings.uploading;

    final user = context.read<UserCubit>().state.user;
    final uploadService = locator<UploadService>();
    final uploadCubit = context.read<UploadCubit>();

    try {
      // Convert SalesDocumentInfo to UploadFileInfo
      final uploadFiles = allFilesToUpload.map((doc) {
        return UploadFileInfo(
          id: doc.id,
          type: doc.type,
          title: doc.title,
          file: doc.file.value!,
        );
      }).toList();

      // Queue the upload using the service
      final taskId = await uploadService.queueUpload(
        userId: user?.userId ?? '',
        categoryType: APIConsts.salesCategoryType,
        representingId: representing.value?.id ?? "",
        files: uploadFiles,
        originatingRoute: AppRoutes.uploadDocument.path,
        progress: uploadProgress.value,
      );

      // Store the task ID for cancellation
      _currentUploadTaskId = taskId;

      // Start progress simulation with global state sync
      _startProgressSimulation(uploadCubit: uploadCubit, taskId: taskId);

      // Listen to upload progress
      StreamSubscription? subscription;
      subscription = uploadCubit.stream.listen((state) async {
        if (state is UploadTaskUpdated) {
          final task = state.task;
          if (task.taskId == taskId) {
            // Update local UI state from global state
            uploadProgress.value = task.progress;
            currentFileIndex.value = task.currentFileIndex;
            totalFilesCount.value = task.totalFiles;
            currentUploadingFileName.value = task.currentFileName ?? '';

            // Update status based on task status from UploadCubit
            if (task.status == UploadTaskStatus.uploading) {
              uploadStatus.value = AppStrings.uploadingDocument;
            } else if (task.status == UploadTaskStatus.processing) {
              uploadStatus.value = AppStrings.processing;
            } else if (task.status == UploadTaskStatus.completed) {
              uploadStatus.value = AppStrings.uploadCompletedSuccessfully;
              uploadProgress.value = 1.0;

              // Only auto-navigate if user is still on upload screen
              if (!task.userNavigatedAway) {
                // Navigate to review screen
                final addendumCount = addendumsToUpload.length;
                final mandatoryCount = mandatoryFiles.length;

                String successMessage =
                    '$mandatoryCount document${mandatoryCount != 1 ? 's' : ''}';
                if (addendumCount > 0) {
                  successMessage +=
                      ' and $addendumCount addendum${addendumCount != 1 ? 's' : ''}';
                }
                successMessage += AppStrings.uploadSuccessfully;

                await SuccessSnackBar.showSnackBar(
                  context,
                  successMessage,
                  SnackBarType.success,
                  timeout: const Duration(seconds: 2),
                );

                isUploading.value = false;
                setFileToNull();
                _stopProgressSimulation();

                // Navigate to review screen
                // safeGo(
                //   context,
                //   AppRoutes.saleReviewDoc.path,
                //   extra: {'salesId': task.salesId, 'isAgentEdit': true},
                // );
              } else {
                // User navigated away, just update UI state
                isUploading.value = false;
                setFileToNull();
              }

              subscription?.cancel();
            } else if (task.status == UploadTaskStatus.failed) {
              uploadStatus.value = AppStrings.uploadFailed;
              isUploading.value = false;

              AppSnackBar.showSnackBar(
                context,
                task.errorMessage ?? AppStrings.errorOccured,
                SnackBarType.error,
                showCloseButton: true,
                isTimerNeeded: false,
              );

              subscription?.cancel();
            } else if (task.status == UploadTaskStatus.cancelled) {
              uploadStatus.value = AppStrings.uploadCancelled;
              isUploading.value = false;
              subscription?.cancel();
            }
          }
        }
      });

      // Show success message that upload has been queued
      AppSnackBar.showSnackBar(
        context,
        'Upload started. You can navigate away and it will continue in the background.',
        SnackBarType.info,
        showCloseButton: true,
        isTimerNeeded: false,
      );
    } catch (e) {
      isUploading.value = false;
      uploadProgress.value = 0.0;
      uploadStatus.value = AppStrings.uploadFailed;

      await AppSnackBar.showSnackBar(
        context,
        'Failed to start upload: ${e.toString()}',
        SnackBarType.error,
        showCloseButton: true,
        isTimerNeeded: false,
      );

      currentUploadingFileName.value = '';
      currentFileIndex.value = 0;
      totalFilesCount.value = 0;
    }
  }

  void _addNewAddendum(
    ValueNotifier<List<SalesDocumentInfo>> addendumSections,
  ) {
    final newAddendum = SalesDocumentInfo(
      id: 'addendum_${addendumSections.value.length + 1}',
      type: SalesDocumentsType.ADDENDUMS.name,
      title: AppStrings.addendum,
      description: AppStrings.pdfFormatsOnly,
      allowedExtensions: APIConsts.allowedFileExtensions,
      isRequired: false,
    );

    addendumSections.value = [...addendumSections.value, newAddendum];
  }

  void _removeAddendum(
    ValueNotifier<List<SalesDocumentInfo>> addendumSections,
    int index,
  ) {
    final updatedList = List<SalesDocumentInfo>.from(addendumSections.value);
    updatedList[index].dispose();
    updatedList.removeAt(index);
    addendumSections.value = updatedList;
  }

  /// Show cancel upload confirmation dialog
  void _showCancelUploadDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return showAlertDialogue(
          context,
          title: AppStrings.cancelUpload,
          content: AppStrings.cancelUploadConfirmation,
          primaryColor: AppTheme.primaryBlueColor,
          positiveButtonText: AppStrings.yes,
          negativeButtonText: AppStrings.no,
          onPositivePressed: () {
            Navigator.of(context).pop();
            _cancelUpload();
          },
          onNegativePressed: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// Cancel the upload process
  void _cancelUpload() {
    if (_currentUploadTaskId != null) {
      final uploadService = locator<UploadService>();
      uploadService.cancelUpload(_currentUploadTaskId!);

      debugPrint('Cancelling upload task: $_currentUploadTaskId');

      // Reset upload state
      isUploading.value = false;
      uploadProgress.value = 0.0;
      uploadStatus.value = AppStrings.uploadCancelled;

      // Clear the task ID
      _currentUploadTaskId = null;
    } else {
      debugPrint('No active upload task to cancel');
    }

    // Show cancellation message
    // Note: We don't show snackbar here as the user intentionally cancelled
  }
}
